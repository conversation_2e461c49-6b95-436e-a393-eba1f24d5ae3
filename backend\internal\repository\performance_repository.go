package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type PerformanceIndicatorRepository struct {
	db *gorm.DB
}

func NewPerformanceIndicatorRepository(db *gorm.DB) *PerformanceIndicatorRepository {
	return &PerformanceIndicatorRepository{db: db}
}

func (r *PerformanceIndicatorRepository) Create(indicator *model.PerformanceIndicator) error {
	return r.db.Create(indicator).Error
}

func (r *PerformanceIndicatorRepository) GetByID(id uint) (*model.PerformanceIndicator, error) {
	var indicator model.PerformanceIndicator
	err := r.db.Preload("PerformanceData").First(&indicator, id).Error
	if err != nil {
		return nil, err
	}
	return &indicator, nil
}

func (r *PerformanceIndicatorRepository) Update(indicator *model.PerformanceIndicator) error {
	return r.db.Save(indicator).Error
}

func (r *PerformanceIndicatorRepository) Delete(id uint) error {
	return r.db.Delete(&model.PerformanceIndicator{}, id).Error
}

func (r *PerformanceIndicatorRepository) List(offset, limit int) ([]model.PerformanceIndicator, int64, error) {
	var indicators []model.PerformanceIndicator
	var total int64

	err := r.db.Model(&model.PerformanceIndicator{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Offset(offset).Limit(limit).Find(&indicators).Error
	if err != nil {
		return nil, 0, err
	}

	return indicators, total, nil
}
