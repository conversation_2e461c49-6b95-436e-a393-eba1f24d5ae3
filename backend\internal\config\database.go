package config

import (
	"fmt"
	"hospital-management/internal/model"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func InitDB(cfg *Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=Asia/Shanghai",
		cfg.Database.Host,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.DBName,
		cfg.Database.Port,
		cfg.Database.SSLMode,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	return db, nil
}

func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		// 基础模型
		&model.User{},
		&model.Department{},
		&model.Role{},
		&model.UserRole{},
		&model.File{},

		// 审批流模型
		&model.ApprovalFlow{},
		&model.ApprovalNode{},

		// 预算管理模型
		&model.Budget{},
		&model.BudgetItem{},
		&model.BudgetAdjustment{},

		// 支出控制模型
		&model.PreApplication{},
		&model.ExpenseApplication{},
		&model.ExpenseDetail{},
		&model.Payment{},

		// 采购管理模型
		&model.Supplier{},
		&model.PurchaseRequisition{},
		&model.PurchaseOrder{},
		&model.Contract{},
		&model.ContractPaymentSchedule{},

		// 入库验收模型
		&model.ReceiptRecord{},
		&model.ReceiptItem{},
		&model.InspectionRecord{},

		// 资产管理模型
		&model.AssetCategory{},
		&model.Asset{},
		&model.AssetLocation{},
		&model.AssetMovement{},
		&model.AssetMaintenance{},
		&model.AssetInventory{},
		&model.AssetInventoryItem{},
	)
}
