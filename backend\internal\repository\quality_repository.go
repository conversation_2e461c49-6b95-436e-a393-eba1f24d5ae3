package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type QualityIndicatorRepository struct {
	db *gorm.DB
}

func NewQualityIndicatorRepository(db *gorm.DB) *QualityIndicatorRepository {
	return &QualityIndicatorRepository{db: db}
}

func (r *QualityIndicatorRepository) Create(indicator *model.QualityIndicator) error {
	return r.db.Create(indicator).Error
}

func (r *QualityIndicatorRepository) GetByID(id uint) (*model.QualityIndicator, error) {
	var indicator model.QualityIndicator
	err := r.db.Preload("QualityData").First(&indicator, id).Error
	if err != nil {
		return nil, err
	}
	return &indicator, nil
}

func (r *QualityIndicatorRepository) Update(indicator *model.QualityIndicator) error {
	return r.db.Save(indicator).Error
}

func (r *QualityIndicatorRepository) Delete(id uint) error {
	return r.db.Delete(&model.QualityIndicator{}, id).Error
}

func (r *QualityIndicatorRepository) List(offset, limit int) ([]model.QualityIndicator, int64, error) {
	var indicators []model.QualityIndicator
	var total int64

	err := r.db.Model(&model.QualityIndicator{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Offset(offset).Limit(limit).Find(&indicators).Error
	if err != nil {
		return nil, 0, err
	}

	return indicators, total, nil
}

func (r *QualityIndicatorRepository) CreateData(data *model.QualityData) error {
	return r.db.Create(data).Error
}

func (r *QualityIndicatorRepository) GetDataByIndicatorID(indicatorID uint, offset, limit int) ([]model.QualityData, int64, error) {
	var data []model.QualityData
	var total int64

	err := r.db.Model(&model.QualityData{}).Where("indicator_id = ?", indicatorID).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Preload("Indicator").Preload("Collector").
		Where("indicator_id = ?", indicatorID).
		Order("period DESC").
		Offset(offset).Limit(limit).Find(&data).Error
	if err != nil {
		return nil, 0, err
	}

	return data, total, nil
}
