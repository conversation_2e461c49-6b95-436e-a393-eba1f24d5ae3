import api from './index'
import type { ApiResponse, PaginationParams, PaginationResponse } from '@/types'

// 入库单相关类型定义
export interface ReceiptRecord {
  id: string
  receipt_code: string
  order_id: string
  supplier_id: string
  receiver_id: string
  inspector_id?: string
  receipt_date: string
  inspect_date?: string
  total_amount: number
  status: 'RECEIVED' | 'INSPECTING' | 'PASSED' | 'REJECTED'
  remark?: string
  created_at: string
  updated_at: string
  order?: {
    id: string
    order_code: string
    total_amount: number
  }
  supplier?: {
    id: string
    name: string
    credit_code: string
  }
  receiver?: {
    id: string
    username: string
    real_name: string
  }
  inspector?: {
    id: string
    username: string
    real_name: string
  }
  receipt_items: ReceiptItem[]
}

export interface ReceiptItem {
  id: string
  receipt_id: string
  item_name: string
  item_spec?: string
  unit: string
  ordered_quantity: number
  received_quantity: number
  qualified_quantity: number
  unit_price: number
  total_price: number
  batch_number?: string
  expiry_date?: string
  inspect_result: 'PENDING' | 'PASSED' | 'REJECTED'
  inspect_remark?: string
  created_at: string
  updated_at: string
}

export interface CreateReceiptRequest {
  order_id: string
  receiver_id: string
  receipt_date: string
  items: {
    item_name: string
    item_spec?: string
    unit: string
    ordered_quantity: number
    received_quantity: number
    unit_price: number
    batch_number?: string
    expiry_date?: string
  }[]
  remark?: string
}

export interface InspectReceiptRequest {
  inspector_id: string
  items: {
    item_id: string
    qualified_quantity: number
    inspect_result: 'PASSED' | 'REJECTED'
    inspect_remark?: string
  }[]
  remark?: string
}

export interface ReceiptStatistics {
  total_count: number
  total_amount: number
  status_stats: {
    status: string
    count: number
  }[]
}

// 获取入库单列表
export const getReceiptList = (params: PaginationParams & {
  status?: string
  supplier_id?: string
  start_date?: string
  end_date?: string
}) => {
  return api.get<ApiResponse<PaginationResponse<ReceiptRecord>>>('/receipts', { params })
}

// 根据ID获取入库单详情
export const getReceiptById = (id: string) => {
  return api.get<ApiResponse<ReceiptRecord>>(`/receipts/${id}`)
}

// 创建入库单
export const createReceipt = (data: CreateReceiptRequest) => {
  return api.post<ApiResponse<ReceiptRecord>>('/receipts', data)
}

// 质检入库单
export const inspectReceipt = (id: string, data: InspectReceiptRequest) => {
  return api.post<ApiResponse<null>>(`/receipts/${id}/inspect`, data)
}

// 更新入库单状态
export const updateReceiptStatus = (id: string, status: string) => {
  return api.put<ApiResponse<null>>(`/receipts/${id}/status`, { status })
}

// 获取待质检的入库单
export const getPendingInspectionReceipts = (inspector_id?: string) => {
  return api.get<ApiResponse<ReceiptRecord[]>>('/receipts/pending-inspection', {
    params: { inspector_id }
  })
}

// 获取入库统计数据
export const getReceiptStatistics = (start_date?: string, end_date?: string) => {
  return api.get<ApiResponse<ReceiptStatistics>>('/receipts/statistics', {
    params: { start_date, end_date }
  })
}

// 导出入库单
export const exportReceipt = (id: string) => {
  return api.get(`/receipts/${id}/export`, { responseType: 'blob' })
}

// 打印入库单
export const printReceipt = (id: string) => {
  return api.get(`/receipts/${id}/print`, { responseType: 'blob' })
}
