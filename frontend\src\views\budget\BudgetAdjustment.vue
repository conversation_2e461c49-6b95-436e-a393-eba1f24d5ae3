<template>
  <div class="budget-adjustment">
    <a-page-header title="预算调整申请" sub-title="预算调整的申请和审批流程" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="statusFilter"
            placeholder="筛选状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="PENDING">待审批</a-select-option>
            <a-select-option value="APPROVED">已批准</a-select-option>
            <a-select-option value="REJECTED">已拒绝</a-select-option>
          </a-select>
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            申请调整
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'adjustment_type'">
            <a-tag :color="getTypeColor(record.adjustment_type)">
              {{ getTypeText(record.adjustment_type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'amount'">
            <span :class="['amount', record.adjustment_type === 'DECREASE' ? 'decrease' : 'increase']">
              {{ record.adjustment_type === 'DECREASE' ? '-' : '+' }}¥{{ formatAmount(record.amount) }}
            </span>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="showEditModal(record)"
                :disabled="record.status !== 'PENDING'"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-popconfirm
                title="确定要撤回这个调整申请吗？"
                @confirm="withdrawAdjustment(record.id)"
                :disabled="record.status !== 'PENDING'"
              >
                <a-button 
                  type="text" 
                  size="small" 
                  danger
                  :disabled="record.status !== 'PENDING'"
                >
                  <RollbackOutlined />
                  撤回
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑调整申请模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑调整申请' : '申请预算调整'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调整单号" name="adjustment_code">
              <a-input v-model:value="formData.adjustment_code" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调整类型" name="adjustment_type">
              <a-select v-model:value="formData.adjustment_type" placeholder="选择调整类型">
                <a-select-option value="INCREASE">增加预算</a-select-option>
                <a-select-option value="DECREASE">减少预算</a-select-option>
                <a-select-option value="TRANSFER">预算转移</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="预算项目" name="budget_item_id">
          <a-select
            v-model:value="formData.budget_item_id"
            placeholder="选择预算项目"
            show-search
            :filter-option="filterBudgetItem"
          >
            <a-select-option 
              v-for="item in budgetItemOptions" 
              :key="item.id" 
              :value="item.id"
            >
              {{ item.item_name }} ({{ item.item_code }}) - ¥{{ formatAmount(item.budget_amount) }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调整金额" name="amount">
              <a-input-number
                v-model:value="formData.amount"
                :min="0"
                :precision="2"
                placeholder="调整金额"
                style="width: 100%"
                :addon-before="formData.adjustment_type === 'DECREASE' ? '-' : '+'"
                addon-after="元"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="申请人" name="applicant_id">
              <a-input v-model:value="currentUserName" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="调整原因" name="reason">
          <a-textarea
            v-model:value="formData.reason"
            placeholder="请详细说明预算调整的原因"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            multiple
          >
            <a-button>
              <UploadOutlined />
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情查看模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="调整申请详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="调整单号">{{ currentRecord.adjustment_code }}</a-descriptions-item>
          <a-descriptions-item label="调整类型">
            <a-tag :color="getTypeColor(currentRecord.adjustment_type)">
              {{ getTypeText(currentRecord.adjustment_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预算项目">{{ currentRecord.budget_item?.item_name }}</a-descriptions-item>
          <a-descriptions-item label="项目编码">{{ currentRecord.budget_item?.item_code }}</a-descriptions-item>
          <a-descriptions-item label="调整金额">
            <span :class="['amount', currentRecord.adjustment_type === 'DECREASE' ? 'decrease' : 'increase']">
              {{ currentRecord.adjustment_type === 'DECREASE' ? '-' : '+' }}¥{{ formatAmount(currentRecord.amount) }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentRecord.applicant?.user_name }}</a-descriptions-item>
          <a-descriptions-item label="申请时间">{{ currentRecord.created_at }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="调整原因" :span="2">{{ currentRecord.reason }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>审批历史</a-divider>
        
        <a-timeline>
          <a-timeline-item color="blue">
            <template #dot>
              <FileTextOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">提交申请</div>
              <div class="timeline-desc">{{ currentRecord.applicant?.user_name }} 提交了预算调整申请</div>
              <div class="timeline-time">{{ currentRecord.created_at }}</div>
            </div>
          </a-timeline-item>
          
          <a-timeline-item 
            v-if="currentRecord.status === 'APPROVED'" 
            color="green"
          >
            <template #dot>
              <CheckCircleOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">审批通过</div>
              <div class="timeline-desc">财务部门审批通过，预算调整生效</div>
              <div class="timeline-time">{{ currentRecord.updated_at }}</div>
            </div>
          </a-timeline-item>
          
          <a-timeline-item 
            v-else-if="currentRecord.status === 'REJECTED'" 
            color="red"
          >
            <template #dot>
              <CloseCircleOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">审批拒绝</div>
              <div class="timeline-desc">财务部门审批拒绝，请重新提交申请</div>
              <div class="timeline-time">{{ currentRecord.updated_at }}</div>
            </div>
          </a-timeline-item>
          
          <a-timeline-item 
            v-else 
            color="gray"
          >
            <template #dot>
              <ClockCircleOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">待审批</div>
              <div class="timeline-desc">等待财务部门审批</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  RollbackOutlined,
  UploadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import type { BudgetAdjustment, BudgetItem } from '@/types'

const loading = ref(false)
const statusFilter = ref<string>()
const tableData = ref<BudgetAdjustment[]>([])
const budgetItemOptions = ref<BudgetItem[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentRecord = ref<BudgetAdjustment | null>(null)
const fileList = ref([])
const currentUserName = ref('张三')

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '调整单号', dataIndex: 'adjustment_code', key: 'adjustment_code', width: 150 },
  { title: '预算项目', dataIndex: ['budget_item', 'item_name'], key: 'budget_item', width: 150 },
  { title: '调整类型', key: 'adjustment_type', width: 100 },
  { title: '调整金额', key: 'amount', width: 120 },
  { title: '申请人', dataIndex: ['applicant', 'user_name'], key: 'applicant', width: 100 },
  { title: '申请时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
  { title: '状态', key: 'status', width: 100 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const formData = reactive({
  id: '',
  adjustment_code: '',
  budget_item_id: '',
  adjustment_type: '',
  amount: 0,
  reason: '',
  applicant_id: ''
})

const formRules = {
  adjustment_type: [{ required: true, message: '请选择调整类型', trigger: 'change' }],
  budget_item_id: [{ required: true, message: '请选择预算项目', trigger: 'change' }],
  amount: [{ required: true, message: '请输入调整金额', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入调整原因', trigger: 'blur' }]
}

const getTypeColor = (type: string) => {
  const colors = {
    'INCREASE': 'green',
    'DECREASE': 'red',
    'TRANSFER': 'blue'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: string) => {
  const texts = {
    'INCREASE': '增加预算',
    'DECREASE': '减少预算',
    'TRANSFER': '预算转移'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'processing',
    'APPROVED': 'success',
    'REJECTED': 'error'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝'
  }
  return texts[status] || status
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const generateAdjustmentCode = () => {
  const now = new Date()
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
  const timeStr = now.getTime().toString().slice(-4)
  return `ADJ${dateStr}${timeStr}`
}

const filterBudgetItem = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

const loadAdjustments = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取调整申请列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        adjustment_code: 'ADJ20240723001',
        budget_item_id: '1',
        adjustment_type: 'INCREASE',
        amount: 100000,
        reason: '由于医疗设备价格上涨，需要增加预算',
        status: 'PENDING',
        applicant_id: '1',
        created_at: '2024-07-23 09:30:00',
        updated_at: '2024-07-23 09:30:00',
        budget_item: {
          id: '1',
          item_name: '医疗设备采购',
          item_code: 'YLSB001',
          budget_amount: 500000
        },
        applicant: {
          id: '1',
          user_name: '张三'
        }
      },
      {
        id: '2',
        adjustment_code: 'ADJ20240722001',
        budget_item_id: '2',
        adjustment_type: 'DECREASE',
        amount: 50000,
        reason: '项目取消，减少预算',
        status: 'APPROVED',
        applicant_id: '2',
        created_at: '2024-07-22 14:20:00',
        updated_at: '2024-07-23 10:15:00',
        budget_item: {
          id: '2',
          item_name: '医用耗材',
          item_code: 'YYHC001',
          budget_amount: 300000
        },
        applicant: {
          id: '2',
          user_name: '李四'
        }
      }
    ]
    pagination.total = 2
  } catch (error) {
    message.error('加载调整申请数据失败')
  } finally {
    loading.value = false
  }
}

const loadBudgetItems = async () => {
  try {
    // TODO: 调用API获取预算项目列表
    budgetItemOptions.value = [
      {
        id: '1',
        item_name: '医疗设备采购',
        item_code: 'YLSB001',
        budget_amount: 500000,
        category: '设备费用'
      },
      {
        id: '2',
        item_name: '医用耗材',
        item_code: 'YYHC001',
        budget_amount: 300000,
        category: '材料费用'
      },
      {
        id: '3',
        item_name: '培训费用',
        item_code: 'PXFY001',
        budget_amount: 200000,
        category: '管理费用'
      }
    ]
  } catch (error) {
    message.error('加载预算项目数据失败')
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadAdjustments()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadAdjustments()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    adjustment_code: generateAdjustmentCode(),
    budget_item_id: '',
    adjustment_type: '',
    amount: 0,
    reason: '',
    applicant_id: ''
  })
  fileList.value = []
}

const showEditModal = (record: BudgetAdjustment) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, record)
  fileList.value = []
}

const viewDetails = (record: BudgetAdjustment) => {
  currentRecord.value = record
  detailModalVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('调整申请更新成功')
    } else {
      // TODO: 调用创建API
      message.success('调整申请提交成功，等待审批')
    }
    
    modalVisible.value = false
    loadAdjustments()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
  fileList.value = []
}

const withdrawAdjustment = async (id: string) => {
  try {
    // TODO: 调用撤回API
    message.success('调整申请已撤回')
    loadAdjustments()
  } catch (error) {
    message.error('撤回失败')
  }
}

const beforeUpload = (file: any) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
  }
  return false // 阻止自动上传
}

const handleRemove = (file: any) => {
  const index = fileList.value.indexOf(file)
  const newFileList = fileList.value.slice()
  newFileList.splice(index, 1)
  fileList.value = newFileList
}

onMounted(() => {
  loadAdjustments()
  loadBudgetItems()
})
</script>

<style scoped>
.budget-adjustment {
  padding: 24px;
}

.amount {
  font-weight: 500;
}

.amount.increase {
  color: #52c41a;
}

.amount.decrease {
  color: #f5222d;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #666;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
}
</style>
