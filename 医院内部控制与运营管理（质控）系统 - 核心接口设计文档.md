1. API 设计总则
本系统所有 API 遵循 RESTful 设计风格，并遵守以下约定：

协议与基地址: 所有 API 均通过 HTTPS 协议访问。基地址 (Base URL) 格式为：https://api.yourhospital.com/api/v1。

版本管理: API 版本通过 URL 路径进行管理（例如: /api/v1）。

认证与授权:

除登录接口外，所有请求都必须在 HTTP Header 中包含 Authorization: Bearer <JWT_TOKEN>。
服务器通过解析 JWT 获取用户身份，并根据 tbl_user_roles 和 tbl_roles 的权限配置进行接口访问授权。
数据格式: 所有请求体 (Request Body) 和响应体 (Response Body) 均为 application/json 格式，UTF-8 编码。

状态码:

200 OK: 请求成功（GET, PUT, PATCH, DELETE）。
201 Created: 资源创建成功（POST）。
204 No Content: 请求成功，但无返回内容（DELETE）。
400 Bad Request: 请求参数错误或业务校验失败。
401 Unauthorized: 未认证或 Token 失效。
403 Forbidden: 已认证，但无权访问该资源。
404 Not Found:请求的资源不存在。
500 Internal Server Error: 服务器内部错误。
统一响应结构: 所有 API 响应都包装在统一的 JSON 结构中，便于前端统一处理。

<JSON>
{
  "success": true, // true 表示业务成功, false 表示业务失败
  "code": 20000,   // 自定义业务状态码 (例如: 20000-成功, 40001-参数错误, 40301-权限不足)
  "message": "操作成功",
  "data": { ... } // 实际返回的数据，如果无数据则为 null 或 {}
}
当 success 为 false 时，data 为 null，message 包含详细错误信息。

2. 核心接口定义
2.1 认证模块 (Auth Module)
用户登录
Endpoint: POST /auth/login
描述: 用户通过邮箱/工号和密码进行登录，获取 JWT Token。
请求体 (Request Body):
<JSON>
{
  "principal": "employee001", // 可能是邮箱、工号或手机号
  "credential": "password123"
}
成功响应 (200 OK):
<JSON>
{
  "success": true,
  "code": 20000,
  "message": "登录成功",
  "data": {
    "token": "eyJh...long_jwt_string...c4s",
    "expires_in": 7200
  }
}
2.2 用户与权限模块 (User & Profile Module)
获取当前用户信息
Endpoint: GET /profile/me
描述: 获取当前登录用户的详细信息，包括其角色和权限。
请求体: None
成功响应 (200 OK):
<JSON>
{
  "success": true,
  "code": 20000,
  "message": "获取成功",
  "data": {
    "id": "a1b2c3d4-...",
    "userName": "张三",
    "employeeId": "EMP000123",
    "email": "<EMAIL>",
    "department": {
      "id": "d1e2f3g4-...",
      "name": "信息科"
    },
    "roles": [
      { "roleName": "科室经办", "roleCode": "DEPT_OPERATOR" },
      { "roleName": "普通员工", "roleCode": "EMPLOYEE" }
    ],
    "permissions": { ... } // 合并后的权限集合
  }
}
2.3 支出控制模块 (Expense Module)
创建报销申请
Endpoint: POST /expense-applications
描述: 提交一个新的费用报销申请。服务器需要进行预算占用（冻结额度）和启动审批流等操作。
请求体 (Request Body):
<JSON>
{
  "title": "2025年7月市内交通费报销",
  "departmentId": "d1e2f3g4-...", // 费用归属部门
  "details": [
    {
      "budgetItemId": "b1c2d3e4-...", // 关联的预算明细项ID
      "description": "从医院到市卫健委往返",
      "amount": "85.50",
      "expenseDate": "2025-07-22",
      "invoiceInfo": { "count": 2 } // 可选的发票信息
    },
    {
      "budgetItemId": "b1c2d3e4-...",
      "description": "外出培训交通",
      "amount": "120.00",
      "expenseDate": "2025-07-20"
    }
  ],
  "attachments": [ // 关联的文件ID
     "f1g2h3i4-..."
  ]
}
成功响应 (201 Created):
<JSON>
{
  "success": true,
  "code": 20100,
  "message": "报销单提交成功，已进入审批流程",
  "data": {
    "id": "e1f2g3h4-...", // 新创建的报销单ID
    "applicationCode": "BX202507230001",
    "status": "PENDING"
  }
}
查询报销申请列表 (我的申请)
Endpoint: GET /expense-applications
描述: 分页查询当前用户发起的报销申请列表，支持按状态和日期过滤。
查询参数 (Query Params):
page (int, default: 1): 页码
pageSize (int, default: 10): 每页数量
status (string): 状态过滤 (e.g., 'PENDING', 'APPROVED')
startDate (string, 'YYYY-MM-DD'): 开始日期
endDate (string, 'YYYY-MM-DD'): 结束日期
成功响应 (200 OK):
<JSON>
{
  "success": true,
  "code": 20000,
  "message": "查询成功",
  "data": {
    "total": 25,
    "list": [
      {
        "id": "e1f2g3h4-...",
        "applicationCode": "BX202507230001",
        "title": "2025年7月市内交通费报销",
        "totalAmount": "205.50",
        "status": "PENDING",
        "createdAt": "2025-07-23T10:30:00Z"
      }
    ]
  }
}
2.4 审批模块 (Approval Module)
查询我的待办任务
Endpoint: GET /approvals/tasks
描述: 获取需要当前用户审批的任务列表。
查询参数 (Query Params):
page, pageSize
businessType (string): 按业务类型过滤 (e.g., 'EXPENSE', 'CONTRACT')
成功响应 (200 OK):
<JSON>
{
    "success": true,
    "code": 20000,
    "message": "查询成功",
    "data": {
        "total": 5,
        "list": [
            {
                "taskId": "t1a2b3c4-...", // 审批节点ID
                "flowId": "f1l2o3w4-...",
                "businessType": "EXPENSE",
                "businessId": "e1f2g3h4-...",
                "businessCode": "BX202507230001",
                "title": "张三提交的交通费报销",
                "applicantName": "张三",
                "amount": "205.50",
                "receivedAt": "2025-07-23T10:35:00Z"
            }
        ]
    }
}
执行审批操作
Endpoint: POST /approvals/tasks/process
描述: 对某个审批任务执行“同意”或“驳回”等操作。
请求体 (Request Body):
<JSON>
{
  "taskId": "t1a2b3c4-...", // 要处理的审批节点ID
  "action": "APPROVE", // 'APPROVE', 'REJECT'
  "comment": "情况属实，同意报销。"
}
成功响应 (200 OK):
<JSON>
{
  "success": true,
  "code": 20000,
  "message": "审批成功",
  "data": null
}