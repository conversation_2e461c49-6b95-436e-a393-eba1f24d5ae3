<template>
  <div class="purchase-order">
    <a-page-header title="采购订单" sub-title="采购订单创建与管理、采购订单跟踪" />

    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="statusFilter"
            placeholder="订单状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="PENDING">待确认</a-select-option>
            <a-select-option value="CONFIRMED">已确认</a-select-option>
            <a-select-option value="SHIPPED">已发货</a-select-option>
            <a-select-option value="DELIVERED">已到货</a-select-option>
            <a-select-option value="COMPLETED">已完成</a-select-option>
            <a-select-option value="CANCELLED">已取消</a-select-option>
          </a-select>
          <a-select
            v-model:value="supplierFilter"
            placeholder="供应商"
            style="width: 200px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="supplier in supplierOptions"
              :key="supplier.id"
              :value="supplier.id"
            >
              {{ supplier.supplier_name }}
            </a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="dateRange"
            placeholder="订单时间范围"
            @change="handleFilterChange"
          />
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新建订单
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
        :expand-row-by-click="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'total_amount'">
            <span class="amount">¥{{ formatAmount(record.total_amount) }}</span>
          </template>

          <template v-if="column.key === 'delivery_progress'">
            <div class="delivery-progress">
              <a-progress
                :percent="getDeliveryProgress(record)"
                size="small"
                :status="getProgressStatus(record)"
              />
              <span class="progress-text">{{ getProgressText(record) }}</span>
            </div>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button
                type="text"
                size="small"
                @click="showEditModal(record)"
                :disabled="!['PENDING'].includes(record.status)"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-button
                type="text"
                size="small"
                @click="trackOrder(record)"
              >
                <CarOutlined />
                跟踪
              </a-button>
              <a-dropdown>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="confirm" v-if="record.status === 'PENDING'">
                      <a @click="confirmOrder(record.id)">确认订单</a>
                    </a-menu-item>
                    <a-menu-item key="ship" v-if="record.status === 'CONFIRMED'">
                      <a @click="shipOrder(record.id)">标记发货</a>
                    </a-menu-item>
                    <a-menu-item key="receive" v-if="record.status === 'SHIPPED'">
                      <a @click="receiveOrder(record.id)">确认收货</a>
                    </a-menu-item>
                    <a-menu-item key="print">
                      <a @click="printOrder(record)">打印订单</a>
                    </a-menu-item>
                    <a-menu-item key="cancel" danger v-if="['PENDING', 'CONFIRMED'].includes(record.status)">
                      <a @click="cancelOrder(record.id)">取消订单</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>

        <template #expandedRowRender="{ record }">
          <div class="order-details">
            <a-table
              :columns="itemColumns"
              :data-source="record.order_items || []"
              :pagination="false"
              size="small"
              row-key="id"
            >
              <template #bodyCell="{ column, record: item }">
                <template v-if="column.key === 'unit_price'">
                  <span class="amount">¥{{ formatAmount(item.unit_price) }}</span>
                </template>
                <template v-if="column.key === 'total_price'">
                  <span class="amount">¥{{ formatAmount(item.total_price) }}</span>
                </template>
                <template v-if="column.key === 'delivery_status'">
                  <a-tag :color="getDeliveryStatusColor(item.delivery_status)">
                    {{ getDeliveryStatusText(item.delivery_status) }}
                  </a-tag>
                </template>
              </template>
            </a-table>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑采购订单模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑采购订单' : '新建采购订单'"
      width="1000px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="订单编号" name="order_code">
              <a-input v-model:value="formData.order_code" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="供应商" name="supplier_id">
              <a-select v-model:value="formData.supplier_id" placeholder="选择供应商">
                <a-select-option
                  v-for="supplier in supplierOptions"
                  :key="supplier.id"
                  :value="supplier.id"
                >
                  {{ supplier.supplier_name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="关联申请" name="request_id">
              <a-select
                v-model:value="formData.request_id"
                placeholder="选择关联的采购申请"
                allow-clear
                @change="handleRequestChange"
              >
                <a-select-option
                  v-for="request in requestOptions"
                  :key="request.id"
                  :value="request.id"
                >
                  {{ request.request_code }} - {{ request.title }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="预计交货日期" name="expected_delivery_date">
              <a-date-picker
                v-model:value="formData.expected_delivery_date"
                placeholder="选择预计交货日期"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="交货地址" name="delivery_address">
              <a-input v-model:value="formData.delivery_address" placeholder="交货地址" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="订单明细">
          <a-table
            :columns="editItemColumns"
            :data-source="formData.order_items"
            :pagination="false"
            size="small"
            row-key="temp_id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'item_name'">
                <a-input v-model:value="record.item_name" placeholder="物品名称" />
              </template>
              <template v-if="column.key === 'specifications'">
                <a-input v-model:value="record.specifications" placeholder="规格型号" />
              </template>
              <template v-if="column.key === 'unit'">
                <a-input v-model:value="record.unit" placeholder="单位" />
              </template>
              <template v-if="column.key === 'quantity'">
                <a-input-number
                  v-model:value="record.quantity"
                  :min="1"
                  placeholder="数量"
                  style="width: 100%"
                  @change="calculateItemTotal(record)"
                />
              </template>
              <template v-if="column.key === 'unit_price'">
                <a-input-number
                  v-model:value="record.unit_price"
                  :min="0"
                  :precision="2"
                  placeholder="单价"
                  style="width: 100%"
                  @change="calculateItemTotal(record)"
                />
              </template>
              <template v-if="column.key === 'total_price'">
                <span class="amount">¥{{ formatAmount(record.total_price) }}</span>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="text" size="small" danger @click="removeItem(index)">
                  <DeleteOutlined />
                </a-button>
              </template>
            </template>
          </a-table>

          <a-button type="dashed" block @click="addItem" style="margin-top: 16px">
            <PlusOutlined />
            添加订单项目
          </a-button>

          <div class="total-amount">
            <strong>订单总金额：¥{{ formatAmount(formData.total_amount) }}</strong>
          </div>
        </a-form-item>

        <a-form-item label="备注">
          <a-textarea v-model:value="formData.remark" placeholder="订单备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 订单详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="采购订单详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentOrder">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="订单编号">{{ currentOrder.order_code }}</a-descriptions-item>
          <a-descriptions-item label="供应商">{{ currentOrder.supplier?.supplier_name }}</a-descriptions-item>
          <a-descriptions-item label="订单状态">
            <a-tag :color="getStatusColor(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="订单总金额">¥{{ formatAmount(currentOrder.total_amount) }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentOrder.created_at }}</a-descriptions-item>
          <a-descriptions-item label="预计交货日期">{{ currentOrder.expected_delivery_date }}</a-descriptions-item>
          <a-descriptions-item label="交货地址" :span="3">{{ currentOrder.delivery_address }}</a-descriptions-item>
          <a-descriptions-item label="备注" :span="3">{{ currentOrder.remark || '无' }}</a-descriptions-item>
        </a-descriptions>

        <a-divider>订单明细</a-divider>

        <a-table
          :columns="viewItemColumns"
          :data-source="currentOrder.order_items || []"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'unit_price'">
              <span class="amount">¥{{ formatAmount(record.unit_price) }}</span>
            </template>
            <template v-if="column.key === 'total_price'">
              <span class="amount">¥{{ formatAmount(record.total_price) }}</span>
            </template>
            <template v-if="column.key === 'delivery_status'">
              <a-tag :color="getDeliveryStatusColor(record.delivery_status)">
                {{ getDeliveryStatusText(record.delivery_status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 订单跟踪模态框 -->
    <a-modal
      v-model:open="trackModalVisible"
      title="订单跟踪"
      width="800px"
      :footer="null"
    >
      <div v-if="currentOrder">
        <div class="track-header">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="订单编号">{{ currentOrder.order_code }}</a-descriptions-item>
            <a-descriptions-item label="供应商">{{ currentOrder.supplier?.supplier_name }}</a-descriptions-item>
            <a-descriptions-item label="当前状态">
              <a-tag :color="getStatusColor(currentOrder.status)">
                {{ getStatusText(currentOrder.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="交货进度">{{ getProgressText(currentOrder) }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <a-divider>跟踪记录</a-divider>

        <a-timeline>
          <a-timeline-item
            v-for="(track, index) in trackingHistory"
            :key="index"
            :color="getTrackColor(track.status)"
          >
            <template #dot>
              <CheckCircleOutlined v-if="track.status === 'COMPLETED'" />
              <ClockCircleOutlined v-else-if="track.status === 'PROCESSING'" />
              <FileTextOutlined v-else />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">{{ track.title }}</div>
              <div class="timeline-desc">{{ track.description }}</div>
              <div class="timeline-time">{{ track.created_at }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CarOutlined,
  MoreOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import type { PurchaseOrder, Supplier, PurchaseRequest } from '@/types'

const loading = ref(false)
const statusFilter = ref<string>()
const supplierFilter = ref<string>()
const dateRange = ref()
const tableData = ref<PurchaseOrder[]>([])
const supplierOptions = ref<Supplier[]>([])
const requestOptions = ref<PurchaseRequest[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const trackModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentOrder = ref<PurchaseOrder | null>(null)
const trackingHistory = ref([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '订单编号', dataIndex: 'order_code', key: 'order_code', width: 150 },
  { title: '供应商', dataIndex: ['supplier', 'supplier_name'], key: 'supplier', ellipsis: true },
  { title: '订单金额', key: 'total_amount', width: 120 },
  { title: '预计交货', dataIndex: 'expected_delivery_date', key: 'expected_delivery_date', width: 120 },
  { title: '交货进度', key: 'delivery_progress', width: 150 },
  { title: '订单状态', key: 'status', width: 100 },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
  { title: '操作', key: 'action', width: 250, fixed: 'right' }
]

const itemColumns = [
  { title: '物品名称', dataIndex: 'item_name', key: 'item_name' },
  { title: '规格型号', dataIndex: 'specifications', key: 'specifications' },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '单价', key: 'unit_price', width: 100 },
  { title: '总价', key: 'total_price', width: 100 },
  { title: '交货状态', key: 'delivery_status', width: 100 }
]

const editItemColumns = [
  { title: '物品名称', key: 'item_name', width: 150 },
  { title: '规格型号', key: 'specifications', width: 120 },
  { title: '单位', key: 'unit', width: 80 },
  { title: '数量', key: 'quantity', width: 80 },
  { title: '单价', key: 'unit_price', width: 100 },
  { title: '总价', key: 'total_price', width: 100 },
  { title: '操作', key: 'action', width: 80 }
]

const viewItemColumns = [
  { title: '物品名称', dataIndex: 'item_name', key: 'item_name' },
  { title: '规格型号', dataIndex: 'specifications', key: 'specifications' },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '单价', key: 'unit_price', width: 100 },
  { title: '总价', key: 'total_price', width: 100 },
  { title: '交货状态', key: 'delivery_status', width: 100 }
]

const formData = reactive({
  id: '',
  order_code: '',
  supplier_id: '',
  request_id: '',
  expected_delivery_date: null,
  delivery_address: '',
  total_amount: 0,
  remark: '',
  order_items: [] as any[]
})

const formRules = {
  supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  expected_delivery_date: [{ required: true, message: '请选择预计交货日期', trigger: 'change' }],
  delivery_address: [{ required: true, message: '请输入交货地址', trigger: 'blur' }]
}

const getStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'processing',
    'CONFIRMED': 'blue',
    'SHIPPED': 'orange',
    'DELIVERED': 'green',
    'COMPLETED': 'success',
    'CANCELLED': 'error'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'PENDING': '待确认',
    'CONFIRMED': '已确认',
    'SHIPPED': '已发货',
    'DELIVERED': '已到货',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return texts[status] || status
}

const getDeliveryStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'default',
    'SHIPPED': 'processing',
    'DELIVERED': 'success'
  }
  return colors[status] || 'default'
}

const getDeliveryStatusText = (status: string) => {
  const texts = {
    'PENDING': '待发货',
    'SHIPPED': '已发货',
    'DELIVERED': '已到货'
  }
  return texts[status] || status
}

const getDeliveryProgress = (order: PurchaseOrder) => {
  const statusProgress = {
    'PENDING': 10,
    'CONFIRMED': 30,
    'SHIPPED': 70,
    'DELIVERED': 90,
    'COMPLETED': 100,
    'CANCELLED': 0
  }
  return statusProgress[order.status] || 0
}

const getProgressStatus = (order: PurchaseOrder) => {
  if (order.status === 'CANCELLED') return 'exception'
  if (order.status === 'COMPLETED') return 'success'
  return 'active'
}

const getProgressText = (order: PurchaseOrder) => {
  const texts = {
    'PENDING': '等待确认',
    'CONFIRMED': '准备发货',
    'SHIPPED': '运输中',
    'DELIVERED': '已到货',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return texts[order.status] || order.status
}

const getTrackColor = (status: string) => {
  const colors = {
    'COMPLETED': 'green',
    'PROCESSING': 'blue',
    'PENDING': 'gray'
  }
  return colors[status] || 'gray'
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const generateOrderCode = () => {
  const now = new Date()
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
  const timeStr = now.getTime().toString().slice(-4)
  return `PO${dateStr}${timeStr}`
}

const loadOrders = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取采购订单列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        order_code: 'PO20240723001',
        supplier_id: '1',
        request_id: '1',
        expected_delivery_date: '2024-08-15',
        delivery_address: '上海市浦东新区医院总部',
        total_amount: 150000,
        status: 'SHIPPED',
        created_at: '2024-07-23 10:30:00',
        updated_at: '2024-07-25 14:20:00',
        remark: '紧急采购',
        supplier: {
          id: '1',
          supplier_name: '上海医疗器械有限公司'
        },
        order_items: [
          {
            id: '1',
            item_name: '心电图机',
            specifications: 'ECG-1200G',
            unit: '台',
            quantity: 2,
            unit_price: 50000,
            total_price: 100000,
            delivery_status: 'SHIPPED'
          },
          {
            id: '2',
            item_name: '血压计',
            specifications: 'BP-A100',
            unit: '台',
            quantity: 10,
            unit_price: 5000,
            total_price: 50000,
            delivery_status: 'SHIPPED'
          }
        ]
      }
    ]
    pagination.total = 1
  } catch (error) {
    message.error('加载采购订单数据失败')
  } finally {
    loading.value = false
  }
}

const loadSuppliers = async () => {
  try {
    // TODO: 调用API获取供应商数据
    supplierOptions.value = [
      {
        id: '1',
        supplier_name: '上海医疗器械有限公司',
        status: 'ACTIVE'
      },
      {
        id: '2',
        supplier_name: '北京医用耗材供应商',
        status: 'ACTIVE'
      }
    ]
  } catch (error) {
    message.error('加载供应商数据失败')
  }
}

const loadRequests = async () => {
  try {
    // TODO: 调用API获取已批准的采购申请
    requestOptions.value = [
      {
        id: '1',
        request_code: 'PR20240723001',
        title: '内科医疗设备采购申请',
        status: 'APPROVED'
      }
    ]
  } catch (error) {
    message.error('加载采购申请数据失败')
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadOrders()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadOrders()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    order_code: generateOrderCode(),
    supplier_id: '',
    request_id: '',
    expected_delivery_date: null,
    delivery_address: '',
    total_amount: 0,
    remark: '',
    order_items: []
  })
}

const showEditModal = (order: PurchaseOrder) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, {
    ...order,
    order_items: order.order_items?.map((item, index) => ({
      ...item,
      temp_id: index
    })) || []
  })
}

const viewDetails = (order: PurchaseOrder) => {
  currentOrder.value = order
  detailModalVisible.value = true
}

const trackOrder = (order: PurchaseOrder) => {
  currentOrder.value = order
  // TODO: 加载跟踪历史
  trackingHistory.value = [
    {
      title: '订单创建',
      description: '采购订单已创建',
      status: 'COMPLETED',
      created_at: '2024-07-23 10:30:00'
    },
    {
      title: '订单确认',
      description: '供应商已确认订单',
      status: 'COMPLETED',
      created_at: '2024-07-23 14:20:00'
    },
    {
      title: '商品发货',
      description: '商品已从供应商仓库发出',
      status: 'COMPLETED',
      created_at: '2024-07-25 09:15:00'
    },
    {
      title: '运输中',
      description: '商品正在运输途中',
      status: 'PROCESSING',
      created_at: '2024-07-25 14:20:00'
    },
    {
      title: '预计到货',
      description: '预计2024-08-15到达',
      status: 'PENDING',
      created_at: null
    }
  ]
  trackModalVisible.value = true
}

const handleRequestChange = (requestId: string) => {
  if (requestId) {
    const request = requestOptions.value.find(r => r.id === requestId)
    if (request && request.purchase_items) {
      formData.order_items = request.purchase_items.map((item, index) => ({
        ...item,
        temp_id: index,
        delivery_status: 'PENDING'
      }))
      calculateTotal()
    }
  }
}

const addItem = () => {
  formData.order_items.push({
    temp_id: Date.now(),
    item_name: '',
    specifications: '',
    unit: '',
    quantity: 1,
    unit_price: 0,
    total_price: 0,
    delivery_status: 'PENDING'
  })
}

const removeItem = (index: number) => {
  formData.order_items.splice(index, 1)
  calculateTotal()
}

const calculateItemTotal = (item: any) => {
  item.total_price = (item.quantity || 0) * (item.unit_price || 0)
  calculateTotal()
}

const calculateTotal = () => {
  formData.total_amount = formData.order_items.reduce((sum, item) => sum + (item.total_price || 0), 0)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (formData.order_items.length === 0) {
      message.error('请至少添加一个订单项目')
      return
    }

    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('采购订单更新成功')
    } else {
      // TODO: 调用创建API
      message.success('采购订单创建成功')
    }

    modalVisible.value = false
    loadOrders()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const confirmOrder = async (id: string) => {
  try {
    // TODO: 调用确认订单API
    message.success('订单已确认')
    loadOrders()
  } catch (error) {
    message.error('确认订单失败')
  }
}

const shipOrder = async (id: string) => {
  try {
    // TODO: 调用发货API
    message.success('订单已标记为发货')
    loadOrders()
  } catch (error) {
    message.error('标记发货失败')
  }
}

const receiveOrder = async (id: string) => {
  try {
    // TODO: 调用收货API
    message.success('订单已确认收货')
    loadOrders()
  } catch (error) {
    message.error('确认收货失败')
  }
}

const printOrder = (order: PurchaseOrder) => {
  // TODO: 实现打印订单功能
  message.info('打印订单功能开发中')
}

const cancelOrder = async (id: string) => {
  try {
    // TODO: 调用取消订单API
    message.success('订单已取消')
    loadOrders()
  } catch (error) {
    message.error('取消订单失败')
  }
}

onMounted(() => {
  loadOrders()
  loadSuppliers()
  loadRequests()
})
</script>

<style scoped>
.purchase-order {
  padding: 24px;
}

.order-details {
  margin: 16px 0;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.delivery-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.total-amount {
  text-align: right;
  margin-top: 16px;
  padding: 12px;
  background: #f0f2f5;
  border-radius: 6px;
}

.track-header {
  margin-bottom: 16px;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #666;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
}
</style>