package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type ApprovalRepository struct {
	db *gorm.DB
}

func NewApprovalRepository(db *gorm.DB) *ApprovalRepository {
	return &ApprovalRepository{db: db}
}

func (r *ApprovalRepository) CreateFlow(flow *model.ApprovalFlow) error {
	return r.db.Create(flow).Error
}

func (r *ApprovalRepository) GetFlowByID(id string) (*model.ApprovalFlow, error) {
	var flow model.ApprovalFlow
	err := r.db.Preload("Nodes").First(&flow, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &flow, nil
}

func (r *ApprovalRepository) GetFlowByBusiness(businessID, businessType string) (*model.ApprovalFlow, error) {
	var flow model.ApprovalFlow
	err := r.db.Preload("Nodes").
		Where("business_id = ? AND business_type = ?", businessID, businessType).
		First(&flow).Error
	if err != nil {
		return nil, err
	}
	return &flow, nil
}

func (r *ApprovalRepository) UpdateFlow(flow *model.ApprovalFlow) error {
	return r.db.Save(flow).Error
}

func (r *ApprovalRepository) CreateNode(node *model.ApprovalNode) error {
	return r.db.Create(node).Error
}

func (r *ApprovalRepository) GetNodeByID(id string) (*model.ApprovalNode, error) {
	var node model.ApprovalNode
	err := r.db.Preload("Flow").Preload("Approver").First(&node, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &node, nil
}

func (r *ApprovalRepository) UpdateNode(node *model.ApprovalNode) error {
	return r.db.Save(node).Error
}

func (r *ApprovalRepository) GetPendingNodesByApprover(approverID string, offset, limit int, businessType string) ([]model.ApprovalNode, int64, error) {
	var nodes []model.ApprovalNode
	var total int64

	query := r.db.Model(&model.ApprovalNode{}).
		Joins("JOIN tbl_approval_flows ON tbl_approval_nodes.flow_id = tbl_approval_flows.id").
		Where("tbl_approval_nodes.approver_id = ? AND tbl_approval_nodes.status = ?", approverID, "PENDING")

	if businessType != "" {
		query = query.Where("tbl_approval_flows.business_type = ?", businessType)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Preload("Flow").Preload("Approver").
		Order("tbl_approval_nodes.created_at DESC").
		Offset(offset).Limit(limit).Find(&nodes).Error
	if err != nil {
		return nil, 0, err
	}

	return nodes, total, nil
}

func (r *ApprovalRepository) GetProcessedNodesByApprover(approverID string, offset, limit int, businessType string) ([]model.ApprovalNode, int64, error) {
	var nodes []model.ApprovalNode
	var total int64

	query := r.db.Model(&model.ApprovalNode{}).
		Joins("JOIN tbl_approval_flows ON tbl_approval_nodes.flow_id = tbl_approval_flows.id").
		Where("tbl_approval_nodes.approver_id = ? AND tbl_approval_nodes.status IN ?", approverID, []string{"APPROVED", "REJECTED"})

	if businessType != "" {
		query = query.Where("tbl_approval_flows.business_type = ?", businessType)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Preload("Flow").Preload("Approver").
		Order("tbl_approval_nodes.processed_at DESC").
		Offset(offset).Limit(limit).Find(&nodes).Error
	if err != nil {
		return nil, 0, err
	}

	return nodes, total, nil
}

func (r *ApprovalRepository) GetFlowHistory(businessID, businessType string) ([]model.ApprovalNode, error) {
	var nodes []model.ApprovalNode
	err := r.db.Joins("JOIN tbl_approval_flows ON tbl_approval_nodes.flow_id = tbl_approval_flows.id").
		Where("tbl_approval_flows.business_id = ? AND tbl_approval_flows.business_type = ?", businessID, businessType).
		Preload("Approver").
		Order("tbl_approval_nodes.step ASC").
		Find(&nodes).Error
	return nodes, err
}

func (r *ApprovalRepository) GetNodesByFlow(flowID string) ([]model.ApprovalNode, error) {
	var nodes []model.ApprovalNode
	err := r.db.Where("flow_id = ?", flowID).
		Preload("Approver").
		Order("step ASC").
		Find(&nodes).Error
	return nodes, err
}

func (r *ApprovalRepository) GetCurrentNode(flowID string, step int) (*model.ApprovalNode, error) {
	var node model.ApprovalNode
	err := r.db.Where("flow_id = ? AND step = ? AND status = ?", flowID, step, "PENDING").
		Preload("Approver").
		First(&node).Error
	if err != nil {
		return nil, err
	}
	return &node, nil
}
