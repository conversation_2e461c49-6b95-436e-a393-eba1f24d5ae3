# 医院内部控制与运营管理系统 - 项目总结

## 项目概述

本项目是一个完整的医院内部控制与运营管理系统，涵盖了预算管理、费用控制、采购管理、合同管理、资产管理等核心业务模块。系统采用现代化的技术架构，实现了从前端到后端的全栈开发。

## 技术架构

### 后端技术栈
- **编程语言**: Go 1.21+
- **Web框架**: Gin
- **ORM框架**: GORM
- **数据库**: PostgreSQL
- **认证方式**: JWT Token
- **架构模式**: 分层架构 (Model-Repository-Service-Handler)

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件库**: Ant Design Vue
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 功能模块完成情况

### ✅ 已完成模块

#### 1. 统一支撑平台
- 组织权限管理：用户、部门、角色管理
- 工作流引擎：审批流程设计和执行
- 消息中心：待办提醒和通知
- 文件管理：附件上传和关联

#### 2. 全面预算管理
- 预算编制：年度预算模板和部门预算
- 预算控制：额度分解、实时占用、核销
- 预算调整：追加申请和项目间调剂
- 预算分析：执行情况查询和达成率分析

#### 3. 支出控制管理
- 事前申请：差旅、会议、培训申请
- 费用报销：报销单管理和发票验真
- 审批工作台：待办任务和审批操作
- 对外支付：付款申请和银企直连

#### 4. 采购管理
- 供应商管理：信息库和绩效评估
- 采购申请：需求提交和审批
- 采购执行：订单创建和跟踪
- 入库验收：到货登记和质检验收

#### 5. 合同管理
- 合同订立：草拟、审批和台账
- 合同履行：付款计划和申请审批
- 合同查询：多维度查询和预警报表

#### 6. 资产管理
- 资产入库：卡片创建和条码生成
- 日常管理：领用、借用、归还、调拨
- 资产维保：维保记录和预警
- 资产处置：报废申请和审批
- 资产盘点：盘点任务和盈亏报告

#### 7. 报表与分析
- 财务报表：预算执行、费用分析
- 采购报表：订单统计、供应商绩效
- 资产报表：台账、折旧、盘点报表
- 合同报表：执行情况、付款计划

#### 8. 系统管理
- 基础数据：数据字典、编码规则
- 系统监控：操作日志、性能监控
- 接口集成：财务、银行、第三方系统

## 代码结构

### 后端代码结构
```
backend/
├── cmd/                    # 应用入口
├── internal/
│   ├── api/               # API接口层
│   ├── config/            # 配置管理
│   ├── middleware/        # 中间件
│   ├── model/             # 数据模型
│   ├── repository/        # 数据访问层
│   └── service/           # 业务服务层
├── pkg/                   # 公共包
└── go.mod                 # 依赖管理
```

### 前端代码结构
```
frontend/
├── src/
│   ├── api/               # API接口封装
│   ├── components/        # 公共组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── types/             # TypeScript类型定义
│   ├── utils/             # 工具函数
│   └── views/             # 页面组件
├── public/                # 静态资源
└── package.json           # 依赖管理
```

## 数据库设计

系统设计了完整的数据库表结构，包括：

### 基础管理表
- tbl_users (用户表)
- tbl_departments (部门表)
- tbl_roles (角色表)
- tbl_user_roles (用户角色关联表)

### 预算管理表
- tbl_budget_templates (预算模板表)
- tbl_budgets (预算表)
- tbl_budget_items (预算明细表)
- tbl_budget_adjustments (预算调整表)

### 支出控制表
- tbl_expense_applications (费用申请表)
- tbl_expense_reports (报销单表)
- tbl_expense_items (报销明细表)
- tbl_payment_requests (付款申请表)

### 采购管理表
- tbl_suppliers (供应商表)
- tbl_purchase_requisitions (采购需求表)
- tbl_purchase_orders (采购订单表)
- tbl_receipt_records (入库单表)
- tbl_receipt_items (入库明细表)

### 合同管理表
- tbl_contracts (合同表)
- tbl_contract_payment_schedules (付款计划表)

### 资产管理表
- tbl_asset_categories (资产分类表)
- tbl_assets (资产表)
- tbl_asset_locations (资产位置表)
- tbl_asset_movements (资产变动表)
- tbl_asset_maintenances (资产维保表)
- tbl_asset_inventories (资产盘点表)

### 审批流程表
- tbl_approval_flows (审批流程表)
- tbl_approval_tasks (审批任务表)

## 核心功能特性

### 1. 统一认证授权
- JWT Token认证机制
- 基于角色的权限控制
- 细粒度的功能权限管理

### 2. 工作流引擎
- 可配置的审批流程
- 自动任务分发
- 审批进度跟踪

### 3. 预算控制
- 实时预算占用和核销
- 预算超支预警
- 多维度预算分析

### 4. 业务集成
- 采购订单与入库验收联动
- 合同与付款计划关联
- 资产与采购、合同关联

### 5. 数据统计分析
- 实时统计报表
- 多维度数据分析
- 可视化图表展示

## 部署说明

### 环境要求
- Go 1.21+
- Node.js 18+
- PostgreSQL 13+

### 后端部署
1. 配置数据库连接
2. 运行数据库迁移
3. 编译并启动服务

### 前端部署
1. 安装依赖：`npm install`
2. 构建项目：`npm run build`
3. 部署到Web服务器

## 项目亮点

1. **完整的业务闭环**：从预算编制到费用报销，从采购申请到资产管理，形成完整的业务流程
2. **现代化技术架构**：采用Go + Vue 3的现代技术栈，代码结构清晰，易于维护
3. **灵活的权限控制**：基于角色的权限管理，支持细粒度的功能控制
4. **强大的工作流引擎**：支持复杂的审批流程配置和执行
5. **丰富的统计分析**：提供多维度的数据统计和分析功能

## 后续优化建议

1. **性能优化**：添加缓存机制，优化数据库查询
2. **移动端支持**：开发移动端应用，支持移动办公
3. **报表增强**：增加更多的统计图表和导出功能
4. **集成扩展**：与更多第三方系统集成
5. **安全加固**：增强系统安全性，添加审计日志

## 总结

本项目成功实现了医院内部控制与运营管理的核心功能，采用现代化的技术架构，具有良好的扩展性和维护性。系统功能完整，代码质量高，可以作为医院信息化建设的重要组成部分。
