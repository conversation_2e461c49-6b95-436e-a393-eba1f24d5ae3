设计原则回顾:

表前缀: tbl_
主键: id UUID
命名: 小写蛇形 column_name
通用字段: created_at, updated_at, deleted_at, created_by, updated_by
模块一：统一支撑平台 (Foundation & Platform)
这些是构建所有上层业务应用的基础。

tbl_users (员工/用户表) - (同V1.0)
职责: 存储系统用户。
tbl_departments (科室/部门表) - (同V1.0)
职责: 存储组织架构。
tbl_roles (角色表)
职责: 定义系统角色。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | role_name | VARCHAR(100) | NOT NULL, UNIQUE | 角色名称 (如: 科室经办, 财务审核) | | role_code | VARCHAR(50) | NOT NULL, UNIQUE | 角色编码 (如: DEPT_OPERATOR) | | permissions | JSONB | | 角色的权限集合 (如: {"menu": ["/budget", "/expense"], "api": ["POST:/api/expense"]}) | | description | TEXT | | 角色描述 | | ... | | | (通用字段) |
tbl_user_roles (用户角色关联表)
职责: 连接用户和角色 (多对多)。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | user_id | UUID | PK, FK -> tbl_users(id) | 用户ID | | role_id | UUID | PK, FK -> tbl_roles(id) | 角色ID |
tbl_approval_flows (审批流实例表)
职责: 记录每个业务单据关联的审批流。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | business_id | UUID | NOT NULL | 关联的业务单据ID (如报销单ID、合同ID) | | business_type | VARCHAR(50) | NOT NULL | 业务类型 (如: 'EXPENSE', 'CONTRACT') | | status | VARCHAR(30) | NOT NULL | 审批流状态 (PENDING, APPROVED, REJECTED) | | current_step| INT | | 当前走到的步骤序号 | | ... | | | (通用字段) | 索引: idx_flows_business on (business_id, business_type)
tbl_approval_nodes (审批节点记录表)
职责: 记录审批流中的每一个具体操作。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | flow_id | UUID | NOT NULL, FK -> tbl_approval_flows(id) | 所属审批流实例ID | | approver_id | UUID | NOT NULL, FK -> tbl_users(id) | 审批人ID | | status | VARCHAR(30) | NOT NULL | 节点状态 (APPROVED, REJECTED, TRANSFERRED) | | comment | TEXT | | 审批意见 | | step | INT | | 节点所在步骤序号 | | processed_at | TIMESTAMPTZ | | 处理时间 | | ... | | | (通用字段) |
tbl_files (文件管理表)
职责: 管理系统中上传的所有文件。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | file_name | VARCHAR(255) | NOT NULL | 原始文件名 | | file_path | VARCHAR(500) | NOT NULL | 文件存储路径 (如: '2025/07/xxx.pdf') | | file_size | BIGINT | | 文件大小 (Bytes) | | mime_type | VARCHAR(100) | | 文件MIME类型 | | storage_type| VARCHAR(20) | NOT NULL | 存储类型 (如: 'LOCAL', 'OSS') | | ... | | | (通用字段) |
模块二：全面预算管理 (Budget Management)
tbl_budgets & tbl_budget_items - (同V1.0)
职责: 定义部门年度预算和明细预算。
模块三：支出控制管理 (Expenditure Control)
tbl_expense_applications (报销申请单主表) - (同V1.0)
职责: 记录报销申请。
tbl_expense_details (报销明细表) - (同V1.0)
职责: 记录报销单的每一条费用。
tbl_pre_applications (事前申请单)
职责: 针对差旅、会议、培训等需要事前申请的业务。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | application_code| VARCHAR(50) | UNIQUE, NOT NULL | 事前申请单号 | | applicant_id | UUID | NOT NULL, FK -> tbl_users(id) | 申请人 | | type | VARCHAR(50) | NOT NULL | 事前申请类型 (TRAVEL, MEETING) | | estimated_amount| DECIMAL(18,2) | NOT NULL | 预估总金额 | | status | VARCHAR(30) | NOT NULL | 状态 (PENDING, APPROVED, CLOSED) | | details | JSONB | | 详细信息 (如行程、参会人员) | | ... | | | (通用字段) |
tbl_payments (付款单)
职责: 由财务出纳操作，记录对外支付。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | payment_code | VARCHAR(50) | UNIQUE, NOT NULL | 付款单号 | | source_id | UUID | NOT NULL | 来源单据ID (报销单/合同付款计划) | | source_type | VARCHAR(50) | NOT NULL | 来源类型 (EXPENSE, CONTRACT_PAY) | | payee_name | VARCHAR(255) | NOT NULL | 收款方名称 | | payee_account | VARCHAR(100) | NOT NULL | 收款方账号 | | payee_bank | VARCHAR(255) | NOT NULL | 收款方开户行 | | amount | DECIMAL(18,2) | NOT NULL | 支付金额 | | status | VARCHAR(30) | NOT NULL | 状态 (PENDING, PAID, FAILED) | | paid_at | TIMESTAMPTZ | | 实际支付时间 | | ... | | | (通用字段) |
模块四：采购管理 (Procurement Management)
tbl_suppliers (供应商表)
职责: 维护供应商信息。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | name | VARCHAR(255) | NOT NULL, UNIQUE | 供应商名称 | | credit_code | VARCHAR(100) | UNIQUE | 社会统一信用代码 | | status | SMALLINT | NOT NULL | 状态 (1:合作中, 0:黑名单) | | contact_info| JSONB | | 联系人、地址等信息 | | ... | | | (通用字段) |
tbl_purchase_requisitions (采购需求单)
职责: 业务科室发起采购需求的记录。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | requisition_code| VARCHAR(50) | UNIQUE, NOT NULL | 需求单号 | | applicant_id | UUID | NOT NULL, FK -> tbl_users(id) | 申请人 | | department_id| UUID | NOT NULL, FK -> tbl_departments(id)| 需求部门 | | title | VARCHAR(255) | NOT NULL | 采购事由 | | details | JSONB | | 需求明细 (物品、数量、规格、预估单价) | | total_amount| DECIMAL(18,2) | | 预估总金额 | | ... | | | (通用字段，关联审批流) |
tbl_purchase_orders (采购订单)
职责: 采购计划执行后生成的正式订单。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | order_code | VARCHAR(50) | UNIQUE, NOT NULL | 订单号 | | supplier_id | UUID | NOT NULL, FK -> tbl_suppliers(id) | 供应商ID | | contract_id | UUID | FK -> tbl_contracts(id) | 关联的采购合同ID | | total_amount| DECIMAL(18,2) | NOT NULL | 订单总金额 | | status | VARCHAR(30) | NOT NULL | 状态 (PENDING, DELIVERED, COMPLETED) | | order_details| JSONB | | 订单明细 | | ... | | | (通用字段) |
模块五：合同管理 (Contract Management)
tbl_contracts (合同主表)
职责: 记录合同核心信息。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | contract_code| VARCHAR(50) | UNIQUE, NOT NULL | 合同编号 | | contract_name| VARCHAR(255)| NOT NULL | 合同名称 | | contract_type| VARCHAR(50) | NOT NULL | 合同类型 (采购/服务/基建) | | counterparty_id| UUID| FK -> tbl_suppliers(id) | 对方单位ID (可关联供应商) | | counterparty_name|VARCHAR(255)| NOT NULL | 对方单位名称 (冗余) | | total_amount | DECIMAL(18,2)| NOT NULL | 合同总金额 | | start_date | DATE | | 合同开始日期 | | end_date | DATE | | 合同结束日期 | | status | VARCHAR(30) | NOT NULL | 状态 (DRAFT, PENDING, ACTIVE, COMPLETED) | | ... | | | (通用字段) |
tbl_contract_payment_schedules (合同付款计划表)
职责: 定义合同的分期付款计划。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | contract_id | UUID | NOT NULL, FK -> tbl_contracts(id) | 所属合同 | | phase_name | VARCHAR(100) | | 付款阶段名称 (如: 首笔款, 验收款) | | due_date | DATE | | 预计付款日期 | | amount | DECIMAL(18,2)| NOT NULL | 计划付款金额 | | status | VARCHAR(30) | NOT NULL | 状态 (PENDING, PAID) | | ... | | | (通用字段) |
模块六：资产管理 (Asset Management)
tbl_asset_categories (资产分类表)
职责: 定义资产的分类体系。
tbl_assets (资产卡片表)
职责: 记录每一件固定资产的信息。 | 字段名 | 类型 | 约束 | 备注 | |---|---|---|---| | id | UUID | PK | 唯一标识符 | | asset_code | VARCHAR(50) | UNIQUE, NOT NULL | 资产编码 | | asset_name | VARCHAR(255) | NOT NULL | 资产名称 | | category_id | UUID | FK -> tbl_asset_categories(id)| 资产分类 | | purchase_order_id| UUID| FK -> tbl_purchase_orders(id)| 来源采购订单 | | purchase_date| DATE | | 购置日期 | | purchase_price| DECIMAL(18,2)| | 购置价格 | | status | VARCHAR(30) | NOT NULL | 状态 (IN_USE, IDLE, SCRAPPED) | | owner_dept_id| UUID | FK -> tbl_departments(id) | 使用部门 | | location | VARCHAR(255)| | 存放位置 | | ... | | | (通用字段) |
此设计已涵盖项目所述的核心功能闭环。您可以基于此表结构开始设计 API。例如：

POST /api/v1/expense-applications: 创建报销单，会写入 tbl_expense_applications 和 tbl_expense_details，并冻结 tbl_budget_items 的额度。
GET /api/v1/expense-applications/{id}: 获取报销单详情，会联查 tbl_users, tbl_departments, tbl_expense_details 等表。
GET /api/v1/approvals/todo: 获取我的待办，会查询 tbl_approval_nodes 中 approver_id 是当前用户且状态为待处理的记录。
下一步，我们将基于此数据库设计，具体定义 API 的 Endpoint、Request/Response Body 等。