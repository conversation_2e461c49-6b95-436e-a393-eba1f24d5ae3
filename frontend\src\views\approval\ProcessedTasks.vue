<template>
  <div class="processed-tasks">
    <a-page-header title="已办任务" sub-title="我的已办审批历史" />
    
    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="stats-cards">
      <a-col :xs="24" :sm="6">
        <a-card>
          <a-statistic
            title="本月已办"
            :value="processedStats.thisMonth"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card>
          <a-statistic
            title="通过率"
            :value="processedStats.approvalRate"
            suffix="%"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <LikeOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card>
          <a-statistic
            title="平均处理时长"
            :value="processedStats.avgProcessTime"
            suffix="小时"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <ClockCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card>
          <a-statistic
            title="累计已办"
            :value="processedStats.total"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix>
              <FileTextOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="businessTypeFilter"
            placeholder="业务类型"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="EXPENSE">报销申请</a-select-option>
            <a-select-option value="PRE_APPLICATION">事前申请</a-select-option>
            <a-select-option value="BUDGET_ADJUSTMENT">预算调整</a-select-option>
            <a-select-option value="CONTRACT">合同审批</a-select-option>
            <a-select-option value="PURCHASE">采购申请</a-select-option>
          </a-select>
          <a-select
            v-model:value="actionFilter"
            placeholder="审批结果"
            style="width: 120px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="APPROVED">通过</a-select-option>
            <a-select-option value="REJECTED">拒绝</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="dateRange"
            placeholder="处理时间范围"
            @change="handleFilterChange"
          />
          <a-button @click="exportProcessedTasks">
            <DownloadOutlined />
            导出记录
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'business_type'">
            <a-tag :color="getBusinessTypeColor(record.business_type)">
              {{ getBusinessTypeText(record.business_type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-tag :color="getActionColor(record.action)">
              <CheckOutlined v-if="record.action === 'APPROVED'" />
              <CloseOutlined v-else-if="record.action === 'REJECTED'" />
              {{ getActionText(record.action) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'amount'">
            <span v-if="record.amount" class="amount">¥{{ formatAmount(record.amount) }}</span>
            <span v-else>-</span>
          </template>
          
          <template v-if="column.key === 'process_time'">
            <div class="process-time">
              <div>{{ formatDateTime(record.processed_at) }}</div>
              <div class="duration">{{ getProcessDuration(record.received_at, record.processed_at) }}</div>
            </div>
          </template>
          
          <template v-if="column.key === 'operation'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看详情
              </a-button>
              <a-button type="text" size="small" @click="viewHistory(record)">
                <HistoryOutlined />
                审批历史
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 详情查看模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="已办任务详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentTask">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务ID">{{ currentTask.task_id }}</a-descriptions-item>
          <a-descriptions-item label="业务类型">
            <a-tag :color="getBusinessTypeColor(currentTask.business_type)">
              {{ getBusinessTypeText(currentTask.business_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="业务单号">{{ currentTask.business_code }}</a-descriptions-item>
          <a-descriptions-item label="申请标题" :span="2">{{ currentTask.title }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentTask.applicant_name }}</a-descriptions-item>
          <a-descriptions-item label="申请金额">
            <span v-if="currentTask.amount" class="amount">¥{{ formatAmount(currentTask.amount) }}</span>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="接收时间">{{ formatDateTime(currentTask.received_at) }}</a-descriptions-item>
          <a-descriptions-item label="处理时间">{{ formatDateTime(currentTask.processed_at) }}</a-descriptions-item>
          <a-descriptions-item label="处理结果">
            <a-tag :color="getActionColor(currentTask.action)">
              <CheckOutlined v-if="currentTask.action === 'APPROVED'" />
              <CloseOutlined v-else-if="currentTask.action === 'REJECTED'" />
              {{ getActionText(currentTask.action) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="处理时长">
            {{ getProcessDuration(currentTask.received_at, currentTask.processed_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="审批意见" :span="2">
            {{ currentTask.comment || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 审批历史模态框 -->
    <a-modal
      v-model:open="historyModalVisible"
      title="审批历史"
      width="800px"
      :footer="null"
    >
      <div v-if="currentTask">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="业务单号">{{ currentTask.business_code }}</a-descriptions-item>
          <a-descriptions-item label="申请标题">{{ currentTask.title }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>审批流程</a-divider>
        
        <a-timeline>
          <a-timeline-item 
            v-for="(history, index) in approvalHistory" 
            :key="index"
            :color="getHistoryColor(history.action)"
          >
            <template #dot>
              <CheckCircleOutlined v-if="history.action === 'APPROVED'" />
              <CloseCircleOutlined v-else-if="history.action === 'REJECTED'" />
              <FileTextOutlined v-else-if="history.action === 'SUBMITTED'" />
              <ClockCircleOutlined v-else />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">{{ history.step_name }}</div>
              <div class="timeline-desc">
                <strong>{{ history.approver_name }}</strong> {{ getActionText(history.action) }}
                <span v-if="history.comment">：{{ history.comment }}</span>
              </div>
              <div class="timeline-time">{{ formatDateTime(history.processed_at) }}</div>
              <div v-if="history.process_duration" class="timeline-duration">
                处理时长：{{ history.process_duration }}
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  LikeOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  HistoryOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import type { ProcessedTask } from '@/types'

const loading = ref(false)
const businessTypeFilter = ref<string>()
const actionFilter = ref<string>()
const dateRange = ref()
const tableData = ref<ProcessedTask[]>([])
const detailModalVisible = ref(false)
const historyModalVisible = ref(false)
const currentTask = ref<ProcessedTask | null>(null)
const approvalHistory = ref([])

const processedStats = reactive({
  thisMonth: 45,
  approvalRate: 92.3,
  avgProcessTime: 2.5,
  total: 328
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '业务类型', key: 'business_type', width: 100 },
  { title: '业务单号', dataIndex: 'business_code', key: 'business_code', width: 150 },
  { title: '申请标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '申请人', dataIndex: 'applicant_name', key: 'applicant_name', width: 100 },
  { title: '申请金额', key: 'amount', width: 120 },
  { title: '处理结果', key: 'action', width: 100 },
  { title: '处理时间', key: 'process_time', width: 150 },
  { title: '操作', key: 'operation', width: 150, fixed: 'right' }
]

const getBusinessTypeColor = (type: string) => {
  const colors = {
    'EXPENSE': 'blue',
    'PRE_APPLICATION': 'green',
    'BUDGET_ADJUSTMENT': 'orange',
    'CONTRACT': 'purple',
    'PURCHASE': 'cyan'
  }
  return colors[type] || 'default'
}

const getBusinessTypeText = (type: string) => {
  const texts = {
    'EXPENSE': '报销申请',
    'PRE_APPLICATION': '事前申请',
    'BUDGET_ADJUSTMENT': '预算调整',
    'CONTRACT': '合同审批',
    'PURCHASE': '采购申请'
  }
  return texts[type] || type
}

const getActionColor = (action: string) => {
  const colors = {
    'APPROVED': 'success',
    'REJECTED': 'error'
  }
  return colors[action] || 'default'
}

const getActionText = (action: string) => {
  const texts = {
    'APPROVED': '通过',
    'REJECTED': '拒绝',
    'SUBMITTED': '提交',
    'PENDING': '待审批'
  }
  return texts[action] || action
}

const getHistoryColor = (action: string) => {
  const colors = {
    'APPROVED': 'green',
    'REJECTED': 'red',
    'SUBMITTED': 'blue',
    'PENDING': 'gray'
  }
  return colors[action] || 'gray'
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString()
}

const getProcessDuration = (receivedAt: string, processedAt: string) => {
  const received = new Date(receivedAt)
  const processed = new Date(processedAt)
  const diffHours = Math.floor((processed.getTime() - received.getTime()) / (1000 * 60 * 60))
  
  if (diffHours < 1) return '不到1小时'
  if (diffHours < 24) return `${diffHours}小时`
  
  const diffDays = Math.floor(diffHours / 24)
  const remainHours = diffHours % 24
  return remainHours > 0 ? `${diffDays}天${remainHours}小时` : `${diffDays}天`
}

const loadProcessedTasks = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取已办任务列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        task_id: 'TASK20240722001',
        flow_id: 'FLOW001',
        business_type: 'EXPENSE',
        business_id: 'BX20240722001',
        business_code: 'BX20240722001',
        title: '王五的医疗器械采购报销',
        applicant_name: '王五',
        amount: 15600,
        action: 'APPROVED',
        comment: '符合预算要求，同意报销',
        received_at: '2024-07-22 09:30:00',
        processed_at: '2024-07-22 11:15:00'
      },
      {
        id: '2',
        task_id: 'TASK20240721001',
        flow_id: 'FLOW002',
        business_type: 'PRE_APPLICATION',
        business_id: 'PRE20240721001',
        business_code: 'PRE20240721001',
        title: '赵六的培训申请',
        applicant_name: '赵六',
        amount: 12000,
        action: 'REJECTED',
        comment: '培训内容与岗位要求不符，建议重新申请',
        received_at: '2024-07-21 14:20:00',
        processed_at: '2024-07-21 16:45:00'
      },
      {
        id: '3',
        task_id: 'TASK20240720001',
        flow_id: 'FLOW003',
        business_type: 'BUDGET_ADJUSTMENT',
        business_id: 'ADJ20240720001',
        business_code: 'ADJ20240720001',
        title: '设备维护预算调整',
        applicant_name: '孙七',
        amount: 50000,
        action: 'APPROVED',
        comment: '设备维护确实需要，同意调整',
        received_at: '2024-07-20 10:00:00',
        processed_at: '2024-07-20 15:30:00'
      }
    ]
    pagination.total = 3
  } catch (error) {
    message.error('加载已办任务失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadProcessedTasks()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadProcessedTasks()
}

const viewDetails = (task: ProcessedTask) => {
  currentTask.value = task
  detailModalVisible.value = true
}

const viewHistory = async (task: ProcessedTask) => {
  currentTask.value = task
  
  // TODO: 加载审批历史
  approvalHistory.value = [
    {
      step_name: '申请提交',
      approver_name: task.applicant_name,
      action: 'SUBMITTED',
      comment: '提交申请',
      processed_at: task.received_at,
      process_duration: null
    },
    {
      step_name: '部门审批',
      approver_name: '部门主任',
      action: 'APPROVED',
      comment: '部门同意',
      processed_at: '2024-07-22 10:30:00',
      process_duration: '1小时'
    },
    {
      step_name: '财务审批',
      approver_name: '当前用户',
      action: task.action,
      comment: task.comment,
      processed_at: task.processed_at,
      process_duration: getProcessDuration(task.received_at, task.processed_at)
    }
  ]
  
  historyModalVisible.value = true
}

const exportProcessedTasks = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

onMounted(() => {
  loadProcessedTasks()
})
</script>

<style scoped>
.processed-tasks {
  padding: 24px;
}

.stats-cards {
  margin-bottom: 24px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.process-time {
  line-height: 1.4;
}

.duration {
  font-size: 12px;
  color: #999;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #666;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.timeline-duration {
  font-size: 12px;
  color: #1890ff;
}
</style>
