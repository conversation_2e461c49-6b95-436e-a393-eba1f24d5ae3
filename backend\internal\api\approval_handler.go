package api

import (
	"hospital-management/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type ApprovalHandler struct {
	approvalService *service.ApprovalService
}

func NewApprovalHandler(approvalService *service.ApprovalService) *ApprovalHandler {
	return &ApprovalHandler{approvalService: approvalService}
}

// GetTasks 查询我的待办任务
func (h *ApprovalHandler) GetTasks(c *gin.Context) {
	page, _ := strconv.Atoi(c.Default<PERSON><PERSON>y("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("pageSize", "10"))
	businessType := c.Query("businessType")

	userID := c.GetString("user_id")
	tasks, total, err := h.approvalService.GetTasksByApprover(userID, page, pageSize, businessType)
	if err != nil {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "查询失败",
			"data":    nil,
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "查询成功",
		"data": gin.H{
			"total": total,
			"list":  tasks,
		},
	})
}

// ProcessTask 执行审批操作
func (h *ApprovalHandler) ProcessTask(c *gin.Context) {
	var req struct {
		TaskID  string `json:"task_id" binding:"required"`
		Action  string `json:"action" binding:"required"` // APPROVE, REJECT
		Comment string `json:"comment"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	if _, err := uuid.Parse(req.TaskID); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "任务ID格式错误",
			"data":    nil,
		})
		return
	}

	userID := c.GetString("user_id")
	err := h.approvalService.ProcessTask(req.TaskID, userID, req.Action, req.Comment)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "审批成功",
		"data":    nil,
	})
}

// GetProcessedTasks 查询已办任务
func (h *ApprovalHandler) GetProcessedTasks(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	businessType := c.Query("businessType")

	userID := c.GetString("user_id")
	tasks, total, err := h.approvalService.GetProcessedTasksByApprover(userID, page, pageSize, businessType)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "查询失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "查询成功",
		"data": gin.H{
			"total": total,
			"list":  tasks,
		},
	})
}

// GetFlowHistory 获取审批流历史
func (h *ApprovalHandler) GetFlowHistory(c *gin.Context) {
	businessID := c.Param("businessId")
	businessType := c.Query("businessType")

	if _, err := uuid.Parse(businessID); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "业务ID格式错误",
			"data":    nil,
		})
		return
	}

	history, err := h.approvalService.GetFlowHistory(businessID, businessType)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "查询失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "查询成功",
		"data":    history,
	})
}
