<template>
  <div class="budget-monitor">
    <a-page-header title="预算执行监控" sub-title="预算使用情况的实时监控" />
    
    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="stats-cards">
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="预算总额"
            :value="budgetStats.totalBudget"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="已使用金额"
            :value="budgetStats.usedAmount"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#f5222d' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="冻结金额"
            :value="budgetStats.frozenAmount"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#fa8c16' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="可用金额"
            :value="budgetStats.availableAmount"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#52c41a' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 筛选条件 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="预算年度">
          <a-select
            v-model:value="filterForm.year"
            placeholder="选择年度"
            style="width: 120px"
            @change="handleFilterChange"
          >
            <a-select-option v-for="year in yearOptions" :key="year" :value="year">
              {{ year }}年
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="部门">
          <a-tree-select
            v-model:value="filterForm.departmentId"
            :tree-data="departmentOptions"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="选择部门"
            style="width: 200px"
            allow-clear
            @change="handleFilterChange"
          />
        </a-form-item>
        
        <a-form-item label="预算分类">
          <a-select
            v-model:value="filterForm.category"
            placeholder="选择分类"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="人员费用">人员费用</a-select-option>
            <a-select-option value="设备费用">设备费用</a-select-option>
            <a-select-option value="材料费用">材料费用</a-select-option>
            <a-select-option value="管理费用">管理费用</a-select-option>
            <a-select-option value="其他费用">其他费用</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="预警状态">
          <a-select
            v-model:value="filterForm.alertLevel"
            placeholder="选择预警状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="normal">正常</a-select-option>
            <a-select-option value="warning">预警</a-select-option>
            <a-select-option value="danger">超支</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="exportReport">
            <DownloadOutlined />
            导出报告
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 预算执行表格 -->
    <a-card title="预算执行详情">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        row-key="id"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'budget_amount'">
            <span class="amount">¥{{ formatAmount(record.budget_amount) }}</span>
          </template>
          
          <template v-if="column.key === 'used_amount'">
            <span class="amount used">¥{{ formatAmount(record.used_amount) }}</span>
          </template>
          
          <template v-if="column.key === 'frozen_amount'">
            <span class="amount frozen">¥{{ formatAmount(record.frozen_amount) }}</span>
          </template>
          
          <template v-if="column.key === 'available_amount'">
            <span class="amount available">¥{{ formatAmount(record.available_amount) }}</span>
          </template>
          
          <template v-if="column.key === 'usage_rate'">
            <div class="usage-progress">
              <a-progress
                :percent="getUsageRate(record)"
                size="small"
                :status="getProgressStatus(record)"
              />
              <span class="usage-text">{{ getUsageRate(record) }}%</span>
            </div>
          </template>
          
          <template v-if="column.key === 'alert_level'">
            <a-tag :color="getAlertColor(record)">
              {{ getAlertText(record) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看详情
              </a-button>
              <a-button type="text" size="small" @click="viewHistory(record)">
                <HistoryOutlined />
                使用历史
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 预算使用趋势图表 -->
    <a-row :gutter="[16, 16]" class="charts-section">
      <a-col :xs="24" :lg="12">
        <a-card title="预算使用趋势">
          <div class="chart-container">
            <div class="chart-placeholder">
              预算使用趋势图表
              <br />
              <small>显示各月份预算使用情况</small>
            </div>
          </div>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :lg="12">
        <a-card title="预算分类占比">
          <div class="chart-container">
            <div class="chart-placeholder">
              预算分类饼图
              <br />
              <small>显示各类别预算占比</small>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 预警提醒 -->
    <a-card title="预警提醒" class="alert-card">
      <a-list
        :data-source="alertList"
        item-layout="horizontal"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-avatar :style="{ backgroundColor: getAlertAvatarColor(item.level) }">
                  <WarningOutlined v-if="item.level === 'warning'" />
                  <ExclamationCircleOutlined v-else-if="item.level === 'danger'" />
                  <CheckCircleOutlined v-else />
                </a-avatar>
              </template>
              <template #title>
                <span>{{ item.title }}</span>
                <a-tag :color="getAlertColor({ alert_level: item.level })" size="small" style="margin-left: 8px">
                  {{ getAlertText({ alert_level: item.level }) }}
                </a-tag>
              </template>
              <template #description>
                {{ item.description }}
              </template>
            </a-list-item-meta>
            <template #actions>
              <span class="alert-time">{{ item.time }}</span>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  DownloadOutlined,
  EyeOutlined,
  HistoryOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import type { BudgetItem, Department } from '@/types'

const loading = ref(false)
const tableData = ref<BudgetItem[]>([])
const departmentOptions = ref<Department[]>([])

const budgetStats = reactive({
  totalBudget: 5000000,
  usedAmount: 2850000,
  frozenAmount: 350000,
  availableAmount: 1800000
})

const filterForm = reactive({
  year: new Date().getFullYear(),
  departmentId: undefined,
  category: undefined,
  alertLevel: undefined
})

const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  return [currentYear - 1, currentYear, currentYear + 1]
})

const columns = [
  { title: '项目名称', dataIndex: 'item_name', key: 'item_name', width: 150, fixed: 'left' },
  { title: '项目编码', dataIndex: 'item_code', key: 'item_code', width: 120 },
  { title: '分类', dataIndex: 'category', key: 'category', width: 100 },
  { title: '部门', dataIndex: ['budget', 'department', 'name'], key: 'department', width: 120 },
  { title: '预算金额', key: 'budget_amount', width: 120 },
  { title: '已使用', key: 'used_amount', width: 120 },
  { title: '冻结金额', key: 'frozen_amount', width: 120 },
  { title: '可用金额', key: 'available_amount', width: 120 },
  { title: '使用率', key: 'usage_rate', width: 150 },
  { title: '预警状态', key: 'alert_level', width: 100 },
  { title: '操作', key: 'action', width: 150, fixed: 'right' }
]

const alertList = ref([
  {
    level: 'danger',
    title: '内科医疗设备采购预算超支',
    description: '当前使用率105%，已超出预算50,000元，请及时处理',
    time: '2024-07-23 14:30'
  },
  {
    level: 'warning',
    title: '心内科医用耗材预算预警',
    description: '当前使用率85%，预计本月底将达到预算上限',
    time: '2024-07-23 10:15'
  },
  {
    level: 'warning',
    title: '外科培训费用预算预警',
    description: '当前使用率80%，建议合理安排后续培训计划',
    time: '2024-07-22 16:45'
  }
])

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const getUsageRate = (record: BudgetItem) => {
  if (!record.budget_amount) return 0
  return Math.round((record.used_amount / record.budget_amount) * 100)
}

const getProgressStatus = (record: BudgetItem) => {
  const rate = getUsageRate(record)
  if (rate > 100) return 'exception'
  if (rate > 80) return 'active'
  return 'normal'
}

const getAlertColor = (record: any) => {
  const level = record.alert_level || calculateAlertLevel(record)
  const colors = {
    'normal': 'green',
    'warning': 'orange',
    'danger': 'red'
  }
  return colors[level] || 'default'
}

const getAlertText = (record: any) => {
  const level = record.alert_level || calculateAlertLevel(record)
  const texts = {
    'normal': '正常',
    'warning': '预警',
    'danger': '超支'
  }
  return texts[level] || '正常'
}

const getAlertAvatarColor = (level: string) => {
  const colors = {
    'normal': '#52c41a',
    'warning': '#fa8c16',
    'danger': '#f5222d'
  }
  return colors[level] || '#52c41a'
}

const calculateAlertLevel = (record: BudgetItem) => {
  const rate = getUsageRate(record)
  if (rate > 100) return 'danger'
  if (rate > 80) return 'warning'
  return 'normal'
}

const loadBudgetItems = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取预算项目数据
    // 临时数据
    tableData.value = [
      {
        id: '1',
        budget_id: '1',
        item_name: '医疗设备采购',
        item_code: 'YLSB001',
        category: '设备费用',
        budget_amount: 500000,
        used_amount: 525000,
        frozen_amount: 50000,
        available_amount: -75000,
        description: '心电图机、血压计等医疗设备采购',
        alert_level: 'danger',
        budget: {
          department: { name: '内科' }
        }
      },
      {
        id: '2',
        budget_id: '1',
        item_name: '医用耗材',
        item_code: 'YYHC001',
        category: '材料费用',
        budget_amount: 300000,
        used_amount: 255000,
        frozen_amount: 20000,
        available_amount: 25000,
        description: '一次性医用耗材采购',
        alert_level: 'warning',
        budget: {
          department: { name: '心内科' }
        }
      },
      {
        id: '3',
        budget_id: '1',
        item_name: '培训费用',
        item_code: 'PXFY001',
        category: '管理费用',
        budget_amount: 200000,
        used_amount: 160000,
        frozen_amount: 0,
        available_amount: 40000,
        description: '医护人员培训费用',
        alert_level: 'warning',
        budget: {
          department: { name: '外科' }
        }
      },
      {
        id: '4',
        budget_id: '2',
        item_name: '办公用品',
        item_code: 'BGYP001',
        category: '管理费用',
        budget_amount: 50000,
        used_amount: 25000,
        frozen_amount: 5000,
        available_amount: 20000,
        description: '日常办公用品采购',
        alert_level: 'normal',
        budget: {
          department: { name: '行政部' }
        }
      }
    ]
  } catch (error) {
    message.error('加载预算监控数据失败')
  } finally {
    loading.value = false
  }
}

const loadDepartments = async () => {
  try {
    // TODO: 调用API获取部门数据
    departmentOptions.value = [
      {
        id: '1',
        name: '医院总部',
        code: 'HQ',
        children: [
          { id: '2', name: '内科', code: 'NK' },
          { id: '3', name: '心内科', code: 'XNK' },
          { id: '4', name: '外科', code: 'WK' },
          { id: '5', name: '行政部', code: 'XZB' }
        ]
      }
    ]
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

const handleFilterChange = () => {
  loadBudgetItems()
}

const viewDetails = (record: BudgetItem) => {
  // TODO: 实现查看详情功能
  message.info('查看详情功能开发中')
}

const viewHistory = (record: BudgetItem) => {
  // TODO: 实现查看使用历史功能
  message.info('查看使用历史功能开发中')
}

const exportReport = () => {
  // TODO: 实现导出报告功能
  message.info('导出报告功能开发中')
}

onMounted(() => {
  loadBudgetItems()
  loadDepartments()
})
</script>

<style scoped>
.budget-monitor {
  padding: 24px;
}

.stats-cards {
  margin-bottom: 24px;
}

.filter-card {
  margin-bottom: 24px;
}

.charts-section {
  margin: 24px 0;
}

.chart-container {
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  color: #999;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.alert-card {
  margin-top: 24px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.amount.used {
  color: #f5222d;
}

.amount.frozen {
  color: #fa8c16;
}

.amount.available {
  color: #52c41a;
}

.usage-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-text {
  font-size: 12px;
  color: #666;
  min-width: 35px;
}

.alert-time {
  font-size: 12px;
  color: #999;
}
</style>
