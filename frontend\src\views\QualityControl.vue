<template>
  <div class="quality-control">
    <h1>质量控制</h1>
    <p>质量指标管理和监控</p>
    
    <el-card>
      <template #header>
        <div class="card-header">
          <span>质量指标列表</span>
          <el-button type="primary" @click="handleAdd">新增指标</el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="指标名称" />
        <el-table-column prop="code" label="指标编码" />
        <el-table-column prop="category" label="分类" />
        <el-table-column prop="target_value" label="目标值" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const tableData = ref([
  {
    id: 1,
    name: '患者满意度',
    code: 'PATIENT_SATISFACTION',
    category: '服务质量',
    target_value: 95,
    unit: '%',
    status: 1
  },
  {
    id: 2,
    name: '医疗事故发生率',
    code: 'MEDICAL_ACCIDENT_RATE',
    category: '安全质量',
    target_value: 0.1,
    unit: '%',
    status: 1
  }
])

const handleAdd = () => {
  ElMessage.info('新增指标功能待实现')
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑指标: ${row.name}`)
}

const handleDelete = (row: any) => {
  ElMessage.info(`删除指标: ${row.name}`)
}

onMounted(() => {
  console.log('QualityControl mounted')
})
</script>

<style scoped>
.quality-control {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
