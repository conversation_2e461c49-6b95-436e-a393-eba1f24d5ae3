<template>
  <div class="receipt-management">
    <a-card>
      <template #title>
        <span>入库验收管理</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            新建入库单
          </a-button>
          <a-button @click="loadStatistics">
            <template #icon><BarChartOutlined /></template>
            统计分析
          </a-button>
        </a-space>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-form">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.status"
              placeholder="选择状态"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="RECEIVED">已收货</a-select-option>
              <a-select-option value="INSPECTING">质检中</a-select-option>
              <a-select-option value="PASSED">验收通过</a-select-option>
              <a-select-option value="REJECTED">验收不通过</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.supplier_id"
              placeholder="选择供应商"
              allow-clear
              show-search
              @change="handleSearch"
            >
              <a-select-option
                v-for="supplier in supplierOptions"
                :key="supplier.id"
                :value="supplier.id"
              >
                {{ supplier.name }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="8">
            <a-range-picker
              v-model:value="dateRange"
              @change="handleDateChange"
            />
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'total_amount'">
            <span class="amount">¥{{ record.total_amount?.toFixed(2) }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetails(record)">
                详情
              </a-button>
              <a-button
                v-if="record.status === 'RECEIVED' || record.status === 'INSPECTING'"
                type="link"
                size="small"
                @click="showInspectModal(record)"
              >
                质检
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="printReceipt(record)">
                      <PrinterOutlined /> 打印
                    </a-menu-item>
                    <a-menu-item @click="exportReceipt(record)">
                      <ExportOutlined /> 导出
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新建入库单弹窗 -->
    <a-modal
      v-model:open="addModalVisible"
      title="新建入库单"
      width="1000px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="采购订单" name="order_id">
              <a-select
                v-model:value="formData.order_id"
                placeholder="选择采购订单"
                show-search
                @change="handleOrderChange"
              >
                <a-select-option
                  v-for="order in orderOptions"
                  :key="order.id"
                  :value="order.id"
                >
                  {{ order.order_code }} - {{ order.supplier?.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="收货人" name="receiver_id">
              <a-select
                v-model:value="formData.receiver_id"
                placeholder="选择收货人"
                show-search
              >
                <a-select-option
                  v-for="user in userOptions"
                  :key="user.id"
                  :value="user.id"
                >
                  {{ user.real_name }} ({{ user.username }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="收货日期" name="receipt_date">
              <a-date-picker
                v-model:value="formData.receipt_date"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备注">
              <a-textarea
                v-model:value="formData.remark"
                placeholder="请输入备注"
                :rows="2"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 入库明细 -->
        <a-form-item label="入库明细">
          <a-table
            :columns="itemColumns"
            :data-source="formData.items"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'received_quantity'">
                <a-input-number
                  v-model:value="record.received_quantity"
                  :min="0"
                  :max="record.ordered_quantity"
                  @change="calculateItemTotal(record)"
                />
              </template>
              <template v-else-if="column.key === 'batch_number'">
                <a-input v-model:value="record.batch_number" />
              </template>
              <template v-else-if="column.key === 'expiry_date'">
                <a-date-picker v-model:value="record.expiry_date" />
              </template>
              <template v-else-if="column.key === 'total_price'">
                <span class="amount">¥{{ record.total_price?.toFixed(2) }}</span>
              </template>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 质检弹窗 -->
    <a-modal
      v-model:open="inspectModalVisible"
      title="质检验收"
      width="1000px"
      :confirm-loading="inspectLoading"
      @ok="handleInspect"
      @cancel="inspectModalVisible = false"
    >
      <a-form
        ref="inspectFormRef"
        :model="inspectFormData"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="质检员" name="inspector_id">
              <a-select
                v-model:value="inspectFormData.inspector_id"
                placeholder="选择质检员"
                show-search
              >
                <a-select-option
                  v-for="user in userOptions"
                  :key="user.id"
                  :value="user.id"
                >
                  {{ user.real_name }} ({{ user.username }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="质检备注">
              <a-textarea
                v-model:value="inspectFormData.remark"
                placeholder="请输入质检备注"
                :rows="2"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 质检明细 -->
        <a-form-item label="质检明细">
          <a-table
            :columns="inspectColumns"
            :data-source="inspectFormData.items"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'qualified_quantity'">
                <a-input-number
                  v-model:value="record.qualified_quantity"
                  :min="0"
                  :max="record.received_quantity"
                />
              </template>
              <template v-else-if="column.key === 'inspect_result'">
                <a-select v-model:value="record.inspect_result">
                  <a-select-option value="PASSED">合格</a-select-option>
                  <a-select-option value="REJECTED">不合格</a-select-option>
                </a-select>
              </template>
              <template v-else-if="column.key === 'inspect_remark'">
                <a-input v-model:value="record.inspect_remark" />
              </template>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="入库单详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentReceipt">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="入库单号">
            {{ currentReceipt.receipt_code }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentReceipt.status)">
              {{ getStatusText(currentReceipt.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="供应商">
            {{ currentReceipt.supplier?.name }}
          </a-descriptions-item>
          <a-descriptions-item label="收货人">
            {{ currentReceipt.receiver?.real_name }}
          </a-descriptions-item>
          <a-descriptions-item label="收货日期">
            {{ currentReceipt.receipt_date }}
          </a-descriptions-item>
          <a-descriptions-item label="质检日期">
            {{ currentReceipt.inspect_date || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="总金额">
            <span class="amount">¥{{ currentReceipt.total_amount?.toFixed(2) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="质检员">
            {{ currentReceipt.inspector?.real_name || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>入库明细</a-divider>
        <a-table
          :columns="detailColumns"
          :data-source="currentReceipt.receipt_items"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'inspect_result'">
              <a-tag :color="getInspectResultColor(record.inspect_result)">
                {{ getInspectResultText(record.inspect_result) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'total_price'">
              <span class="amount">¥{{ record.total_price?.toFixed(2) }}</span>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  BarChartOutlined,
  PrinterOutlined,
  ExportOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getReceiptList,
  getReceiptById,
  createReceipt,
  inspectReceipt,
  getReceiptStatistics,
  printReceipt as printReceiptApi,
  exportReceipt as exportReceiptApi,
  type ReceiptRecord,
  type CreateReceiptRequest,
  type InspectReceiptRequest
} from '@/api/receipt'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const inspectLoading = ref(false)
const tableData = ref<ReceiptRecord[]>([])
const addModalVisible = ref(false)
const inspectModalVisible = ref(false)
const detailModalVisible = ref(false)
const currentReceipt = ref<ReceiptRecord | null>(null)

// 搜索表单
const searchForm = reactive({
  status: '',
  supplier_id: '',
  start_date: '',
  end_date: ''
})

const dateRange = ref<[Dayjs, Dayjs] | null>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '入库单号',
    dataIndex: 'receipt_code',
    key: 'receipt_code',
    width: 150
  },
  {
    title: '供应商',
    dataIndex: ['supplier', 'name'],
    key: 'supplier_name',
    width: 200
  },
  {
    title: '收货人',
    dataIndex: ['receiver', 'real_name'],
    key: 'receiver_name',
    width: 120
  },
  {
    title: '收货日期',
    dataIndex: 'receipt_date',
    key: 'receipt_date',
    width: 120
  },
  {
    title: '总金额',
    dataIndex: 'total_amount',
    key: 'total_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '质检员',
    dataIndex: ['inspector', 'real_name'],
    key: 'inspector_name',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 新建表单数据
const formData = reactive<CreateReceiptRequest & { items: any[] }>({
  order_id: '',
  receiver_id: '',
  receipt_date: '',
  items: [],
  remark: ''
})

// 表单验证规则
const formRules = {
  order_id: [{ required: true, message: '请选择采购订单' }],
  receiver_id: [{ required: true, message: '请选择收货人' }],
  receipt_date: [{ required: true, message: '请选择收货日期' }]
}

// 质检表单数据
const inspectFormData = reactive<InspectReceiptRequest & { items: any[] }>({
  inspector_id: '',
  items: [],
  remark: ''
})

// 选项数据
const supplierOptions = ref<any[]>([])
const orderOptions = ref<any[]>([])
const userOptions = ref<any[]>([])

// 表单引用
const formRef = ref()
const inspectFormRef = ref()

// 入库明细列配置
const itemColumns: TableColumnsType = [
  { title: '物品名称', dataIndex: 'item_name', key: 'item_name' },
  { title: '规格型号', dataIndex: 'item_spec', key: 'item_spec' },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
  { title: '订单数量', dataIndex: 'ordered_quantity', key: 'ordered_quantity', width: 100 },
  { title: '实收数量', dataIndex: 'received_quantity', key: 'received_quantity', width: 120 },
  { title: '单价', dataIndex: 'unit_price', key: 'unit_price', width: 100 },
  { title: '总价', dataIndex: 'total_price', key: 'total_price', width: 120 },
  { title: '批次号', dataIndex: 'batch_number', key: 'batch_number', width: 120 },
  { title: '有效期', dataIndex: 'expiry_date', key: 'expiry_date', width: 120 }
]

// 质检明细列配置
const inspectColumns: TableColumnsType = [
  { title: '物品名称', dataIndex: 'item_name', key: 'item_name' },
  { title: '实收数量', dataIndex: 'received_quantity', key: 'received_quantity', width: 100 },
  { title: '合格数量', dataIndex: 'qualified_quantity', key: 'qualified_quantity', width: 120 },
  { title: '质检结果', dataIndex: 'inspect_result', key: 'inspect_result', width: 120 },
  { title: '质检备注', dataIndex: 'inspect_remark', key: 'inspect_remark', width: 200 }
]

// 详情明细列配置
const detailColumns: TableColumnsType = [
  { title: '物品名称', dataIndex: 'item_name', key: 'item_name' },
  { title: '规格型号', dataIndex: 'item_spec', key: 'item_spec' },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
  { title: '订单数量', dataIndex: 'ordered_quantity', key: 'ordered_quantity', width: 100 },
  { title: '实收数量', dataIndex: 'received_quantity', key: 'received_quantity', width: 100 },
  { title: '合格数量', dataIndex: 'qualified_quantity', key: 'qualified_quantity', width: 100 },
  { title: '单价', dataIndex: 'unit_price', key: 'unit_price', width: 100 },
  { title: '总价', dataIndex: 'total_price', key: 'total_price', width: 120 },
  { title: '批次号', dataIndex: 'batch_number', key: 'batch_number' },
  { title: '质检结果', dataIndex: 'inspect_result', key: 'inspect_result', width: 100 }
]

// 方法定义
const loadReceipts = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }

    const response = await getReceiptList(params)
    if (response.data.success) {
      tableData.value = response.data.data.items
      pagination.total = response.data.data.total
    }
  } catch (error) {
    message.error('加载入库单列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadReceipts()
}

const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    searchForm.start_date = dates[0].format('YYYY-MM-DD')
    searchForm.end_date = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.start_date = ''
    searchForm.end_date = ''
  }
  handleSearch()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadReceipts()
}

const showAddModal = () => {
  addModalVisible.value = true
  // 重置表单数据
  Object.assign(formData, {
    order_id: '',
    receiver_id: '',
    receipt_date: '',
    items: [],
    remark: ''
  })
}

const handleOrderChange = (orderId: string) => {
  // TODO: 根据选择的订单加载订单明细
  const selectedOrder = orderOptions.value.find(order => order.id === orderId)
  if (selectedOrder && selectedOrder.order_details) {
    try {
      const orderDetails = JSON.parse(selectedOrder.order_details)
      formData.items = orderDetails.map((item: any) => ({
        ...item,
        received_quantity: item.ordered_quantity,
        total_price: item.ordered_quantity * item.unit_price,
        batch_number: '',
        expiry_date: null
      }))
    } catch (error) {
      console.error('解析订单明细失败:', error)
    }
  }
}

const calculateItemTotal = (record: any) => {
  record.total_price = record.received_quantity * record.unit_price
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (formData.items.length === 0) {
      message.error('请至少添加一个入库项目')
      return
    }

    submitLoading.value = true

    const submitData = {
      ...formData,
      receipt_date: dayjs(formData.receipt_date).format('YYYY-MM-DD'),
      items: formData.items.map(item => ({
        ...item,
        expiry_date: item.expiry_date ? dayjs(item.expiry_date).format('YYYY-MM-DD') : undefined
      }))
    }

    const response = await createReceipt(submitData)
    if (response.data.success) {
      message.success('入库单创建成功')
      addModalVisible.value = false
      loadReceipts()
    }
  } catch (error) {
    console.error('创建入库单失败:', error)
    message.error('创建入库单失败')
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  addModalVisible.value = false
  formRef.value?.resetFields()
}

const showInspectModal = (record: ReceiptRecord) => {
  currentReceipt.value = record
  inspectModalVisible.value = true

  // 初始化质检表单数据
  Object.assign(inspectFormData, {
    inspector_id: '',
    items: record.receipt_items.map(item => ({
      item_id: item.id,
      qualified_quantity: item.received_quantity,
      inspect_result: 'PASSED',
      inspect_remark: ''
    })),
    remark: ''
  })
}

const handleInspect = async () => {
  try {
    if (!inspectFormData.inspector_id) {
      message.error('请选择质检员')
      return
    }

    inspectLoading.value = true

    const response = await inspectReceipt(currentReceipt.value!.id, inspectFormData)
    if (response.data.success) {
      message.success('质检完成')
      inspectModalVisible.value = false
      loadReceipts()
    }
  } catch (error) {
    console.error('质检失败:', error)
    message.error('质检失败')
  } finally {
    inspectLoading.value = false
  }
}

const viewDetails = async (record: ReceiptRecord) => {
  try {
    const response = await getReceiptById(record.id)
    if (response.data.success) {
      currentReceipt.value = response.data.data
      detailModalVisible.value = true
    }
  } catch (error) {
    message.error('获取入库单详情失败')
  }
}

const loadStatistics = async () => {
  try {
    const response = await getReceiptStatistics(searchForm.start_date, searchForm.end_date)
    if (response.data.success) {
      // TODO: 显示统计数据
      message.info('统计功能开发中')
    }
  } catch (error) {
    message.error('获取统计数据失败')
  }
}

const printReceipt = async (record: ReceiptRecord) => {
  try {
    const response = await printReceiptApi(record.id)
    // TODO: 处理打印
    message.info('打印功能开发中')
  } catch (error) {
    message.error('打印失败')
  }
}

const exportReceipt = async (record: ReceiptRecord) => {
  try {
    const response = await exportReceiptApi(record.id)
    // TODO: 处理导出
    message.info('导出功能开发中')
  } catch (error) {
    message.error('导出失败')
  }
}

// 工具方法
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'RECEIVED': 'blue',
    'INSPECTING': 'orange',
    'PASSED': 'green',
    'REJECTED': 'red'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'RECEIVED': '已收货',
    'INSPECTING': '质检中',
    'PASSED': '验收通过',
    'REJECTED': '验收不通过'
  }
  return textMap[status] || status
}

const getInspectResultColor = (result: string) => {
  const colorMap: Record<string, string> = {
    'PENDING': 'default',
    'PASSED': 'green',
    'REJECTED': 'red'
  }
  return colorMap[result] || 'default'
}

const getInspectResultText = (result: string) => {
  const textMap: Record<string, string> = {
    'PENDING': '待质检',
    'PASSED': '合格',
    'REJECTED': '不合格'
  }
  return textMap[result] || result
}

// 加载基础数据
const loadSuppliers = async () => {
  try {
    // TODO: 调用供应商API
    supplierOptions.value = [
      { id: '1', name: '供应商A', credit_code: '123456789' },
      { id: '2', name: '供应商B', credit_code: '987654321' }
    ]
  } catch (error) {
    message.error('加载供应商数据失败')
  }
}

const loadOrders = async () => {
  try {
    // TODO: 调用采购订单API
    orderOptions.value = [
      {
        id: '1',
        order_code: 'PO202501001',
        supplier: { name: '供应商A' },
        order_details: JSON.stringify([
          {
            item_name: '医用口罩',
            item_spec: 'N95',
            unit: '盒',
            ordered_quantity: 100,
            unit_price: 25.00
          }
        ])
      }
    ]
  } catch (error) {
    message.error('加载采购订单数据失败')
  }
}

const loadUsers = async () => {
  try {
    // TODO: 调用用户API
    userOptions.value = [
      { id: '1', username: 'user1', real_name: '张三' },
      { id: '2', username: 'user2', real_name: '李四' }
    ]
  } catch (error) {
    message.error('加载用户数据失败')
  }
}

onMounted(() => {
  loadReceipts()
  loadSuppliers()
  loadOrders()
  loadUsers()
})
</script>

<style scoped>
.receipt-management {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}
</style>
