<template>
  <div class="approval-history">
    <a-page-header title="审批历史" sub-title="查看具体业务的完整审批流程" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索业务单号、申请人"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-select
            v-model:value="businessTypeFilter"
            placeholder="业务类型"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="EXPENSE">报销申请</a-select-option>
            <a-select-option value="PRE_APPLICATION">事前申请</a-select-option>
            <a-select-option value="BUDGET_ADJUSTMENT">预算调整</a-select-option>
            <a-select-option value="CONTRACT">合同审批</a-select-option>
            <a-select-option value="PURCHASE">采购申请</a-select-option>
          </a-select>
          <a-select
            v-model:value="statusFilter"
            placeholder="流程状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="PENDING">审批中</a-select-option>
            <a-select-option value="APPROVED">已通过</a-select-option>
            <a-select-option value="REJECTED">已拒绝</a-select-option>
            <a-select-option value="CANCELLED">已取消</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="dateRange"
            placeholder="申请时间范围"
            @change="handleFilterChange"
          />
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
        :expand-row-by-click="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'business_type'">
            <a-tag :color="getBusinessTypeColor(record.business_type)">
              {{ getBusinessTypeText(record.business_type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'amount'">
            <span v-if="record.amount" class="amount">¥{{ formatAmount(record.amount) }}</span>
            <span v-else>-</span>
          </template>
          
          <template v-if="column.key === 'progress'">
            <div class="progress-info">
              <a-progress
                :percent="getProgressPercent(record)"
                size="small"
                :status="getProgressStatus(record)"
              />
              <div class="progress-text">
                {{ record.current_step }}/{{ record.total_steps }} 步
              </div>
            </div>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewFlowChart(record)">
                <NodeIndexOutlined />
                流程图
              </a-button>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                详情
              </a-button>
              <a-button type="text" size="small" @click="exportFlow(record)">
                <DownloadOutlined />
                导出
              </a-button>
            </a-space>
          </template>
        </template>

        <template #expandedRowRender="{ record }">
          <div class="flow-timeline">
            <a-timeline>
              <a-timeline-item 
                v-for="(step, index) in record.approval_steps" 
                :key="index"
                :color="getStepColor(step.status)"
              >
                <template #dot>
                  <CheckCircleOutlined v-if="step.status === 'APPROVED'" />
                  <CloseCircleOutlined v-else-if="step.status === 'REJECTED'" />
                  <ClockCircleOutlined v-else-if="step.status === 'PENDING'" />
                  <FileTextOutlined v-else />
                </template>
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="step-name">{{ step.step_name }}</span>
                    <a-tag :color="getStepColor(step.status)" size="small">
                      {{ getStepStatusText(step.status) }}
                    </a-tag>
                  </div>
                  <div class="timeline-body">
                    <div class="approver-info">
                      <a-avatar size="small">{{ step.approver_name?.charAt(0) }}</a-avatar>
                      <span class="approver-name">{{ step.approver_name || '待分配' }}</span>
                      <span v-if="step.department_name" class="department-name">
                        ({{ step.department_name }})
                      </span>
                    </div>
                    <div v-if="step.comment" class="step-comment">
                      <strong>审批意见：</strong>{{ step.comment }}
                    </div>
                    <div class="step-time">
                      <span v-if="step.received_at">
                        接收时间：{{ formatDateTime(step.received_at) }}
                      </span>
                      <span v-if="step.processed_at">
                        处理时间：{{ formatDateTime(step.processed_at) }}
                      </span>
                      <span v-if="step.process_duration" class="duration">
                        (耗时：{{ step.process_duration }})
                      </span>
                    </div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 流程图查看模态框 -->
    <a-modal
      v-model:open="flowChartVisible"
      title="审批流程图"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentFlow">
        <div class="flow-header">
          <a-descriptions :column="3" bordered>
            <a-descriptions-item label="业务单号">{{ currentFlow.business_code }}</a-descriptions-item>
            <a-descriptions-item label="申请标题">{{ currentFlow.title }}</a-descriptions-item>
            <a-descriptions-item label="流程状态">
              <a-tag :color="getStatusColor(currentFlow.status)">
                {{ getStatusText(currentFlow.status) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>
        
        <a-divider>流程图</a-divider>
        
        <div class="flow-chart">
          <div class="flow-steps">
            <div 
              v-for="(step, index) in currentFlow.approval_steps" 
              :key="index"
              class="flow-step"
              :class="{ 
                'active': step.status === 'PENDING',
                'completed': step.status === 'APPROVED',
                'rejected': step.status === 'REJECTED'
              }"
            >
              <div class="step-icon">
                <CheckCircleOutlined v-if="step.status === 'APPROVED'" />
                <CloseCircleOutlined v-else-if="step.status === 'REJECTED'" />
                <ClockCircleOutlined v-else-if="step.status === 'PENDING'" />
                <UserOutlined v-else />
              </div>
              <div class="step-content">
                <div class="step-title">{{ step.step_name }}</div>
                <div class="step-approver">{{ step.approver_name || '待分配' }}</div>
                <div v-if="step.department_name" class="step-department">
                  {{ step.department_name }}
                </div>
              </div>
              <div v-if="index < currentFlow.approval_steps.length - 1" class="step-arrow">
                <RightOutlined />
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 详情查看模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="审批流程详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentFlow">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="流程ID">{{ currentFlow.flow_id }}</a-descriptions-item>
          <a-descriptions-item label="业务类型">
            <a-tag :color="getBusinessTypeColor(currentFlow.business_type)">
              {{ getBusinessTypeText(currentFlow.business_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="业务单号">{{ currentFlow.business_code }}</a-descriptions-item>
          <a-descriptions-item label="申请标题" :span="2">{{ currentFlow.title }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentFlow.applicant_name }}</a-descriptions-item>
          <a-descriptions-item label="申请金额">
            <span v-if="currentFlow.amount" class="amount">¥{{ formatAmount(currentFlow.amount) }}</span>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">{{ formatDateTime(currentFlow.created_at) }}</a-descriptions-item>
          <a-descriptions-item label="流程状态">
            <a-tag :color="getStatusColor(currentFlow.status)">
              {{ getStatusText(currentFlow.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前步骤">
            {{ currentFlow.current_step }}/{{ currentFlow.total_steps }}
          </a-descriptions-item>
          <a-descriptions-item label="总耗时">
            {{ getTotalDuration(currentFlow) }}
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>审批统计</a-divider>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic
              title="已完成步骤"
              :value="getCompletedSteps(currentFlow)"
              suffix={`/ ${currentFlow.total_steps}`}
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="平均处理时长"
              :value="getAvgProcessTime(currentFlow)"
              suffix="小时"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="流程进度"
              :value="getProgressPercent(currentFlow)"
              suffix="%"
            />
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  NodeIndexOutlined,
  EyeOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  UserOutlined,
  RightOutlined
} from '@ant-design/icons-vue'
import type { ApprovalFlow } from '@/types'

const loading = ref(false)
const searchKeyword = ref('')
const businessTypeFilter = ref<string>()
const statusFilter = ref<string>()
const dateRange = ref()
const tableData = ref<ApprovalFlow[]>([])
const flowChartVisible = ref(false)
const detailModalVisible = ref(false)
const currentFlow = ref<ApprovalFlow | null>(null)

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '业务类型', key: 'business_type', width: 100 },
  { title: '业务单号', dataIndex: 'business_code', key: 'business_code', width: 150 },
  { title: '申请标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '申请人', dataIndex: 'applicant_name', key: 'applicant_name', width: 100 },
  { title: '申请金额', key: 'amount', width: 120 },
  { title: '流程状态', key: 'status', width: 100 },
  { title: '审批进度', key: 'progress', width: 120 },
  { title: '申请时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const getBusinessTypeColor = (type: string) => {
  const colors = {
    'EXPENSE': 'blue',
    'PRE_APPLICATION': 'green',
    'BUDGET_ADJUSTMENT': 'orange',
    'CONTRACT': 'purple',
    'PURCHASE': 'cyan'
  }
  return colors[type] || 'default'
}

const getBusinessTypeText = (type: string) => {
  const texts = {
    'EXPENSE': '报销申请',
    'PRE_APPLICATION': '事前申请',
    'BUDGET_ADJUSTMENT': '预算调整',
    'CONTRACT': '合同审批',
    'PURCHASE': '采购申请'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'processing',
    'APPROVED': 'success',
    'REJECTED': 'error',
    'CANCELLED': 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'PENDING': '审批中',
    'APPROVED': '已通过',
    'REJECTED': '已拒绝',
    'CANCELLED': '已取消'
  }
  return texts[status] || status
}

const getStepColor = (status: string) => {
  const colors = {
    'APPROVED': 'green',
    'REJECTED': 'red',
    'PENDING': 'blue',
    'WAITING': 'gray'
  }
  return colors[status] || 'gray'
}

const getStepStatusText = (status: string) => {
  const texts = {
    'APPROVED': '已通过',
    'REJECTED': '已拒绝',
    'PENDING': '审批中',
    'WAITING': '等待中'
  }
  return texts[status] || status
}

const getProgressPercent = (flow: ApprovalFlow) => {
  if (!flow.total_steps) return 0
  return Math.round((flow.current_step / flow.total_steps) * 100)
}

const getProgressStatus = (flow: ApprovalFlow) => {
  if (flow.status === 'REJECTED') return 'exception'
  if (flow.status === 'APPROVED') return 'success'
  return 'active'
}

const getCompletedSteps = (flow: ApprovalFlow) => {
  return flow.approval_steps?.filter(step => step.status === 'APPROVED').length || 0
}

const getAvgProcessTime = (flow: ApprovalFlow) => {
  const completedSteps = flow.approval_steps?.filter(step => 
    step.status === 'APPROVED' && step.process_duration
  ) || []
  
  if (completedSteps.length === 0) return 0
  
  const totalHours = completedSteps.reduce((sum, step) => {
    const duration = parseFloat(step.process_duration?.replace(/[^\d.]/g, '') || '0')
    return sum + duration
  }, 0)
  
  return Math.round((totalHours / completedSteps.length) * 10) / 10
}

const getTotalDuration = (flow: ApprovalFlow) => {
  if (flow.status === 'PENDING') return '进行中'
  
  const created = new Date(flow.created_at)
  const lastProcessed = flow.approval_steps?.reduce((latest, step) => {
    if (step.processed_at) {
      const processedTime = new Date(step.processed_at)
      return processedTime > latest ? processedTime : latest
    }
    return latest
  }, created)
  
  if (!lastProcessed || lastProcessed === created) return '-'
  
  const diffHours = Math.floor((lastProcessed.getTime() - created.getTime()) / (1000 * 60 * 60))
  if (diffHours < 24) return `${diffHours}小时`
  
  const diffDays = Math.floor(diffHours / 24)
  const remainHours = diffHours % 24
  return remainHours > 0 ? `${diffDays}天${remainHours}小时` : `${diffDays}天`
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString()
}

const loadApprovalHistory = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取审批历史列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        flow_id: 'FLOW20240723001',
        business_type: 'EXPENSE',
        business_id: 'BX20240723001',
        business_code: 'BX20240723001',
        title: '张三的北京出差费用报销',
        applicant_name: '张三',
        amount: 4850,
        status: 'APPROVED',
        current_step: 3,
        total_steps: 3,
        created_at: '2024-07-23 09:30:00',
        updated_at: '2024-07-23 15:20:00',
        approval_steps: [
          {
            step: 1,
            step_name: '部门审批',
            approver_name: '部门主任',
            department_name: '内科',
            status: 'APPROVED',
            comment: '同意报销',
            received_at: '2024-07-23 09:30:00',
            processed_at: '2024-07-23 10:15:00',
            process_duration: '45分钟'
          },
          {
            step: 2,
            step_name: '财务审批',
            approver_name: '财务经理',
            department_name: '财务部',
            status: 'APPROVED',
            comment: '金额合理，同意支付',
            received_at: '2024-07-23 10:15:00',
            processed_at: '2024-07-23 14:30:00',
            process_duration: '4小时15分钟'
          },
          {
            step: 3,
            step_name: '总经理审批',
            approver_name: '总经理',
            department_name: '管理层',
            status: 'APPROVED',
            comment: '批准',
            received_at: '2024-07-23 14:30:00',
            processed_at: '2024-07-23 15:20:00',
            process_duration: '50分钟'
          }
        ]
      },
      {
        id: '2',
        flow_id: 'FLOW20240722001',
        business_type: 'PRE_APPLICATION',
        business_id: 'PRE20240722001',
        business_code: 'PRE20240722001',
        title: '李四的培训申请',
        applicant_name: '李四',
        amount: 8000,
        status: 'PENDING',
        current_step: 2,
        total_steps: 3,
        created_at: '2024-07-22 14:20:00',
        updated_at: '2024-07-23 09:30:00',
        approval_steps: [
          {
            step: 1,
            step_name: '部门审批',
            approver_name: '部门主任',
            department_name: '外科',
            status: 'APPROVED',
            comment: '培训内容有价值，同意申请',
            received_at: '2024-07-22 14:20:00',
            processed_at: '2024-07-23 09:30:00',
            process_duration: '19小时10分钟'
          },
          {
            step: 2,
            step_name: '人事审批',
            approver_name: '人事经理',
            department_name: '人事部',
            status: 'PENDING',
            comment: '',
            received_at: '2024-07-23 09:30:00',
            processed_at: null,
            process_duration: null
          },
          {
            step: 3,
            step_name: '总经理审批',
            approver_name: '总经理',
            department_name: '管理层',
            status: 'WAITING',
            comment: '',
            received_at: null,
            processed_at: null,
            process_duration: null
          }
        ]
      }
    ]
    pagination.total = 2
  } catch (error) {
    message.error('加载审批历史失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadApprovalHistory()
}

const handleFilterChange = () => {
  pagination.current = 1
  loadApprovalHistory()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadApprovalHistory()
}

const viewFlowChart = (flow: ApprovalFlow) => {
  currentFlow.value = flow
  flowChartVisible.value = true
}

const viewDetails = (flow: ApprovalFlow) => {
  currentFlow.value = flow
  detailModalVisible.value = true
}

const exportFlow = (flow: ApprovalFlow) => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

onMounted(() => {
  loadApprovalHistory()
})
</script>

<style scoped>
.approval-history {
  padding: 24px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.flow-timeline {
  margin: 16px 0;
  padding: 0 24px;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.step-name {
  font-weight: 500;
}

.timeline-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.approver-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.approver-name {
  font-weight: 500;
}

.department-name {
  color: #666;
  font-size: 12px;
}

.step-comment {
  color: #666;
  font-size: 14px;
}

.step-time {
  font-size: 12px;
  color: #999;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.duration {
  color: #1890ff;
}

.flow-header {
  margin-bottom: 16px;
}

.flow-chart {
  padding: 24px;
  background: #fafafa;
  border-radius: 6px;
}

.flow-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 2px solid #d9d9d9;
  min-width: 120px;
  position: relative;
}

.flow-step.active {
  border-color: #1890ff;
  background: #e6f7ff;
}

.flow-step.completed {
  border-color: #52c41a;
  background: #f6ffed;
}

.flow-step.rejected {
  border-color: #f5222d;
  background: #fff2f0;
}

.step-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.step-content {
  text-align: center;
}

.step-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.step-approver {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.step-department {
  font-size: 11px;
  color: #999;
}

.step-arrow {
  position: absolute;
  right: -24px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #d9d9d9;
}
</style>
