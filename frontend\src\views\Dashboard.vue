<template>
  <div class="dashboard">
    <a-page-header title="仪表盘" sub-title="欢迎使用医院内部控制与运营管理系统" />

    <a-row :gutter="[16, 16]" class="stats-grid">
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="预算执行率"
            :value="85.6"
            suffix="%"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <FundOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="待审批事项"
            :value="12"
            :value-style="{ color: '#cf1322' }"
          >
            <template #prefix>
              <AuditOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="本月支出"
            :value="2856789"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <AccountBookOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="合同数量"
            :value="156"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <FileTextOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="charts-grid">
      <a-col :xs="24" :lg="12">
        <a-card title="预算执行趋势" class="chart-card">
          <div class="chart-placeholder">
            图表区域 - 预算执行趋势图
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :lg="12">
        <a-card title="支出分类统计" class="chart-card">
          <div class="chart-placeholder">
            图表区域 - 支出分类统计图
          </div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :lg="16">
        <a-card title="最近审批事项" class="recent-approvals">
          <a-list
            :data-source="recentApprovals"
            item-layout="horizontal"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta
                  :title="item.title"
                  :description="item.description"
                >
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      {{ item.type }}
                    </a-avatar>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button type="primary" size="small">处理</a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <a-col :xs="24" :lg="8">
        <a-card title="快捷操作" class="quick-actions">
          <a-space direction="vertical" style="width: 100%">
            <a-button type="primary" block @click="createExpense">
              <PlusOutlined />
              新建报销申请
            </a-button>
            <a-button block @click="createBudget">
              <FundOutlined />
              制定预算计划
            </a-button>
            <a-button block @click="viewContracts">
              <FileTextOutlined />
              查看合同管理
            </a-button>
            <a-button block @click="viewReports">
              <BarChartOutlined />
              生成分析报告
            </a-button>
          </a-space>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  FundOutlined,
  AuditOutlined,
  AccountBookOutlined,
  FileTextOutlined,
  PlusOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

const recentApprovals = ref([
  {
    title: '张三的差旅费报销申请',
    description: '申请金额：¥2,580.00 | 申请时间：2024-07-23 09:30',
    type: '报',
    color: '#1890ff'
  },
  {
    title: '办公用品采购合同审批',
    description: '合同金额：¥15,600.00 | 申请时间：2024-07-23 08:45',
    type: '合',
    color: '#52c41a'
  },
  {
    title: '李四的培训费用申请',
    description: '申请金额：¥3,200.00 | 申请时间：2024-07-22 16:20',
    type: '报',
    color: '#1890ff'
  }
])

const createExpense = () => {
  router.push('/expense/create')
}

const createBudget = () => {
  router.push('/budget/create')
}

const viewContracts = () => {
  router.push('/contracts')
}

const viewReports = () => {
  router.push('/reports')
}

onMounted(() => {
  console.log('Dashboard mounted')
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.stats-grid {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.charts-grid {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  color: #999;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.recent-approvals :deep(.ant-list-item) {
  padding: 12px 0;
}

.quick-actions :deep(.ant-btn) {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
</style>
