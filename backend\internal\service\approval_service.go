package service

import (
	"fmt"
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
	"time"

	"github.com/google/uuid"
)

type ApprovalService struct {
	approvalRepo *repository.ApprovalRepository
}

func NewApprovalService(approvalRepo *repository.ApprovalRepository) *ApprovalService {
	return &ApprovalService{approvalRepo: approvalRepo}
}

type ApprovalTask struct {
	TaskID        string  `json:"task_id"`
	FlowID        string  `json:"flow_id"`
	BusinessType  string  `json:"business_type"`
	BusinessID    string  `json:"business_id"`
	BusinessCode  string  `json:"business_code"`
	Title         string  `json:"title"`
	ApplicantName string  `json:"applicant_name"`
	Amount        float64 `json:"amount,omitempty"`
	ReceivedAt    string  `json:"received_at"`
}

func (s *ApprovalService) GetTasksByApprover(approverID string, page, pageSize int, businessType string) ([]ApprovalTask, int64, error) {
	offset := (page - 1) * pageSize
	nodes, total, err := s.approvalRepo.GetPendingNodesByApprover(approverID, offset, pageSize, businessType)
	if err != nil {
		return nil, 0, err
	}

	var tasks []ApprovalTask
	for _, node := range nodes {
		task := ApprovalTask{
			TaskID:       node.ID.String(),
			FlowID:       node.FlowID.String(),
			BusinessType: node.Flow.BusinessType,
			BusinessID:   node.Flow.BusinessID.String(),
			ReceivedAt:   node.CreatedAt.Format(time.RFC3339),
		}

		// 根据业务类型获取业务详情
		switch node.Flow.BusinessType {
		case "EXPENSE":
			// TODO: 获取报销单详情
			task.BusinessCode = "BX202507230001" // 临时数据
			task.Title = "张三提交的交通费报销"
			task.ApplicantName = "张三"
			task.Amount = 205.50
		case "CONTRACT":
			// TODO: 获取合同详情
		case "PURCHASE":
			// TODO: 获取采购详情
		}

		tasks = append(tasks, task)
	}

	return tasks, total, nil
}

func (s *ApprovalService) ProcessTask(taskID, approverID, action, comment string) error {
	// 验证任务ID和审批人ID
	taskUUID, err := uuid.Parse(taskID)
	if err != nil {
		return fmt.Errorf("任务ID格式错误")
	}

	approverUUID, err := uuid.Parse(approverID)
	if err != nil {
		return fmt.Errorf("审批人ID格式错误")
	}

	// 获取审批节点
	node, err := s.approvalRepo.GetNodeByID(taskUUID.String())
	if err != nil {
		return fmt.Errorf("审批任务不存在")
	}

	// 验证审批人权限
	if node.ApproverID != approverUUID {
		return fmt.Errorf("无权限处理此审批任务")
	}

	// 验证任务状态
	if node.Status != "PENDING" {
		return fmt.Errorf("任务已处理，无法重复操作")
	}

	// 更新审批节点
	now := time.Now()
	node.Status = action
	node.Comment = comment
	node.ProcessedAt = &now

	if err := s.approvalRepo.UpdateNode(node); err != nil {
		return fmt.Errorf("更新审批节点失败: %v", err)
	}

	// TODO: 根据审批结果处理后续流程
	// 1. 如果是同意，检查是否还有后续节点
	// 2. 如果是拒绝，结束流程并通知申请人
	// 3. 更新审批流状态
	// 4. 发送通知

	return nil
}

func (s *ApprovalService) GetProcessedTasksByApprover(approverID string, page, pageSize int, businessType string) ([]ApprovalTask, int64, error) {
	offset := (page - 1) * pageSize
	nodes, total, err := s.approvalRepo.GetProcessedNodesByApprover(approverID, offset, pageSize, businessType)
	if err != nil {
		return nil, 0, err
	}

	var tasks []ApprovalTask
	for _, node := range nodes {
		task := ApprovalTask{
			TaskID:       node.ID.String(),
			FlowID:       node.FlowID.String(),
			BusinessType: node.Flow.BusinessType,
			BusinessID:   node.Flow.BusinessID.String(),
			ReceivedAt:   node.CreatedAt.Format(time.RFC3339),
		}

		// 根据业务类型获取业务详情
		switch node.Flow.BusinessType {
		case "EXPENSE":
			// TODO: 获取报销单详情
			task.BusinessCode = "BX202507230001" // 临时数据
			task.Title = "张三提交的交通费报销"
			task.ApplicantName = "张三"
			task.Amount = 205.50
		}

		tasks = append(tasks, task)
	}

	return tasks, total, nil
}

func (s *ApprovalService) GetFlowHistory(businessID, businessType string) ([]model.ApprovalNode, error) {
	return s.approvalRepo.GetFlowHistory(businessID, businessType)
}

func (s *ApprovalService) StartFlow(businessID, businessType string, approvers []string) error {
	// 创建审批流实例
	flow := &model.ApprovalFlow{
		BusinessID:   uuid.MustParse(businessID),
		BusinessType: businessType,
		Status:       "PENDING",
		CurrentStep:  1,
	}

	if err := s.approvalRepo.CreateFlow(flow); err != nil {
		return fmt.Errorf("创建审批流失败: %v", err)
	}

	// 创建审批节点
	for i, approverID := range approvers {
		node := &model.ApprovalNode{
			FlowID:     flow.ID,
			ApproverID: uuid.MustParse(approverID),
			Status:     "PENDING",
			Step:       i + 1,
		}

		if err := s.approvalRepo.CreateNode(node); err != nil {
			return fmt.Errorf("创建审批节点失败: %v", err)
		}
	}

	return nil
}
