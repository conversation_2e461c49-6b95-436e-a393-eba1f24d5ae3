// 用户相关类型
export interface User {
  id: string
  user_name: string
  employee_id: string
  email: string
  phone: string
  department_id: string
  status: number
  last_login_at?: string
  created_at: string
  updated_at: string
  department?: Department
  roles?: Role[]
}

export interface LoginForm {
  principal: string  // 工号、邮箱或手机号
  credential: string // 密码
}

export interface LoginResponse {
  token: string
  expires_in: number
}

// 部门相关类型
export interface Department {
  id: string
  name: string
  code: string
  parent_id?: string
  level: number
  sort_order: number
  description?: string
  status: number
  created_at: string
  updated_at: string
  parent?: Department
  children?: Department[]
}

// 角色相关类型
export interface Role {
  id: string
  role_name: string
  role_code: string
  permissions: string // JSON字符串
  description?: string
  status: number
  created_at: string
  updated_at: string
}

// 预算相关类型
export interface Budget {
  id: string
  budget_year: number
  department_id: string
  total_amount: number
  status: string
  created_at: string
  updated_at: string
  department?: Department
  budget_items?: BudgetItem[]
}

export interface BudgetItem {
  id: string
  budget_id: string
  item_name: string
  item_code: string
  category: string
  budget_amount: number
  used_amount: number
  frozen_amount: number
  available_amount: number
  description?: string
  created_at: string
  updated_at: string
}

// 支出控制相关类型
export interface ExpenseApplication {
  id: string
  application_code: string
  title: string
  applicant_id: string
  department_id: string
  total_amount: number
  status: string
  pre_application_id?: string
  created_at: string
  updated_at: string
  applicant?: User
  department?: Department
  expense_details?: ExpenseDetail[]
}

export interface ExpenseDetail {
  id: string
  application_id: string
  budget_item_id: string
  description: string
  amount: number
  expense_date: string
  invoice_info?: string
  created_at: string
  updated_at: string
  budget_item?: BudgetItem
}

// 审批相关类型
export interface ApprovalTask {
  task_id: string
  flow_id: string
  business_type: string
  business_id: string
  business_code: string
  title: string
  applicant_name: string
  amount?: number
  received_at: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
}

export interface PaginationParams {
  page: number
  pageSize: number
}

export interface PaginationResponse<T = any> {
  list: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}
