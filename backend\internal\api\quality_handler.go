package api

import (
	"hospital-management/internal/model"
	"hospital-management/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type QualityHandler struct {
	qualityService *service.QualityService
}

func NewQualityHandler(qualityService *service.QualityService) *QualityHandler {
	return &QualityHandler{qualityService: qualityService}
}

// ListIndicators 获取质量指标列表
func (h *QualityHandler) ListIndicators(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10"))

	indicators, total, err := h.qualityService.ListIndicators(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取质量指标列表失败",
			"data":    nil,
		})
		return
	}

	c.<PERSON><PERSON>(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":        indicators,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// CreateIndicator 创建质量指标
func (h *QualityHandler) CreateIndicator(c *gin.Context) {
	var indicator model.QualityIndicator
	if err := c.ShouldBindJSON(&indicator); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	if err := h.qualityService.CreateIndicator(&indicator); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建质量指标失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    indicator,
	})
}

// GetIndicator 获取质量指标详情
func (h *QualityHandler) GetIndicator(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	indicator, err := h.qualityService.GetIndicatorByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "质量指标不存在",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    indicator,
	})
}

// UpdateIndicator 更新质量指标
func (h *QualityHandler) UpdateIndicator(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	var indicator model.QualityIndicator
	if err := c.ShouldBindJSON(&indicator); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	indicator.ID = uint(id)
	if err := h.qualityService.UpdateIndicator(&indicator); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新质量指标失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    indicator,
	})
}

// DeleteIndicator 删除质量指标
func (h *QualityHandler) DeleteIndicator(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	if err := h.qualityService.DeleteIndicator(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除质量指标失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
		"data":    nil,
	})
}

// CreateData 创建质量数据
func (h *QualityHandler) CreateData(c *gin.Context) {
	var data model.QualityData
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	if err := h.qualityService.CreateData(&data); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建质量数据失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    data,
	})
}

// GetDataByIndicator 根据指标ID获取质量数据
func (h *QualityHandler) GetDataByIndicator(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	data, total, err := h.qualityService.GetDataByIndicatorID(uint(id), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取质量数据失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":        data,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}
