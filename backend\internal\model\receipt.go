package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ReceiptRecord 入库单主表 (tbl_receipt_records)
type ReceiptRecord struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ReceiptCode  string    `json:"receipt_code" gorm:"size:50;uniqueIndex;not null;comment:入库单号"`
	OrderID      uuid.UUID `json:"order_id" gorm:"type:uuid;not null;comment:关联采购订单ID"`
	SupplierID   uuid.UUID `json:"supplier_id" gorm:"type:uuid;not null;comment:供应商ID"`
	ReceiverID   uuid.UUID `json:"receiver_id" gorm:"type:uuid;not null;comment:收货人ID"`
	InspectorID  *uuid.UUID `json:"inspector_id" gorm:"type:uuid;comment:质检员ID"`
	ReceiptDate  time.Time `json:"receipt_date" gorm:"not null;comment:收货日期"`
	InspectDate  *time.Time `json:"inspect_date" gorm:"comment:质检日期"`
	TotalAmount  float64   `json:"total_amount" gorm:"type:decimal(18,2);not null;comment:入库总金额"`
	Status       string    `json:"status" gorm:"size:30;not null;default:'RECEIVED';comment:状态 RECEIVED/INSPECTING/PASSED/REJECTED"`
	Remark       string    `json:"remark" gorm:"type:text;comment:备注"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy    *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy    *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Order        PurchaseOrder `json:"order" gorm:"foreignKey:OrderID"`
	Supplier     Supplier      `json:"supplier" gorm:"foreignKey:SupplierID"`
	Receiver     User          `json:"receiver" gorm:"foreignKey:ReceiverID"`
	Inspector    *User         `json:"inspector" gorm:"foreignKey:InspectorID"`
	ReceiptItems []ReceiptItem `json:"receipt_items" gorm:"foreignKey:ReceiptID"`
}

// ReceiptItem 入库明细表 (tbl_receipt_items)
type ReceiptItem struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ReceiptID       uuid.UUID `json:"receipt_id" gorm:"type:uuid;not null;comment:入库单ID"`
	ItemName        string    `json:"item_name" gorm:"size:255;not null;comment:物品名称"`
	ItemSpec        string    `json:"item_spec" gorm:"size:255;comment:规格型号"`
	Unit            string    `json:"unit" gorm:"size:50;not null;comment:单位"`
	OrderedQuantity int       `json:"ordered_quantity" gorm:"not null;comment:订单数量"`
	ReceivedQuantity int      `json:"received_quantity" gorm:"not null;comment:实收数量"`
	QualifiedQuantity int     `json:"qualified_quantity" gorm:"default:0;comment:合格数量"`
	UnitPrice       float64   `json:"unit_price" gorm:"type:decimal(18,2);not null;comment:单价"`
	TotalPrice      float64   `json:"total_price" gorm:"type:decimal(18,2);not null;comment:总价"`
	BatchNumber     string    `json:"batch_number" gorm:"size:100;comment:批次号"`
	ExpiryDate      *time.Time `json:"expiry_date" gorm:"comment:有效期"`
	InspectResult   string    `json:"inspect_result" gorm:"size:30;default:'PENDING';comment:质检结果 PENDING/PASSED/REJECTED"`
	InspectRemark   string    `json:"inspect_remark" gorm:"type:text;comment:质检备注"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy       *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy       *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Receipt ReceiptRecord `json:"receipt" gorm:"foreignKey:ReceiptID"`
}

// InspectionRecord 质检记录表 (tbl_inspection_records)
type InspectionRecord struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ReceiptID     uuid.UUID `json:"receipt_id" gorm:"type:uuid;not null;comment:入库单ID"`
	InspectorID   uuid.UUID `json:"inspector_id" gorm:"type:uuid;not null;comment:质检员ID"`
	InspectDate   time.Time `json:"inspect_date" gorm:"not null;comment:质检日期"`
	InspectResult string    `json:"inspect_result" gorm:"size:30;not null;comment:质检结果 PASSED/REJECTED"`
	InspectItems  string    `json:"inspect_items" gorm:"type:jsonb;comment:质检明细JSON"`
	Remark        string    `json:"remark" gorm:"type:text;comment:质检备注"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy     *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy     *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Receipt   ReceiptRecord `json:"receipt" gorm:"foreignKey:ReceiptID"`
	Inspector User          `json:"inspector" gorm:"foreignKey:InspectorID"`
}

// TableName 设置表名
func (ReceiptRecord) TableName() string {
	return "tbl_receipt_records"
}

func (ReceiptItem) TableName() string {
	return "tbl_receipt_items"
}

func (InspectionRecord) TableName() string {
	return "tbl_inspection_records"
}

// 状态常量
const (
	ReceiptStatusReceived   = "RECEIVED"   // 已收货
	ReceiptStatusInspecting = "INSPECTING" // 质检中
	ReceiptStatusPassed     = "PASSED"     // 验收通过
	ReceiptStatusRejected   = "REJECTED"   // 验收不通过
)

const (
	InspectResultPending  = "PENDING"  // 待质检
	InspectResultPassed   = "PASSED"   // 质检通过
	InspectResultRejected = "REJECTED" // 质检不通过
)
