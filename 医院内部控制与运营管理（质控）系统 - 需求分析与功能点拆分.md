1. 项目背景与核心目标
1.1. 项目愿景
本项目旨在构建一个以“内部控制”为核心理念，以“预算管理”为主线，集成支出、采购、合同、资产等关键业务的B/S架构医院运营管理平台。该平台的目标是解决当前医院管理中普遍存在的“信息孤岛”、流程不透明、预算与执行脱节、管理效率低下以及潜在的合规风险等痛尖问题。

1.2. 核心设计理念
系统设计将严格遵循以下核心理念，这些理念源自您提供的文件中反复强调的管理思想：

预算先行，无预算不支出: 预算是所有经济活动的起点和硬性约束。任何支出、采购、合同签订都必须关联预算指标。
业财融合，流程驱动: 打通业务（采购、合同、项目）与财务（预算、支付、核算）的壁垒，通过标准化的线上流程驱动业务，实现数据同源、过程可溯。
分权制衡，风险可控: 践行不相容岗位分离原则（如申请与审批、采购与验收、付款与记账），通过灵活的权限与流程配置，将风险控制融入到每个业务环节。
数据驱动，智能决策: 汇集运营全过程数据，提供多维度、可穿透的统计分析报表和管理驾驶舱，为管理者决策提供及时、准确的数据支持。
1.3. 技术栈约束
后端服务: Go (Golang)
数据库: PostgreSQL
前端框架: Vue 3
移动端: Uni-app (兼容小程序、H5)
2. 系统总体功能架构 (Logical Architecture)
系统将采用模块化设计，主要分为 核心业务应用层、统一支撑平台层 和 数据集成层。

Parse error on line 3:
...层        A[PC端 Web (Vue3)]        B[移动
----------------------^
Expecting 'SQE', 'DOUBLECIRCLEEND', 'PE', '-)', 'STADIUMEND', 'SUBROUTINEEND', 'PIPE', 'CYLINDEREND', 'DIAMOND_STOP', 'TAGEND', 'TRAPEND', 'INVTRAPEND', 'UNICODE_TEXT', 'TEXT', 'TAGSTART', got 'PS'
3. 核心功能模块详细拆解
模块一：全面预算管理系统
目标：建立从目标制定、预算编制、审批、调整到执行分析与绩效评价的全周期闭环管理。

一级功能	二级功能	核心需求点	备注
1.1 预算方案与配置	1.1.1 预算方案设置	支持创建多套预算方案（如年度预算、滚动预算）；定义预算周期、控制规则（刚性/柔性）。	系统基础配置
1.1.2 预算科目体系	定义预算收支科目，可与财务会计科目建立映射关系。	支撑业财融合
1.1.3 预算事项管理	将预算科目细化到具体的业务活动（如会议费、差旅费、设备购置）。	
1.2 预算编制	1.2.1 目标管理	医院下达年度战略目标（如收入增长率、成本控制率），分解至各科室。	目标驱动编制
1.2.2 事业计划上报	临床科室上报业务量计划（门诊人次、手术台数）、人员计划、采购计划等。	预算编制依据
1.2.3 多维预算编制	科室在线填报收入预算、支出预算（基本支出、项目支出）。支持从历史数据、事业计划自动测算。	支持按科室、按项目等多维度编制
1.3 预算审批与下达	1.3.1 归口部门审核	财务、医务等归口部门对相关科室的预算进行汇总、审核、调整。	流程节点
1.3.2 预算委员会审批	预算委员会在线进行最终审批，支持逐级审批与驳回。	流程节点
1.3.3 预算指标下达	审批通过后，系统自动生成各科室、各项目的可用预算指标。	
1.4 预算执行与控制	1.4.1 预算占用与释放	所有支出、采购、合同申请必须关联预算指标，提交时冻结额度，支付完成时扣减，废弃时释放。	核心控制点
1.4.2 预算调整	支持预算执行过程中的追加、追减和内部调剂，需走审批流程。	
1.5 预算分析与评价	1.5.1 预算执行分析	提供多维度（科室、项目、时间、事项）的执行进度、差异分析报表，支持数据穿透查询。	管理驾驶舱核心数据源
1.5.2 预算绩效评价	设置绩效评价指标，对项目预算的产出和效益进行评价。	实现PDCA闭环
模块二：支出控制管理系统
目标：规范化、流程化管理所有经费支出活动，强化事前、事中控制，确保支出的合规性与合理性。

一级功能	二级功能	核心需求点	备注
2.1 基础设置	2.1.1 费用类型定义	定义员工易于理解的费用类型（如：市内交通费、住宿费），并关联预算事项和会计科目。	简化用户操作
2.1.2 费用标准设置	灵活配置各类费用（差旅、招待）的报销标准，支持按职级、地区等多维度控制。	
2.2 事前申请	2.2.1 差旅/会议/培训申请	员工提交出差、会议等事前申请，估算费用并关联预算，审批通过后方可执行。	强化事前控制
2.2.2 借款申请	员工线上发起对公或对私借款申请，与事前申请关联，关联预算。	
2.3 通用报销	2.3.1 移动填单与OCR识别	员工通过移动端拍照或上传发票，系统通过OCR自动识别发票信息（发票代码、号码、金额、日期）并填充表单。	提升效率，发票验真、查重
2.3.2 报销单填报	区分差旅报销（需填行程）与通用报销。自动进行标准校验、预算校验。	关联事前申请、冲抵借款
2.3.3 审批流程	根据费用类型、金额、成本中心等条件，自动匹配审批路径。	
2.4 支付管理	2.4.1 对公付款申请	针对供应商货款、合同款等，发起付款申请，需关联合同或采购订单。	
2.4.2 支付受理与处理	出纳岗对审批通过的支付单进行受理，处理支付，并登记支付信息。	
2.5 台账与凭证	2.5.1 各类台账	自动生成借款台账、报销台账、付款台账、发票台账。	全过程追溯
2.5.2 凭证中心	根据预设规则，自动汇总生成记账凭证，推送至财务核算系统。	业财融合关键接口
模块三：采购管理系统
目标：规范医院物资、设备、服务的采购流程，提高效率与透明度，有效管理供应商。

一级功能	二级功能	核心需求点	备注
3.1 基础管理	3.1.1 供应商管理	建立供应商档案库，管理资质、评价、履约情况，支持分级与黑名单。	
3.1.2 评审专家管理	建立院内外评审专家库，记录专业、职称、评审历史，支持随机抽取与回避规则。	
3.2 采购流程管理	3.2.1 采购需求申报	业务科室提交采购需求，自动关联预算进行校验。	
3.2.2 采购计划生成	归口管理部门汇总需求，制定采购计划。	
3.2.3 采购执行	涵盖招标、询价、单一来源等多种采购方式的管理。支持采购公告发布、投标材料审查、开评标管理。	
3.2.4 采购结果处理	录入中标/成交信息，系统自动生成或关联合同、生成采购订单。	
3.3 采购执行	3.3.1 订单跟踪	采购订单下发与状态跟踪。	
3.3.2 验收管理	物品到货后，进行线上验收登记，可关联资产入库。	
3.4 归档与分析	3.4.1 采购归档	归档采购全过程的电子文档（需求单、招标文件、合同等）。	
3.4.2 统计分析	提供采购成本、周期、供应商绩效等多维度分析报表。	
模块四：合同管理系统
目标：实现合同全生命周期（订立、审批、履行、变更、归档）的统一管理，防范法律与经济风险。

一级功能	二级功能	核心需求点	备注
4.1 基础设置	4.1.1 合同范本管理	上传并管理各类合同范本，方便业务人员拟定。	
4.1.2 相对方管理	管理合同签约的对方单位信息。	与供应商库可打通
4.2 合同订立与审批	4.2.1 合同起草	基于范本或直接起草合同，录入关键信息（金额、标的、期限等）。	关联采购项目或预算
4.2.2 文本比对	在审批流程中，新上传的版本能与上一版自动比对差异，高亮显示。防止线下篡改。	核心风控功能
4.2.3 合同审批	法务、业务、财务等多部门会签，审批通过后赋予统一合同编号。	
4.3 合同履行与监控	4.3.1 合同备案	对已签订的纸质合同进行扫描件备案，与电子流程关联。	
4.3.2 支付管理	合同付款计划制定与执行。每次付款申请需关联合同，系统自动校验是否超额。	联动【支出控制系统】
4.3.3 合同预警	对即将到期、即将达到付款节点的合同进行自动提醒。	
4.4 变更与归档	4.4.1 合同变更/终止/结项	记录合同的变更、补充协议，或进行终止、结项操作，均需审批。	释放或调整占用的预算
4.4.2 合同台账与归档	形成全院统一的合同台账，支持多维度查询。已完结合同进行电子归档。	
4. 统一支撑平台需求
用户与权限中心 (RBAC+ABAC): 实现基于“角色”的权限控制（菜单、按钮权限），并结合基于“属性”的控制（例如，科室主任只能看到本科室的数据）。支持与医院现有 HR 系统或钉钉/企业微信组织架构同步。
工作流引擎: 提供图形化的流程设计器，允许业务管理员根据管理制度灵活配置审批流程，支持会签、或签、条件分支、逐级审批等复杂流程模式。
消息中心: 实现统一的待办事项、已办事项、系统通知、预警消息。支持通过系统内弹窗、邮件、企业微信/钉钉等多种方式推送。
门户管理: 提供可配置的门户页面（个人门户、领导驾驶舱），用户可自定义快捷方式、待办列表、常用报表等，实现“千人千面”。
5. 非功能性需求
性能: 核心业务（如提交报销单、审批）的页面响应时间应在 3 秒以内。
安全性: 遵循常见的 Web 安全规范，防止 SQL 注入、XSS、CSRF 攻击。敏感数据（如身份证、银行卡号）在存储和传输时需加密。操作日志需完整记录。
可扩展性: 系统应采用微服务或模块化架构，便于未来新增业务模块（如人力资源、绩效管理）或对现有模块进行升级。
易用性: 界面设计简洁、直观，符合用户操作习惯，降低学习成本。
集成性: 提供标准的 RESTful API 接口，并具备与 HIS、财务软件、OA 等异构系统进行数据交换的能力（通过视图、中间库、Web Service 等方式）。
