<template>
  <div class="approval-tasks">
    <a-page-header title="待办任务" sub-title="我的待办审批任务" />
    
    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="stats-cards">
      <a-col :xs="24" :sm="8" :lg="6">
        <a-card>
          <a-statistic
            title="待办总数"
            :value="taskStats.total"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <ClockCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="8" :lg="6">
        <a-card>
          <a-statistic
            title="今日新增"
            :value="taskStats.today"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <PlusCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="8" :lg="6">
        <a-card>
          <a-statistic
            title="超时任务"
            :value="taskStats.overdue"
            :value-style="{ color: '#f5222d' }"
          >
            <template #prefix>
              <ExclamationCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="8" :lg="6">
        <a-card>
          <a-statistic
            title="本周已办"
            :value="taskStats.weekCompleted"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="businessTypeFilter"
            placeholder="业务类型"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="EXPENSE">报销申请</a-select-option>
            <a-select-option value="PRE_APPLICATION">事前申请</a-select-option>
            <a-select-option value="BUDGET_ADJUSTMENT">预算调整</a-select-option>
            <a-select-option value="CONTRACT">合同审批</a-select-option>
            <a-select-option value="PURCHASE">采购申请</a-select-option>
          </a-select>
          <a-select
            v-model:value="priorityFilter"
            placeholder="优先级"
            style="width: 120px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="HIGH">高</a-select-option>
            <a-select-option value="MEDIUM">中</a-select-option>
            <a-select-option value="LOW">低</a-select-option>
          </a-select>
          <a-button type="primary" @click="batchApprove" :disabled="selectedRowKeys.length === 0">
            <CheckOutlined />
            批量通过
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'business_type'">
            <a-tag :color="getBusinessTypeColor(record.business_type)">
              {{ getBusinessTypeText(record.business_type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'amount'">
            <span v-if="record.amount" class="amount">¥{{ formatAmount(record.amount) }}</span>
            <span v-else>-</span>
          </template>
          
          <template v-if="column.key === 'received_at'">
            <div class="time-info">
              <div>{{ formatDateTime(record.received_at) }}</div>
              <div class="time-duration" :class="{ 'overdue': isOverdue(record.received_at) }">
                {{ getTimeDuration(record.received_at) }}
              </div>
            </div>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="primary" size="small" @click="showApprovalModal(record, 'APPROVE')">
                <CheckOutlined />
                通过
              </a-button>
              <a-button type="default" size="small" @click="showApprovalModal(record, 'REJECT')">
                <CloseOutlined />
                拒绝
              </a-button>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 审批处理模态框 -->
    <a-modal
      v-model:open="approvalModalVisible"
      :title="approvalAction === 'APPROVE' ? '审批通过' : '审批拒绝'"
      @ok="handleApprovalSubmit"
      @cancel="handleApprovalCancel"
    >
      <div v-if="currentTask">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="业务类型">
            <a-tag :color="getBusinessTypeColor(currentTask.business_type)">
              {{ getBusinessTypeText(currentTask.business_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申请标题">{{ currentTask.title }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentTask.applicant_name }}</a-descriptions-item>
          <a-descriptions-item label="申请金额">
            <span v-if="currentTask.amount" class="amount">¥{{ formatAmount(currentTask.amount) }}</span>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="接收时间" :span="2">{{ formatDateTime(currentTask.received_at) }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>审批意见</a-divider>
        
        <a-form
          ref="approvalFormRef"
          :model="approvalForm"
          :rules="approvalFormRules"
          layout="vertical"
        >
          <a-form-item 
            :label="approvalAction === 'APPROVE' ? '审批意见' : '拒绝原因'" 
            name="comment"
          >
            <a-textarea
              v-model:value="approvalForm.comment"
              :placeholder="approvalAction === 'APPROVE' ? '请输入审批意见（可选）' : '请输入拒绝原因'"
              :rows="4"
              :maxlength="500"
              show-count
            />
          </a-form-item>
          
          <a-form-item v-if="approvalAction === 'APPROVE'" label="下一步处理">
            <a-radio-group v-model:value="approvalForm.next_action">
              <a-radio value="AUTO">自动流转</a-radio>
              <a-radio value="MANUAL">指定审批人</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            v-if="approvalAction === 'APPROVE' && approvalForm.next_action === 'MANUAL'" 
            label="指定审批人" 
            name="next_approver"
          >
            <a-select
              v-model:value="approvalForm.next_approver"
              placeholder="选择下一步审批人"
              show-search
              :filter-option="filterUser"
            >
              <a-select-option 
                v-for="user in userOptions" 
                :key="user.id" 
                :value="user.id"
              >
                {{ user.user_name }} ({{ user.department?.name }})
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 详情查看模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="任务详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentTask">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务ID">{{ currentTask.task_id }}</a-descriptions-item>
          <a-descriptions-item label="业务类型">
            <a-tag :color="getBusinessTypeColor(currentTask.business_type)">
              {{ getBusinessTypeText(currentTask.business_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="业务单号">{{ currentTask.business_code }}</a-descriptions-item>
          <a-descriptions-item label="申请标题" :span="2">{{ currentTask.title }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentTask.applicant_name }}</a-descriptions-item>
          <a-descriptions-item label="申请金额">
            <span v-if="currentTask.amount" class="amount">¥{{ formatAmount(currentTask.amount) }}</span>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentTask.priority)">
              {{ getPriorityText(currentTask.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="接收时间">{{ formatDateTime(currentTask.received_at) }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>审批历史</a-divider>
        
        <a-timeline>
          <a-timeline-item 
            v-for="(history, index) in approvalHistory" 
            :key="index"
            :color="getHistoryColor(history.action)"
          >
            <template #dot>
              <CheckCircleOutlined v-if="history.action === 'APPROVED'" />
              <CloseCircleOutlined v-else-if="history.action === 'REJECTED'" />
              <ClockCircleOutlined v-else />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">{{ history.step_name }}</div>
              <div class="timeline-desc">
                {{ history.approver_name }} {{ getActionText(history.action) }}
                <span v-if="history.comment">：{{ history.comment }}</span>
              </div>
              <div class="timeline-time">{{ formatDateTime(history.processed_at) }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  ClockCircleOutlined,
  PlusCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import type { ApprovalTask, User } from '@/types'

const loading = ref(false)
const businessTypeFilter = ref<string>()
const priorityFilter = ref<string>()
const tableData = ref<ApprovalTask[]>([])
const userOptions = ref<User[]>([])
const selectedRowKeys = ref<string[]>([])
const approvalModalVisible = ref(false)
const detailModalVisible = ref(false)
const currentTask = ref<ApprovalTask | null>(null)
const approvalAction = ref<'APPROVE' | 'REJECT'>('APPROVE')
const approvalFormRef = ref()
const approvalHistory = ref([])

const taskStats = reactive({
  total: 15,
  today: 3,
  overdue: 2,
  weekCompleted: 28
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}))

const columns = [
  { title: '业务类型', key: 'business_type', width: 100 },
  { title: '业务单号', dataIndex: 'business_code', key: 'business_code', width: 150 },
  { title: '申请标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '申请人', dataIndex: 'applicant_name', key: 'applicant_name', width: 100 },
  { title: '申请金额', key: 'amount', width: 120 },
  { title: '优先级', key: 'priority', width: 80 },
  { title: '接收时间', key: 'received_at', width: 150 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const approvalForm = reactive({
  comment: '',
  next_action: 'AUTO',
  next_approver: ''
})

const approvalFormRules = computed(() => ({
  comment: approvalAction.value === 'REJECT' ? 
    [{ required: true, message: '请输入拒绝原因', trigger: 'blur' }] : []
}))

const getBusinessTypeColor = (type: string) => {
  const colors = {
    'EXPENSE': 'blue',
    'PRE_APPLICATION': 'green',
    'BUDGET_ADJUSTMENT': 'orange',
    'CONTRACT': 'purple',
    'PURCHASE': 'cyan'
  }
  return colors[type] || 'default'
}

const getBusinessTypeText = (type: string) => {
  const texts = {
    'EXPENSE': '报销申请',
    'PRE_APPLICATION': '事前申请',
    'BUDGET_ADJUSTMENT': '预算调整',
    'CONTRACT': '合同审批',
    'PURCHASE': '采购申请'
  }
  return texts[type] || type
}

const getPriorityColor = (priority: string) => {
  const colors = {
    'HIGH': 'red',
    'MEDIUM': 'orange',
    'LOW': 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const texts = {
    'HIGH': '高',
    'MEDIUM': '中',
    'LOW': '低'
  }
  return texts[priority] || priority
}

const getHistoryColor = (action: string) => {
  const colors = {
    'APPROVED': 'green',
    'REJECTED': 'red',
    'PENDING': 'blue'
  }
  return colors[action] || 'gray'
}

const getActionText = (action: string) => {
  const texts = {
    'APPROVED': '审批通过',
    'REJECTED': '审批拒绝',
    'PENDING': '待审批'
  }
  return texts[action] || action
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString()
}

const getTimeDuration = (receivedAt: string) => {
  const now = new Date()
  const received = new Date(receivedAt)
  const diffHours = Math.floor((now.getTime() - received.getTime()) / (1000 * 60 * 60))
  
  if (diffHours < 1) return '刚刚'
  if (diffHours < 24) return `${diffHours}小时前`
  
  const diffDays = Math.floor(diffHours / 24)
  return `${diffDays}天前`
}

const isOverdue = (receivedAt: string) => {
  const now = new Date()
  const received = new Date(receivedAt)
  const diffHours = (now.getTime() - received.getTime()) / (1000 * 60 * 60)
  return diffHours > 48 // 超过48小时算超时
}

const filterUser = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

const loadTasks = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取待办任务列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        task_id: 'TASK20240723001',
        flow_id: 'FLOW001',
        business_type: 'EXPENSE',
        business_id: 'BX20240723001',
        business_code: 'BX20240723001',
        title: '张三的北京出差费用报销',
        applicant_name: '张三',
        amount: 4850,
        priority: 'MEDIUM',
        received_at: '2024-07-23 10:30:00'
      },
      {
        id: '2',
        task_id: 'TASK20240723002',
        flow_id: 'FLOW002',
        business_type: 'PRE_APPLICATION',
        business_id: 'PRE20240723001',
        business_code: 'PRE20240723001',
        title: '李四的培训申请',
        applicant_name: '李四',
        amount: 8000,
        priority: 'HIGH',
        received_at: '2024-07-22 14:20:00'
      },
      {
        id: '3',
        task_id: 'TASK20240723003',
        flow_id: 'FLOW003',
        business_type: 'BUDGET_ADJUSTMENT',
        business_id: 'ADJ20240723001',
        business_code: 'ADJ20240723001',
        title: '医疗设备预算调整申请',
        applicant_name: '王五',
        amount: 100000,
        priority: 'HIGH',
        received_at: '2024-07-21 09:15:00'
      }
    ]
    pagination.total = 3
  } catch (error) {
    message.error('加载待办任务失败')
  } finally {
    loading.value = false
  }
}

const loadUsers = async () => {
  try {
    // TODO: 调用API获取用户列表
    userOptions.value = [
      {
        id: '1',
        user_name: '张三',
        department: { name: '内科' }
      },
      {
        id: '2',
        user_name: '李四',
        department: { name: '外科' }
      },
      {
        id: '3',
        user_name: '王五',
        department: { name: '财务部' }
      }
    ]
  } catch (error) {
    message.error('加载用户数据失败')
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadTasks()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadTasks()
}

const showApprovalModal = (task: ApprovalTask, action: 'APPROVE' | 'REJECT') => {
  currentTask.value = task
  approvalAction.value = action
  approvalModalVisible.value = true
  Object.assign(approvalForm, {
    comment: '',
    next_action: 'AUTO',
    next_approver: ''
  })
}

const viewDetails = async (task: ApprovalTask) => {
  currentTask.value = task
  
  // TODO: 加载审批历史
  approvalHistory.value = [
    {
      step_name: '申请提交',
      approver_name: task.applicant_name,
      action: 'SUBMITTED',
      comment: '提交申请',
      processed_at: '2024-07-23 09:30:00'
    },
    {
      step_name: '部门审批',
      approver_name: '部门主任',
      action: 'APPROVED',
      comment: '同意申请',
      processed_at: '2024-07-23 10:15:00'
    },
    {
      step_name: '财务审批',
      approver_name: '当前用户',
      action: 'PENDING',
      comment: '',
      processed_at: null
    }
  ]
  
  detailModalVisible.value = true
}

const handleApprovalSubmit = async () => {
  try {
    if (approvalAction.value === 'REJECT') {
      await approvalFormRef.value.validate()
    }
    
    // TODO: 调用审批API
    const actionText = approvalAction.value === 'APPROVE' ? '通过' : '拒绝'
    message.success(`审批${actionText}成功`)
    
    approvalModalVisible.value = false
    loadTasks()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleApprovalCancel = () => {
  approvalModalVisible.value = false
  approvalFormRef.value?.resetFields()
}

const batchApprove = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要批量审批的任务')
    return
  }
  
  // TODO: 实现批量审批功能
  message.info('批量审批功能开发中')
}

onMounted(() => {
  loadTasks()
  loadUsers()
})
</script>

<style scoped>
.approval-tasks {
  padding: 24px;
}

.stats-cards {
  margin-bottom: 24px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.time-info {
  line-height: 1.4;
}

.time-duration {
  font-size: 12px;
  color: #999;
}

.time-duration.overdue {
  color: #f5222d;
  font-weight: 500;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #666;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
}
</style>
