<template>
  <div class="asset-management">
    <a-card>
      <template #title>
        <span>资产管理</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            新建资产
          </a-button>
          <a-button @click="loadStatistics">
            <template #icon><BarChartOutlined /></template>
            统计分析
          </a-button>
          <a-button @click="exportAssets">
            <template #icon><ExportOutlined /></template>
            导出资产
          </a-button>
        </a-space>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-form">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-select
              v-model:value="searchForm.status"
              placeholder="选择状态"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="NORMAL">正常</a-select-option>
              <a-select-option value="USING">使用中</a-select-option>
              <a-select-option value="MAINTENANCE">维修中</a-select-option>
              <a-select-option value="SCRAPPED">已报废</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.category_id"
              placeholder="选择分类"
              allow-clear
              show-search
              @change="handleSearch"
            >
              <a-select-option
                v-for="category in categoryOptions"
                :key="category.id"
                :value="category.id"
              >
                {{ category.category_name }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.department_id"
              placeholder="选择部门"
              allow-clear
              show-search
              @change="handleSearch"
            >
              <a-select-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :value="dept.id"
              >
                {{ dept.name }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="资产名称/编号"
              @press-enter="handleSearch"
            />
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'purchase_price'">
            <span class="amount">¥{{ record.purchase_price?.toFixed(2) }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetails(record)">
                详情
              </a-button>
              <a-button type="link" size="small" @click="showMovementModal(record)">
                变动
              </a-button>
              <a-button type="link" size="small" @click="showMaintenanceModal(record)">
                维保
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="generateQRCode(record)">
                      <QrcodeOutlined /> 生成二维码
                    </a-menu-item>
                    <a-menu-item @click="printLabel(record)">
                      <PrinterOutlined /> 打印标签
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新建资产弹窗 -->
    <a-modal
      v-model:open="addModalVisible"
      title="新建资产"
      width="800px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="资产名称" name="asset_name">
              <a-input v-model:value="formData.asset_name" placeholder="请输入资产名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="资产分类" name="category_id">
              <a-select v-model:value="formData.category_id" placeholder="选择资产分类">
                <a-select-option
                  v-for="category in categoryOptions"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.category_name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="规格型号">
              <a-input v-model:value="formData.specification" placeholder="请输入规格型号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="品牌">
              <a-input v-model:value="formData.brand" placeholder="请输入品牌" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="采购价格" name="purchase_price">
              <a-input-number
                v-model:value="formData.purchase_price"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入采购价格"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属部门" name="department_id">
              <a-select v-model:value="formData.department_id" placeholder="选择所属部门">
                <a-select-option
                  v-for="dept in departmentOptions"
                  :key="dept.id"
                  :value="dept.id"
                >
                  {{ dept.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="资产详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentAsset">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="资产编号">
            {{ currentAsset.asset_code }}
          </a-descriptions-item>
          <a-descriptions-item label="资产名称">
            {{ currentAsset.asset_name }}
          </a-descriptions-item>
          <a-descriptions-item label="资产分类">
            {{ currentAsset.category?.category_name }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentAsset.status)">
              {{ getStatusText(currentAsset.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="规格型号">
            {{ currentAsset.specification || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="品牌">
            {{ currentAsset.brand || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="采购价格">
            <span class="amount">¥{{ currentAsset.purchase_price?.toFixed(2) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ currentAsset.department?.name }}
          </a-descriptions-item>
          <a-descriptions-item label="责任人">
            {{ currentAsset.responsible?.real_name || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="存放位置">
            {{ currentAsset.location?.location_name || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  BarChartOutlined,
  ExportOutlined,
  QrcodeOutlined,
  PrinterOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import {
  getAssetList,
  getAssetById,
  createAsset,
  getAssetStatistics,
  generateAssetQRCode,
  printAssetLabel,
  exportAssets,
  type Asset,
  type CreateAssetRequest
} from '@/api/asset'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref<Asset[]>([])
const addModalVisible = ref(false)
const detailModalVisible = ref(false)
const currentAsset = ref<Asset | null>(null)

// 搜索表单
const searchForm = reactive({
  status: '',
  category_id: '',
  department_id: '',
  keyword: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  { title: '资产编号', dataIndex: 'asset_code', key: 'asset_code', width: 150 },
  { title: '资产名称', dataIndex: 'asset_name', key: 'asset_name', width: 200 },
  { title: '分类', dataIndex: ['category', 'category_name'], key: 'category_name', width: 120 },
  { title: '规格型号', dataIndex: 'specification', key: 'specification', width: 150 },
  { title: '采购价格', dataIndex: 'purchase_price', key: 'purchase_price', width: 120, align: 'right' },
  { title: '所属部门', dataIndex: ['department', 'name'], key: 'department_name', width: 120 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 表单数据
const formData = reactive<CreateAssetRequest>({
  asset_name: '',
  category_id: '',
  specification: '',
  brand: '',
  purchase_price: 0,
  department_id: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  asset_name: [{ required: true, message: '请输入资产名称' }],
  category_id: [{ required: true, message: '请选择资产分类' }],
  purchase_price: [{ required: true, message: '请输入采购价格' }],
  department_id: [{ required: true, message: '请选择所属部门' }]
}

// 选项数据
const categoryOptions = ref<any[]>([])
const departmentOptions = ref<any[]>([])

// 表单引用
const formRef = ref()

// 方法定义
const loadAssets = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getAssetList(params)
    if (response.data.success) {
      tableData.value = response.data.data.items
      pagination.total = response.data.data.total
    }
  } catch (error) {
    message.error('加载资产列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadAssets()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadAssets()
}

const showAddModal = () => {
  addModalVisible.value = true
  Object.assign(formData, {
    asset_name: '',
    category_id: '',
    specification: '',
    brand: '',
    purchase_price: 0,
    department_id: '',
    remark: ''
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    const response = await createAsset(formData)
    if (response.data.success) {
      message.success('资产创建成功')
      addModalVisible.value = false
      loadAssets()
    }
  } catch (error) {
    message.error('创建资产失败')
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  addModalVisible.value = false
  formRef.value?.resetFields()
}

const viewDetails = async (record: Asset) => {
  try {
    const response = await getAssetById(record.id)
    if (response.data.success) {
      currentAsset.value = response.data.data
      detailModalVisible.value = true
    }
  } catch (error) {
    message.error('获取资产详情失败')
  }
}

const showMovementModal = (record: Asset) => {
  message.info('资产变动功能开发中')
}

const showMaintenanceModal = (record: Asset) => {
  message.info('资产维保功能开发中')
}

const loadStatistics = async () => {
  try {
    const response = await getAssetStatistics()
    if (response.data.success) {
      message.info('统计功能开发中')
    }
  } catch (error) {
    message.error('获取统计数据失败')
  }
}

const generateQRCode = async (record: Asset) => {
  try {
    await generateAssetQRCode(record.id)
    message.info('二维码生成功能开发中')
  } catch (error) {
    message.error('生成二维码失败')
  }
}

const printLabel = async (record: Asset) => {
  try {
    await printAssetLabel(record.id)
    message.info('标签打印功能开发中')
  } catch (error) {
    message.error('打印标签失败')
  }
}

const exportAssets = async () => {
  try {
    await exportAssets()
    message.info('导出功能开发中')
  } catch (error) {
    message.error('导出失败')
  }
}

// 工具方法
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'NORMAL': 'green',
    'USING': 'blue',
    'MAINTENANCE': 'orange',
    'SCRAPPED': 'red'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'NORMAL': '正常',
    'USING': '使用中',
    'MAINTENANCE': '维修中',
    'SCRAPPED': '已报废'
  }
  return textMap[status] || status
}

// 加载基础数据
const loadCategories = async () => {
  try {
    categoryOptions.value = [
      { id: '1', category_name: '医疗设备', category_code: 'MEDICAL' },
      { id: '2', category_name: '办公设备', category_code: 'OFFICE' },
      { id: '3', category_name: '车辆', category_code: 'VEHICLE' }
    ]
  } catch (error) {
    message.error('加载分类数据失败')
  }
}

const loadDepartments = async () => {
  try {
    departmentOptions.value = [
      { id: '1', name: '内科' },
      { id: '2', name: '外科' },
      { id: '3', name: '行政部' }
    ]
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

onMounted(() => {
  loadAssets()
  loadCategories()
  loadDepartments()
})
</script>

<style scoped>
.asset-management {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}
</style>
