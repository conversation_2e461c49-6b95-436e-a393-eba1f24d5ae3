package api

import (
	"hospital-management/internal/middleware"
	"hospital-management/internal/service"

	"github.com/gin-gonic/gin"
)

type Services struct {
	Auth        *service.AuthService
	User        *service.UserService
	Department  *service.DepartmentService
	Role        *service.RoleService
	Budget      *service.BudgetService
	Expense     *service.ExpenseService
	Approval    *service.ApprovalService
}

func RegisterRoutes(router *gin.Engine, services *Services, jwtSecret string) {
	// 创建处理器
	authHandler := NewAuthHandler(services.Auth)
	userHandler := NewUserHandler(services.User)
	departmentHandler := NewDepartmentHandler(services.Department)
	roleHandler := NewRoleHandler(services.Role)
	budgetHandler := NewBudgetHandler(services.Budget)
	expenseHandler := NewExpenseHandler(services.Expense)
	approvalHandler := NewApprovalHandler(services.Approval)

	// API路由组
	api := router.Group("/api")

	// 认证相关路由（无需认证）
	auth := api.Group("/auth")
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/logout", authHandler.Logout)
	}

	// 需要认证的路由
	protected := api.Group("")
	protected.Use(middleware.AuthMiddleware(services.Auth))
	{
		// 用户信息
		protected.GET("/profile/me", authHandler.GetUserInfo)

		// 用户管理
		users := protected.Group("/users")
		{
			users.GET("", userHandler.List)
			users.POST("", userHandler.Create)
			users.GET("/:id", userHandler.GetByID)
			users.PUT("/:id", userHandler.Update)
			users.DELETE("/:id", userHandler.Delete)
		}

		// 部门管理
		departments := protected.Group("/departments")
		{
			departments.GET("", departmentHandler.List)
			departments.GET("/tree", departmentHandler.GetTree)
			departments.POST("", departmentHandler.Create)
			departments.GET("/:id", departmentHandler.GetByID)
			departments.PUT("/:id", departmentHandler.Update)
			departments.DELETE("/:id", departmentHandler.Delete)
		}

		// 角色管理
		roles := protected.Group("/roles")
		{
			roles.GET("", roleHandler.List)
			roles.POST("", roleHandler.Create)
			roles.GET("/:id", roleHandler.GetByID)
			roles.PUT("/:id", roleHandler.Update)
			roles.DELETE("/:id", roleHandler.Delete)
		}

		// 预算管理
		budgets := protected.Group("/budgets")
		{
			budgets.GET("", budgetHandler.List)
			budgets.POST("", budgetHandler.Create)
			budgets.GET("/:id", budgetHandler.GetByID)
			budgets.PUT("/:id", budgetHandler.Update)
			budgets.DELETE("/:id", budgetHandler.Delete)

			// 预算项目
			budgets.GET("/:id/items", budgetHandler.GetItems)
			budgets.POST("/:id/items", budgetHandler.CreateItem)
		}

		// 支出控制
		expenses := protected.Group("/expense-applications")
		{
			expenses.GET("", expenseHandler.GetApplications)
			expenses.POST("", expenseHandler.CreateApplication)
			expenses.GET("/:id", expenseHandler.GetApplicationByID)
			expenses.PUT("/:id", expenseHandler.UpdateApplication)
			expenses.DELETE("/:id", expenseHandler.DeleteApplication)
		}

		// 审批管理
		approvals := protected.Group("/approvals")
		{
			approvals.GET("/tasks", approvalHandler.GetTasks)
			approvals.POST("/tasks/process", approvalHandler.ProcessTask)
			approvals.GET("/processed", approvalHandler.GetProcessedTasks)
			approvals.GET("/history/:businessId", approvalHandler.GetFlowHistory)
		}
	}
}
