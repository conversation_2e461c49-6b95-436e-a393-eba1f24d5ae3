package api

import (
	"hospital-management/internal/middleware"
	"hospital-management/internal/service"

	"github.com/gin-gonic/gin"
)

type Services struct {
	Auth        *service.AuthService
	User        *service.UserService
	Department  *service.DepartmentService
	Role        *service.RoleService
	Budget      *service.BudgetService
	Expense     *service.ExpenseService
	Approval    *service.ApprovalService
	Receipt     *service.ReceiptService
	Contract    *service.ContractService
	Asset       *service.AssetService
}

func RegisterRoutes(router *gin.Engine, services *Services, jwtSecret string) {
	// 创建处理器
	authHandler := NewAuthHandler(services.Auth)
	userHandler := NewUserHandler(services.User)
	departmentHandler := NewDepartmentHandler(services.Department)
	roleHandler := NewRoleHandler(services.Role)
	budgetHandler := NewBudgetHandler(services.Budget)
	expenseHandler := NewExpenseHandler(services.Expense)
	approvalHandler := NewApprovalHandler(services.Approval)
	receiptHandler := NewReceiptHandler(services.Receipt)
	contractHandler := NewContractHandler(services.Contract)
	assetHandler := NewAssetHandler(services.Asset)

	// API路由组
	api := router.Group("/api")

	// 认证相关路由（无需认证）
	auth := api.Group("/auth")
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/logout", authHandler.Logout)
	}

	// 需要认证的路由
	protected := api.Group("")
	protected.Use(middleware.AuthMiddleware(services.Auth))
	{
		// 用户信息
		protected.GET("/profile/me", authHandler.GetUserInfo)

		// 用户管理
		users := protected.Group("/users")
		{
			users.GET("", userHandler.List)
			users.POST("", userHandler.Create)
			users.GET("/:id", userHandler.GetByID)
			users.PUT("/:id", userHandler.Update)
			users.DELETE("/:id", userHandler.Delete)
		}

		// 部门管理
		departments := protected.Group("/departments")
		{
			departments.GET("", departmentHandler.List)
			departments.GET("/tree", departmentHandler.GetTree)
			departments.POST("", departmentHandler.Create)
			departments.GET("/:id", departmentHandler.GetByID)
			departments.PUT("/:id", departmentHandler.Update)
			departments.DELETE("/:id", departmentHandler.Delete)
		}

		// 角色管理
		roles := protected.Group("/roles")
		{
			roles.GET("", roleHandler.List)
			roles.POST("", roleHandler.Create)
			roles.GET("/:id", roleHandler.GetByID)
			roles.PUT("/:id", roleHandler.Update)
			roles.DELETE("/:id", roleHandler.Delete)
		}

		// 预算管理
		budgets := protected.Group("/budgets")
		{
			budgets.GET("", budgetHandler.List)
			budgets.POST("", budgetHandler.Create)
			budgets.GET("/:id", budgetHandler.GetByID)
			budgets.PUT("/:id", budgetHandler.Update)
			budgets.DELETE("/:id", budgetHandler.Delete)

			// 预算项目
			budgets.GET("/:id/items", budgetHandler.GetItems)
			budgets.POST("/:id/items", budgetHandler.CreateItem)
		}

		// 支出控制
		expenses := protected.Group("/expense-applications")
		{
			expenses.GET("", expenseHandler.GetApplications)
			expenses.POST("", expenseHandler.CreateApplication)
			expenses.GET("/:id", expenseHandler.GetApplicationByID)
			expenses.PUT("/:id", expenseHandler.UpdateApplication)
			expenses.DELETE("/:id", expenseHandler.DeleteApplication)
		}

		// 审批管理
		approvals := protected.Group("/approvals")
		{
			approvals.GET("/tasks", approvalHandler.GetTasks)
			approvals.POST("/tasks/process", approvalHandler.ProcessTask)
			approvals.GET("/processed", approvalHandler.GetProcessedTasks)
			approvals.GET("/history/:businessId", approvalHandler.GetFlowHistory)
		}

		// 入库验收管理
		receipts := protected.Group("/receipts")
		{
			receipts.GET("", receiptHandler.GetReceipts)
			receipts.POST("", receiptHandler.CreateReceipt)
			receipts.GET("/:id", receiptHandler.GetReceiptByID)
			receipts.POST("/:id/inspect", receiptHandler.InspectReceipt)
			receipts.PUT("/:id/status", receiptHandler.UpdateReceiptStatus)
			receipts.GET("/pending-inspection", receiptHandler.GetPendingInspectionReceipts)
			receipts.GET("/statistics", receiptHandler.GetReceiptStatistics)
			receipts.GET("/:id/export", receiptHandler.ExportReceipt)
			receipts.GET("/:id/print", receiptHandler.PrintReceipt)
		}

		// 合同管理
		contracts := protected.Group("/contracts")
		{
			contracts.GET("", contractHandler.GetContracts)
			contracts.POST("", contractHandler.CreateContract)
			contracts.GET("/:id", contractHandler.GetContractByID)
			contracts.PUT("/:id", contractHandler.UpdateContract)
			contracts.POST("/:id/submit", contractHandler.SubmitContract)
			contracts.POST("/:id/approve", contractHandler.ApproveContract)
			contracts.GET("/expiring", contractHandler.GetExpiringContracts)
			contracts.GET("/statistics", contractHandler.GetContractStatistics)
			contracts.GET("/payment-schedules/pending", contractHandler.GetPendingPaymentSchedules)
			contracts.GET("/:id/export", contractHandler.ExportContract)
			contracts.GET("/:id/print", contractHandler.PrintContract)
		}

		// 资产管理
		assets := protected.Group("/assets")
		{
			assets.GET("", assetHandler.GetAssets)
			assets.POST("", assetHandler.CreateAsset)
			assets.GET("/:id", assetHandler.GetAssetByID)
			assets.POST("/movements", assetHandler.CreateAssetMovement)
			assets.POST("/maintenances", assetHandler.CreateAssetMaintenance)
			assets.GET("/statistics", assetHandler.GetAssetStatistics)
			assets.GET("/maintenances/upcoming", assetHandler.GetUpcomingMaintenances)
			assets.GET("/warranties/expiring", assetHandler.GetExpiringWarranties)
			assets.GET("/:id/qrcode", assetHandler.GenerateAssetQRCode)
			assets.GET("/:id/label", assetHandler.PrintAssetLabel)
			assets.GET("/export", assetHandler.ExportAssets)
			assets.POST("/import", assetHandler.ImportAssets)
		}
	}
}
