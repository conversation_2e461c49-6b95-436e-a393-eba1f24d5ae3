package service

import (
	"fmt"
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
	"time"

	"github.com/google/uuid"
)

type ReceiptService struct {
	receiptRepo *repository.ReceiptRepository
	orderRepo   *repository.PurchaseOrderRepository
}

func NewReceiptService(receiptRepo *repository.ReceiptRepository, orderRepo *repository.PurchaseOrderRepository) *ReceiptService {
	return &ReceiptService{
		receiptRepo: receiptRepo,
		orderRepo:   orderRepo,
	}
}

// CreateReceiptRequest 创建入库单请求
type CreateReceiptRequest struct {
	OrderID     string        `json:"order_id" binding:"required"`
	ReceiverID  string        `json:"receiver_id" binding:"required"`
	ReceiptDate string        `json:"receipt_date" binding:"required"`
	Items       []ReceiptItem `json:"items" binding:"required"`
	Remark      string        `json:"remark"`
}

type ReceiptItem struct {
	ItemName         string  `json:"item_name" binding:"required"`
	ItemSpec         string  `json:"item_spec"`
	Unit             string  `json:"unit" binding:"required"`
	OrderedQuantity  int     `json:"ordered_quantity" binding:"required"`
	ReceivedQuantity int     `json:"received_quantity" binding:"required"`
	UnitPrice        float64 `json:"unit_price" binding:"required"`
	BatchNumber      string  `json:"batch_number"`
	ExpiryDate       string  `json:"expiry_date"`
}

// CreateReceipt 创建入库单
func (s *ReceiptService) CreateReceipt(userID string, req *CreateReceiptRequest) (*model.ReceiptRecord, error) {
	// 解析用户ID
	creatorID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("用户ID格式错误")
	}

	// 解析订单ID
	orderID, err := uuid.Parse(req.OrderID)
	if err != nil {
		return nil, fmt.Errorf("订单ID格式错误")
	}

	// 解析收货人ID
	receiverID, err := uuid.Parse(req.ReceiverID)
	if err != nil {
		return nil, fmt.Errorf("收货人ID格式错误")
	}

	// 获取采购订单信息
	order, err := s.orderRepo.GetByID(req.OrderID)
	if err != nil {
		return nil, fmt.Errorf("获取采购订单失败: %v", err)
	}

	// 解析收货日期
	receiptDate, err := time.Parse("2006-01-02", req.ReceiptDate)
	if err != nil {
		return nil, fmt.Errorf("收货日期格式错误")
	}

	// 计算总金额
	var totalAmount float64
	for _, item := range req.Items {
		totalAmount += float64(item.ReceivedQuantity) * item.UnitPrice
	}

	// 生成入库单号
	receiptCode := s.generateReceiptCode()

	// 创建入库单
	receipt := &model.ReceiptRecord{
		ReceiptCode: receiptCode,
		OrderID:     orderID,
		SupplierID:  order.SupplierID,
		ReceiverID:  receiverID,
		ReceiptDate: receiptDate,
		TotalAmount: totalAmount,
		Status:      model.ReceiptStatusReceived,
		Remark:      req.Remark,
		CreatedBy:   &creatorID,
	}

	// 创建入库明细
	var receiptItems []model.ReceiptItem
	for _, item := range req.Items {
		totalPrice := float64(item.ReceivedQuantity) * item.UnitPrice

		receiptItem := model.ReceiptItem{
			ItemName:         item.ItemName,
			ItemSpec:         item.ItemSpec,
			Unit:             item.Unit,
			OrderedQuantity:  item.OrderedQuantity,
			ReceivedQuantity: item.ReceivedQuantity,
			UnitPrice:        item.UnitPrice,
			TotalPrice:       totalPrice,
			BatchNumber:      item.BatchNumber,
			InspectResult:    model.InspectResultPending,
			CreatedBy:        &creatorID,
		}

		// 处理有效期
		if item.ExpiryDate != "" {
			expiryDate, err := time.Parse("2006-01-02", item.ExpiryDate)
			if err == nil {
				receiptItem.ExpiryDate = &expiryDate
			}
		}

		receiptItems = append(receiptItems, receiptItem)
	}

	receipt.ReceiptItems = receiptItems

	// 保存到数据库
	if err := s.receiptRepo.CreateReceipt(receipt); err != nil {
		return nil, fmt.Errorf("创建入库单失败: %v", err)
	}

	return receipt, nil
}

// GetReceiptByID 根据ID获取入库单
func (s *ReceiptService) GetReceiptByID(id string) (*model.ReceiptRecord, error) {
	return s.receiptRepo.GetReceiptByID(id)
}

// GetReceipts 获取入库单列表
func (s *ReceiptService) GetReceipts(page, pageSize int, status, supplierID, startDate, endDate string) ([]model.ReceiptRecord, int64, error) {
	offset := (page - 1) * pageSize
	return s.receiptRepo.GetReceipts(offset, pageSize, status, supplierID, startDate, endDate)
}

// InspectReceiptRequest 质检请求
type InspectReceiptRequest struct {
	InspectorID string              `json:"inspector_id" binding:"required"`
	Items       []InspectItemResult `json:"items" binding:"required"`
	Remark      string              `json:"remark"`
}

type InspectItemResult struct {
	ItemID            string `json:"item_id" binding:"required"`
	QualifiedQuantity int    `json:"qualified_quantity" binding:"required"`
	InspectResult     string `json:"inspect_result" binding:"required"`
	InspectRemark     string `json:"inspect_remark"`
}

// InspectReceipt 质检入库单
func (s *ReceiptService) InspectReceipt(receiptID string, req *InspectReceiptRequest) error {
	// 解析质检员ID
	inspectorID, err := uuid.Parse(req.InspectorID)
	if err != nil {
		return fmt.Errorf("质检员ID格式错误")
	}

	// 获取入库单
	receipt, err := s.receiptRepo.GetReceiptByID(receiptID)
	if err != nil {
		return fmt.Errorf("获取入库单失败: %v", err)
	}

	// 检查状态
	if receipt.Status != model.ReceiptStatusReceived && receipt.Status != model.ReceiptStatusInspecting {
		return fmt.Errorf("入库单状态不允许质检")
	}

	// 更新入库明细的质检结果
	var updatedItems []model.ReceiptItem
	allPassed := true
	for _, itemResult := range req.Items {
		for i, item := range receipt.ReceiptItems {
			if item.ID.String() == itemResult.ItemID {
				receipt.ReceiptItems[i].QualifiedQuantity = itemResult.QualifiedQuantity
				receipt.ReceiptItems[i].InspectResult = itemResult.InspectResult
				receipt.ReceiptItems[i].InspectRemark = itemResult.InspectRemark
				receipt.ReceiptItems[i].UpdatedBy = &inspectorID

				if itemResult.InspectResult != model.InspectResultPassed {
					allPassed = false
				}

				updatedItems = append(updatedItems, receipt.ReceiptItems[i])
				break
			}
		}
	}

	// 批量更新入库明细
	if err := s.receiptRepo.BatchUpdateReceiptItems(updatedItems); err != nil {
		return fmt.Errorf("更新入库明细失败: %v", err)
	}

	// 更新入库单状态和质检信息
	now := time.Now()
	receipt.InspectorID = &inspectorID
	receipt.InspectDate = &now
	receipt.UpdatedBy = &inspectorID

	if allPassed {
		receipt.Status = model.ReceiptStatusPassed
	} else {
		receipt.Status = model.ReceiptStatusRejected
	}

	if err := s.receiptRepo.UpdateReceipt(receipt); err != nil {
		return fmt.Errorf("更新入库单失败: %v", err)
	}

	// 创建质检记录
	inspectionRecord := &model.InspectionRecord{
		ReceiptID:     receipt.ID,
		InspectorID:   inspectorID,
		InspectDate:   now,
		InspectResult: receipt.Status,
		Remark:        req.Remark,
		CreatedBy:     &inspectorID,
	}

	if err := s.receiptRepo.CreateInspectionRecord(inspectionRecord); err != nil {
		return fmt.Errorf("创建质检记录失败: %v", err)
	}

	// TODO: 如果验收通过，触发后续流程（付款申请、资产登记等）
	if receipt.Status == model.ReceiptStatusPassed {
		s.triggerPostReceiptProcesses(receipt)
	}

	return nil
}

// GetPendingInspectionReceipts 获取待质检的入库单
func (s *ReceiptService) GetPendingInspectionReceipts(inspectorID string) ([]model.ReceiptRecord, error) {
	return s.receiptRepo.GetPendingInspectionReceipts(inspectorID)
}

// GetReceiptStatistics 获取入库统计数据
func (s *ReceiptService) GetReceiptStatistics(startDate, endDate string) (map[string]interface{}, error) {
	return s.receiptRepo.GetReceiptStatistics(startDate, endDate)
}

// generateReceiptCode 生成入库单号
func (s *ReceiptService) generateReceiptCode() string {
	now := time.Now()
	return fmt.Sprintf("RK%s%04d", now.Format("20060102"), now.Unix()%10000)
}

// triggerPostReceiptProcesses 触发后续流程
func (s *ReceiptService) triggerPostReceiptProcesses(receipt *model.ReceiptRecord) {
	// TODO: 实现后续流程
	// 1. 生成付款申请
	// 2. 创建资产登记
	// 3. 发送通知
}
