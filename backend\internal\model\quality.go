package model

import (
	"time"
)

// QualityIndicator 质量指标表
type QualityIndicator struct {
	ID                 uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Name               string    `json:"name" gorm:"size:200;not null"`
	Code               string    `json:"code" gorm:"uniqueIndex;size:100;not null"`
	Category           string    `json:"category" gorm:"size:100;not null"`
	TargetValue        float64   `json:"target_value" gorm:"type:decimal(10,2);not null"`
	Unit               string    `json:"unit" gorm:"size:20"`
	CalculationMethod  string    `json:"calculation_method" gorm:"type:text"`
	DataSource         string    `json:"data_source" gorm:"size:200"`
	CollectionFrequency string   `json:"collection_frequency" gorm:"size:50"`
	ResponsibleDept    string    `json:"responsible_dept" gorm:"size:100"`
	Description        string    `json:"description" gorm:"type:text"`
	Status             int       `json:"status" gorm:"default:1;comment:1-启用,0-禁用"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`

	// 关联
	QualityData []QualityData `json:"quality_data" gorm:"foreignKey:IndicatorID"`
}

// QualityData 质量数据表
type QualityData struct {
	ID           uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	IndicatorID  uint      `json:"indicator_id" gorm:"not null"`
	Period       string    `json:"period" gorm:"size:20;not null;comment:数据周期,如2024-01"`
	ActualValue  float64   `json:"actual_value" gorm:"type:decimal(10,2);not null"`
	TargetValue  float64   `json:"target_value" gorm:"type:decimal(10,2);not null"`
	Variance     float64   `json:"variance" gorm:"type:decimal(10,2)"`
	VarianceRate float64   `json:"variance_rate" gorm:"type:decimal(5,2)"`
	Status       string    `json:"status" gorm:"size:20;default:'normal';comment:normal,warning,critical"`
	Remarks      string    `json:"remarks" gorm:"type:text"`
	CollectorID  uint      `json:"collector_id" gorm:"not null"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 关联
	Indicator QualityIndicator `json:"indicator" gorm:"foreignKey:IndicatorID"`
	Collector User             `json:"collector" gorm:"foreignKey:CollectorID"`
}

// PerformanceIndicator 绩效指标表
type PerformanceIndicator struct {
	ID                 uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Name               string    `json:"name" gorm:"size:200;not null"`
	Code               string    `json:"code" gorm:"uniqueIndex;size:100;not null"`
	Category           string    `json:"category" gorm:"size:100;not null"`
	Weight             float64   `json:"weight" gorm:"type:decimal(5,2);not null"`
	TargetValue        float64   `json:"target_value" gorm:"type:decimal(10,2);not null"`
	Unit               string    `json:"unit" gorm:"size:20"`
	CalculationMethod  string    `json:"calculation_method" gorm:"type:text"`
	DataSource         string    `json:"data_source" gorm:"size:200"`
	EvaluationFrequency string   `json:"evaluation_frequency" gorm:"size:50"`
	ResponsibleDept    string    `json:"responsible_dept" gorm:"size:100"`
	Description        string    `json:"description" gorm:"type:text"`
	Status             int       `json:"status" gorm:"default:1;comment:1-启用,0-禁用"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`

	// 关联
	PerformanceData []PerformanceData `json:"performance_data" gorm:"foreignKey:IndicatorID"`
}

// PerformanceData 绩效数据表
type PerformanceData struct {
	ID           uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	IndicatorID  uint      `json:"indicator_id" gorm:"not null"`
	DepartmentID uint      `json:"department_id" gorm:"not null"`
	Period       string    `json:"period" gorm:"size:20;not null;comment:评估周期,如2024-Q1"`
	ActualValue  float64   `json:"actual_value" gorm:"type:decimal(10,2);not null"`
	TargetValue  float64   `json:"target_value" gorm:"type:decimal(10,2);not null"`
	Score        float64   `json:"score" gorm:"type:decimal(5,2)"`
	Grade        string    `json:"grade" gorm:"size:10;comment:A,B,C,D"`
	Remarks      string    `json:"remarks" gorm:"type:text"`
	EvaluatorID  uint      `json:"evaluator_id" gorm:"not null"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 关联
	Indicator  PerformanceIndicator `json:"indicator" gorm:"foreignKey:IndicatorID"`
	Department Department           `json:"department" gorm:"foreignKey:DepartmentID"`
	Evaluator  User                 `json:"evaluator" gorm:"foreignKey:EvaluatorID"`
}
