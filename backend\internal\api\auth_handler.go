package api

import (
	"hospital-management/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AuthHandler struct {
	authService *service.AuthService
}

func NewAuthHandler(authService *service.AuthService) *AuthHandler {
	return &AuthHandler{authService: authService}
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req service.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    40000,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	resp, err := h.authService.Login(&req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40001,
			"message": err.<PERSON>(),
			"data":    nil,
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "登录成功",
		"data":    resp,
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "登出成功",
		"data":    nil,
	})
}

// GetUserInfo 获取用户信息
func (h *AuthHandler) GetUserInfo(c *gin.Context) {
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40001,
			"message": "未授权",
			"data":    nil,
		})
		return
	}

	user, err := h.authService.GetUserInfo(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40004,
			"message": "用户不存在",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "获取成功",
		"data":    user,
	})
}
