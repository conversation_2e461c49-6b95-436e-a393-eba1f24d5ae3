package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Supplier 供应商表 (tbl_suppliers)
type Supplier struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"size:255;not null;uniqueIndex;comment:供应商名称"`
	CreditCode  string    `json:"credit_code" gorm:"size:100;uniqueIndex;comment:社会统一信用代码"`
	Status      int       `json:"status" gorm:"not null;default:1;comment:状态 1-合作中 0-黑名单"`
	ContactInfo string    `json:"contact_info" gorm:"type:jsonb;comment:联系人、地址等信息JSON"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy   *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`
}

// PurchaseRequisition 采购需求单 (tbl_purchase_requisitions)
type PurchaseRequisition struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	RequisitionCode string    `json:"requisition_code" gorm:"size:50;uniqueIndex;not null;comment:需求单号"`
	ApplicantID     uuid.UUID `json:"applicant_id" gorm:"type:uuid;not null;comment:申请人"`
	DepartmentID    uuid.UUID `json:"department_id" gorm:"type:uuid;not null;comment:需求部门"`
	Title           string    `json:"title" gorm:"size:255;not null;comment:采购事由"`
	Details         string    `json:"details" gorm:"type:jsonb;comment:需求明细JSON"`
	TotalAmount     float64   `json:"total_amount" gorm:"type:decimal(18,2);comment:预估总金额"`
	Status          string    `json:"status" gorm:"size:30;not null;default:'PENDING';comment:状态 PENDING/APPROVED/REJECTED/COMPLETED"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy       *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy       *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Applicant  User       `json:"applicant" gorm:"foreignKey:ApplicantID"`
	Department Department `json:"department" gorm:"foreignKey:DepartmentID"`
}

// PurchaseOrder 采购订单 (tbl_purchase_orders)
type PurchaseOrder struct {
	ID           uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	OrderCode    string     `json:"order_code" gorm:"size:50;uniqueIndex;not null;comment:订单号"`
	SupplierID   uuid.UUID  `json:"supplier_id" gorm:"type:uuid;not null;comment:供应商ID"`
	ContractID   *uuid.UUID `json:"contract_id" gorm:"type:uuid;comment:关联的采购合同ID"`
	TotalAmount  float64    `json:"total_amount" gorm:"type:decimal(18,2);not null;comment:订单总金额"`
	Status       string     `json:"status" gorm:"size:30;not null;default:'PENDING';comment:状态 PENDING/DELIVERED/COMPLETED"`
	OrderDetails string     `json:"order_details" gorm:"type:jsonb;comment:订单明细JSON"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy    *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy    *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Supplier Supplier `json:"supplier" gorm:"foreignKey:SupplierID"`
}

// Contract 合同主表 (tbl_contracts)
type Contract struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ContractCode     string    `json:"contract_code" gorm:"size:50;uniqueIndex;not null;comment:合同编号"`
	ContractName     string    `json:"contract_name" gorm:"size:255;not null;comment:合同名称"`
	ContractType     string    `json:"contract_type" gorm:"size:50;not null;comment:合同类型 采购/服务/基建"`
	CounterpartyID   *uuid.UUID `json:"counterparty_id" gorm:"type:uuid;comment:对方单位ID"`
	CounterpartyName string    `json:"counterparty_name" gorm:"size:255;not null;comment:对方单位名称"`
	TotalAmount      float64   `json:"total_amount" gorm:"type:decimal(18,2);not null;comment:合同总金额"`
	StartDate        *time.Time `json:"start_date" gorm:"comment:合同开始日期"`
	EndDate          *time.Time `json:"end_date" gorm:"comment:合同结束日期"`
	Status           string    `json:"status" gorm:"size:30;not null;default:'DRAFT';comment:状态 DRAFT/PENDING/ACTIVE/COMPLETED"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy        *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy        *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Counterparty     *Supplier                  `json:"counterparty" gorm:"foreignKey:CounterpartyID"`
	PaymentSchedules []ContractPaymentSchedule  `json:"payment_schedules" gorm:"foreignKey:ContractID"`
}

// ContractPaymentSchedule 合同付款计划表 (tbl_contract_payment_schedules)
type ContractPaymentSchedule struct {
	ID         uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ContractID uuid.UUID  `json:"contract_id" gorm:"type:uuid;not null;comment:所属合同"`
	PhaseName  string     `json:"phase_name" gorm:"size:100;comment:付款阶段名称"`
	DueDate    *time.Time `json:"due_date" gorm:"comment:预计付款日期"`
	Amount     float64    `json:"amount" gorm:"type:decimal(18,2);not null;comment:计划付款金额"`
	Status     string     `json:"status" gorm:"size:30;not null;default:'PENDING';comment:状态 PENDING/PAID"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy  *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy  *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Contract Contract `json:"contract" gorm:"foreignKey:ContractID"`
}
