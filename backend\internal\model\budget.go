package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Budget 预算主表 (tbl_budgets)
type Budget struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	BudgetYear   int       `json:"budget_year" gorm:"not null;comment:预算年度"`
	DepartmentID uuid.UUID `json:"department_id" gorm:"type:uuid;not null;comment:部门ID"`
	TotalAmount  float64   `json:"total_amount" gorm:"type:decimal(18,2);not null;comment:预算总金额"`
	Status       string    `json:"status" gorm:"size:30;not null;default:'DRAFT';comment:状态 DRAFT/PENDING/APPROVED/ACTIVE"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy    *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy    *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Department  Department   `json:"department" gorm:"foreignKey:DepartmentID"`
	BudgetItems []BudgetItem `json:"budget_items" gorm:"foreignKey:BudgetID"`
}

// BudgetItem 预算明细表 (tbl_budget_items)
type BudgetItem struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	BudgetID        uuid.UUID `json:"budget_id" gorm:"type:uuid;not null;comment:预算主表ID"`
	ItemName        string    `json:"item_name" gorm:"size:200;not null;comment:预算项目名称"`
	ItemCode        string    `json:"item_code" gorm:"size:100;not null;comment:预算项目编码"`
	Category        string    `json:"category" gorm:"size:100;not null;comment:预算分类"`
	BudgetAmount    float64   `json:"budget_amount" gorm:"type:decimal(18,2);not null;comment:预算金额"`
	UsedAmount      float64   `json:"used_amount" gorm:"type:decimal(18,2);default:0;comment:已使用金额"`
	FrozenAmount    float64   `json:"frozen_amount" gorm:"type:decimal(18,2);default:0;comment:冻结金额"`
	AvailableAmount float64   `json:"available_amount" gorm:"type:decimal(18,2);comment:可用金额"`
	Description     string    `json:"description" gorm:"type:text;comment:描述"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy       *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy       *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Budget Budget `json:"budget" gorm:"foreignKey:BudgetID"`
}

// BudgetAdjustment 预算调整表 (tbl_budget_adjustments)
type BudgetAdjustment struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	AdjustmentCode string    `json:"adjustment_code" gorm:"size:50;uniqueIndex;not null;comment:调整单号"`
	BudgetItemID   uuid.UUID `json:"budget_item_id" gorm:"type:uuid;not null;comment:预算项目ID"`
	AdjustmentType string    `json:"adjustment_type" gorm:"size:30;not null;comment:调整类型 INCREASE/DECREASE/TRANSFER"`
	Amount         float64   `json:"amount" gorm:"type:decimal(18,2);not null;comment:调整金额"`
	Reason         string    `json:"reason" gorm:"type:text;not null;comment:调整原因"`
	Status         string    `json:"status" gorm:"size:30;not null;default:'PENDING';comment:状态 PENDING/APPROVED/REJECTED"`
	ApplicantID    uuid.UUID `json:"applicant_id" gorm:"type:uuid;not null;comment:申请人ID"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy      *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy      *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	BudgetItem Budget `json:"budget_item" gorm:"foreignKey:BudgetItemID"`
	Applicant  User   `json:"applicant" gorm:"foreignKey:ApplicantID"`
}
