import api from './index'
import type { LoginForm, LoginResponse, User } from '@/types'

// 登录
export const login = (data: LoginForm) => {
  return api.post<LoginResponse>('/auth/login', data)
}

// 登出
export const logout = () => {
  return api.post('/auth/logout')
}

// 获取用户信息
export const getUserInfo = () => {
  return api.get<User>('/auth/user')
}

// 刷新token
export const refreshToken = () => {
  return api.post<{ token: string }>('/auth/refresh')
}
