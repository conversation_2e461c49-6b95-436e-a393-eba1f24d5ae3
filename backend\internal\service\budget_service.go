package service

import (
	"fmt"
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
)

type BudgetService struct {
	budgetRepo *repository.BudgetRepository
}

func NewBudgetService(budgetRepo *repository.BudgetRepository) *BudgetService {
	return &BudgetService{budgetRepo: budgetRepo}
}

func (s *BudgetService) CreateBudget(budget *model.Budget) error {
	// 计算预算项目的可用金额
	for i := range budget.BudgetItems {
		item := &budget.BudgetItems[i]
		item.AvailableAmount = item.BudgetAmount - item.UsedAmount - item.FrozenAmount
	}
	
	return s.budgetRepo.CreateBudget(budget)
}

func (s *BudgetService) GetBudgetByID(id string) (*model.Budget, error) {
	return s.budgetRepo.GetBudgetByID(id)
}

func (s *BudgetService) UpdateBudget(budget *model.Budget) error {
	return s.budgetRepo.UpdateBudget(budget)
}

func (s *BudgetService) DeleteBudget(id string) error {
	return s.budgetRepo.DeleteBudget(id)
}

func (s *BudgetService) GetBudgets(page, pageSize, year int, departmentID string) ([]model.Budget, int64, error) {
	offset := (page - 1) * pageSize
	
	if departmentID != "" {
		// 按部门查询
		budgets, err := s.budgetRepo.GetBudgetsByDepartment(departmentID, year)
		return budgets, int64(len(budgets)), err
	}
	
	// 按年度查询
	if year == 0 {
		year = 2024 // 默认当前年度
	}
	return s.budgetRepo.GetBudgetsByYear(year, offset, pageSize)
}

func (s *BudgetService) CreateBudgetItem(item *model.BudgetItem) error {
	// 计算可用金额
	item.AvailableAmount = item.BudgetAmount - item.UsedAmount - item.FrozenAmount
	return s.budgetRepo.CreateBudgetItem(item)
}

func (s *BudgetService) GetBudgetItemByID(id string) (*model.BudgetItem, error) {
	return s.budgetRepo.GetBudgetItemByID(id)
}

func (s *BudgetService) UpdateBudgetItem(item *model.BudgetItem) error {
	// 重新计算可用金额
	item.AvailableAmount = item.BudgetAmount - item.UsedAmount - item.FrozenAmount
	return s.budgetRepo.UpdateBudgetItem(item)
}

func (s *BudgetService) DeleteBudgetItem(id string) error {
	return s.budgetRepo.DeleteBudgetItem(id)
}

func (s *BudgetService) GetBudgetItems(budgetID string) ([]model.BudgetItem, error) {
	return s.budgetRepo.GetBudgetItemsByBudget(budgetID)
}

func (s *BudgetService) FreezeBudgetAmount(itemID string, amount float64) error {
	// 检查可用金额是否足够
	item, err := s.budgetRepo.GetBudgetItemByID(itemID)
	if err != nil {
		return err
	}
	
	if item.AvailableAmount < amount {
		return fmt.Errorf("预算余额不足，可用金额：%.2f，申请金额：%.2f", item.AvailableAmount, amount)
	}
	
	return s.budgetRepo.FreezeBudgetAmount(itemID, amount)
}

func (s *BudgetService) ReleaseBudgetAmount(itemID string, amount float64) error {
	return s.budgetRepo.ReleaseBudgetAmount(itemID, amount)
}

func (s *BudgetService) UseBudgetAmount(itemID string, amount float64) error {
	return s.budgetRepo.UseBudgetAmount(itemID, amount)
}

func (s *BudgetService) CreateBudgetAdjustment(adjustment *model.BudgetAdjustment) error {
	return s.budgetRepo.CreateBudgetAdjustment(adjustment)
}

func (s *BudgetService) GetBudgetAdjustmentByID(id string) (*model.BudgetAdjustment, error) {
	return s.budgetRepo.GetBudgetAdjustmentByID(id)
}

func (s *BudgetService) UpdateBudgetAdjustment(adjustment *model.BudgetAdjustment) error {
	return s.budgetRepo.UpdateBudgetAdjustment(adjustment)
}

func (s *BudgetService) GetBudgetAdjustmentsByStatus(status string, page, pageSize int) ([]model.BudgetAdjustment, int64, error) {
	offset := (page - 1) * pageSize
	return s.budgetRepo.GetBudgetAdjustmentsByStatus(status, offset, pageSize)
}
