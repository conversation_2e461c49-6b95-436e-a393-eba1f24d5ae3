package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type PurchaseOrderRepository struct {
	db *gorm.DB
}

func NewPurchaseOrderRepository(db *gorm.DB) *PurchaseOrderRepository {
	return &PurchaseOrderRepository{db: db}
}

// Create 创建采购订单
func (r *PurchaseOrderRepository) Create(order *model.PurchaseOrder) error {
	return r.db.Create(order).Error
}

// GetByID 根据ID获取采购订单
func (r *PurchaseOrderRepository) GetByID(id string) (*model.PurchaseOrder, error) {
	var order model.PurchaseOrder
	err := r.db.Preload("Supplier").Where("id = ?", id).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// GetByCode 根据订单号获取采购订单
func (r *PurchaseOrderRepository) GetByCode(code string) (*model.PurchaseOrder, error) {
	var order model.PurchaseOrder
	err := r.db.Preload("Supplier").Where("order_code = ?", code).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// Update 更新采购订单
func (r *PurchaseOrderRepository) Update(order *model.PurchaseOrder) error {
	return r.db.Save(order).Error
}

// Delete 删除采购订单
func (r *PurchaseOrderRepository) Delete(id string) error {
	return r.db.Where("id = ?", id).Delete(&model.PurchaseOrder{}).Error
}

// List 获取采购订单列表
func (r *PurchaseOrderRepository) List(offset, limit int, status, supplierID string) ([]model.PurchaseOrder, int64, error) {
	var orders []model.PurchaseOrder
	var total int64

	query := r.db.Model(&model.PurchaseOrder{}).Preload("Supplier")

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 供应商筛选
	if supplierID != "" {
		query = query.Where("supplier_id = ?", supplierID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&orders).Error
	return orders, total, err
}

// UpdateStatus 更新订单状态
func (r *PurchaseOrderRepository) UpdateStatus(id, status string) error {
	return r.db.Model(&model.PurchaseOrder{}).Where("id = ?", id).Update("status", status).Error
}

// GetOrdersBySupplier 根据供应商获取订单列表
func (r *PurchaseOrderRepository) GetOrdersBySupplier(supplierID string) ([]model.PurchaseOrder, error) {
	var orders []model.PurchaseOrder
	err := r.db.Where("supplier_id = ?", supplierID).Find(&orders).Error
	return orders, err
}

// SupplierRepository 供应商仓储
type SupplierRepository struct {
	db *gorm.DB
}

func NewSupplierRepository(db *gorm.DB) *SupplierRepository {
	return &SupplierRepository{db: db}
}

// Create 创建供应商
func (r *SupplierRepository) Create(supplier *model.Supplier) error {
	return r.db.Create(supplier).Error
}

// GetByID 根据ID获取供应商
func (r *SupplierRepository) GetByID(id string) (*model.Supplier, error) {
	var supplier model.Supplier
	err := r.db.Where("id = ?", id).First(&supplier).Error
	if err != nil {
		return nil, err
	}
	return &supplier, nil
}

// Update 更新供应商
func (r *SupplierRepository) Update(supplier *model.Supplier) error {
	return r.db.Save(supplier).Error
}

// Delete 删除供应商
func (r *SupplierRepository) Delete(id string) error {
	return r.db.Where("id = ?", id).Delete(&model.Supplier{}).Error
}

// List 获取供应商列表
func (r *SupplierRepository) List(offset, limit int, status int, keyword string) ([]model.Supplier, int64, error) {
	var suppliers []model.Supplier
	var total int64

	query := r.db.Model(&model.Supplier{})

	// 状态筛选
	if status >= 0 {
		query = query.Where("status = ?", status)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("name LIKE ? OR credit_code LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&suppliers).Error
	return suppliers, total, err
}

// PurchaseRequisitionRepository 采购需求单仓储
type PurchaseRequisitionRepository struct {
	db *gorm.DB
}

func NewPurchaseRequisitionRepository(db *gorm.DB) *PurchaseRequisitionRepository {
	return &PurchaseRequisitionRepository{db: db}
}

// Create 创建采购需求单
func (r *PurchaseRequisitionRepository) Create(requisition *model.PurchaseRequisition) error {
	return r.db.Create(requisition).Error
}

// GetByID 根据ID获取采购需求单
func (r *PurchaseRequisitionRepository) GetByID(id string) (*model.PurchaseRequisition, error) {
	var requisition model.PurchaseRequisition
	err := r.db.Preload("Applicant").Preload("Department").Where("id = ?", id).First(&requisition).Error
	if err != nil {
		return nil, err
	}
	return &requisition, nil
}

// Update 更新采购需求单
func (r *PurchaseRequisitionRepository) Update(requisition *model.PurchaseRequisition) error {
	return r.db.Save(requisition).Error
}

// Delete 删除采购需求单
func (r *PurchaseRequisitionRepository) Delete(id string) error {
	return r.db.Where("id = ?", id).Delete(&model.PurchaseRequisition{}).Error
}

// List 获取采购需求单列表
func (r *PurchaseRequisitionRepository) List(offset, limit int, status, departmentID string) ([]model.PurchaseRequisition, int64, error) {
	var requisitions []model.PurchaseRequisition
	var total int64

	query := r.db.Model(&model.PurchaseRequisition{}).Preload("Applicant").Preload("Department")

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 部门筛选
	if departmentID != "" {
		query = query.Where("department_id = ?", departmentID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&requisitions).Error
	return requisitions, total, err
}
