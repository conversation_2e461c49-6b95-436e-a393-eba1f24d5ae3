package service

import (
	"fmt"
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
	"time"

	"github.com/google/uuid"
)

type ExpenseService struct {
	expenseRepo *repository.ExpenseRepository
	budgetRepo  *repository.BudgetRepository
}

func NewExpenseService(expenseRepo *repository.ExpenseRepository, budgetRepo *repository.BudgetRepository) *ExpenseService {
	return &ExpenseService{
		expenseRepo: expenseRepo,
		budgetRepo:  budgetRepo,
	}
}

type CreateApplicationRequest struct {
	Title        string `json:"title"`
	DepartmentID string `json:"department_id"`
	Details      []struct {
		BudgetItemID string  `json:"budget_item_id"`
		Description  string  `json:"description"`
		Amount       float64 `json:"amount"`
		ExpenseDate  string  `json:"expense_date"`
		InvoiceInfo  string  `json:"invoice_info"`
	} `json:"details"`
	Attachments []string `json:"attachments"`
}

func (s *ExpenseService) CreateApplication(userID string, req *CreateApplicationRequest) (*model.ExpenseApplication, error) {
	// 解析用户ID和部门ID
	applicantID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("用户ID格式错误")
	}

	departmentID, err := uuid.Parse(req.DepartmentID)
	if err != nil {
		return nil, fmt.Errorf("部门ID格式错误")
	}

	// 计算总金额
	var totalAmount float64
	for _, detail := range req.Details {
		totalAmount += detail.Amount
	}

	// 生成申请单号
	applicationCode := s.generateApplicationCode()

	// 创建报销申请
	application := &model.ExpenseApplication{
		ApplicationCode: applicationCode,
		Title:           req.Title,
		ApplicantID:     applicantID,
		DepartmentID:    departmentID,
		TotalAmount:     totalAmount,
		Status:          "DRAFT",
		CreatedBy:       &applicantID,
	}

	// 创建报销明细
	var expenseDetails []model.ExpenseDetail
	for _, detail := range req.Details {
		budgetItemID, err := uuid.Parse(detail.BudgetItemID)
		if err != nil {
			return nil, fmt.Errorf("预算项目ID格式错误")
		}

		expenseDate, err := time.Parse("2006-01-02", detail.ExpenseDate)
		if err != nil {
			return nil, fmt.Errorf("费用发生日期格式错误")
		}

		expenseDetail := model.ExpenseDetail{
			BudgetItemID: budgetItemID,
			Description:  detail.Description,
			Amount:       detail.Amount,
			ExpenseDate:  expenseDate,
			InvoiceInfo:  detail.InvoiceInfo,
			CreatedBy:    &applicantID,
		}
		expenseDetails = append(expenseDetails, expenseDetail)
	}

	application.ExpenseDetails = expenseDetails

	// 保存到数据库
	if err := s.expenseRepo.CreateApplication(application); err != nil {
		return nil, fmt.Errorf("创建报销申请失败: %v", err)
	}

	// TODO: 启动审批流程
	// TODO: 冻结预算额度

	return application, nil
}

func (s *ExpenseService) GetApplicationsByUser(userID string, page, pageSize int, status, startDate, endDate string) ([]model.ExpenseApplication, int64, error) {
	offset := (page - 1) * pageSize
	return s.expenseRepo.GetApplicationsByUser(userID, offset, pageSize, status, startDate, endDate)
}

func (s *ExpenseService) GetApplicationByID(id string) (*model.ExpenseApplication, error) {
	return s.expenseRepo.GetApplicationByID(id)
}

func (s *ExpenseService) UpdateApplication(application *model.ExpenseApplication) error {
	return s.expenseRepo.UpdateApplication(application)
}

func (s *ExpenseService) DeleteApplication(id string) error {
	return s.expenseRepo.DeleteApplication(id)
}

func (s *ExpenseService) generateApplicationCode() string {
	now := time.Now()
	return fmt.Sprintf("BX%s%04d", now.Format("20060102"), now.Unix()%10000)
}
