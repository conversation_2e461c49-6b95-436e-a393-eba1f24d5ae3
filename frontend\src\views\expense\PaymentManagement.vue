<template>
  <div class="payment-management">
    <a-page-header title="付款管理" sub-title="付款申请的处理和执行" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="sourceTypeFilter"
            placeholder="来源类型"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="EXPENSE">报销付款</a-select-option>
            <a-select-option value="CONTRACT_PAY">合同付款</a-select-option>
          </a-select>
          <a-select
            v-model:value="statusFilter"
            placeholder="付款状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="PENDING">待付款</a-select-option>
            <a-select-option value="PAID">已付款</a-select-option>
            <a-select-option value="FAILED">付款失败</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="dateRange"
            placeholder="选择日期范围"
            @change="handleFilterChange"
          />
          <a-button type="primary" @click="batchPayment" :disabled="selectedRowKeys.length === 0">
            <CreditCardOutlined />
            批量付款
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'source_type'">
            <a-tag :color="getSourceTypeColor(record.source_type)">
              {{ getSourceTypeText(record.source_type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'amount'">
            <span class="amount">¥{{ formatAmount(record.amount) }}</span>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'payee_info'">
            <div class="payee-info">
              <div class="payee-name">{{ record.payee_name }}</div>
              <div class="payee-account">{{ record.payee_account }}</div>
            </div>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="processPayment(record)"
                :disabled="record.status !== 'PENDING'"
              >
                <CreditCardOutlined />
                付款
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="showEditModal(record)"
                :disabled="record.status !== 'PENDING'"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="export">
                      <a @click="exportPaymentVoucher(record)">导出付款凭证</a>
                    </a-menu-item>
                    <a-menu-item key="history">
                      <a @click="viewPaymentHistory(record)">查看付款历史</a>
                    </a-menu-item>
                    <a-menu-item key="cancel" danger v-if="record.status === 'PENDING'">
                      <a @click="cancelPayment(record.id)">取消付款</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 付款处理模态框 -->
    <a-modal
      v-model:open="paymentModalVisible"
      title="处理付款"
      width="600px"
      @ok="handlePaymentSubmit"
      @cancel="handlePaymentCancel"
    >
      <div v-if="currentPayment">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="付款单号">{{ currentPayment.payment_code }}</a-descriptions-item>
          <a-descriptions-item label="付款金额">¥{{ formatAmount(currentPayment.amount) }}</a-descriptions-item>
          <a-descriptions-item label="收款方">{{ currentPayment.payee_name }}</a-descriptions-item>
          <a-descriptions-item label="收款账号">{{ currentPayment.payee_account }}</a-descriptions-item>
          <a-descriptions-item label="开户行" :span="2">{{ currentPayment.payee_bank }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>付款信息</a-divider>
        
        <a-form
          ref="paymentFormRef"
          :model="paymentForm"
          :rules="paymentFormRules"
          layout="vertical"
        >
          <a-form-item label="付款方式" name="payment_method">
            <a-select v-model:value="paymentForm.payment_method" placeholder="选择付款方式">
              <a-select-option value="BANK_TRANSFER">银行转账</a-select-option>
              <a-select-option value="ONLINE_BANKING">网银支付</a-select-option>
              <a-select-option value="CHECK">支票</a-select-option>
              <a-select-option value="CASH">现金</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="付款账户" name="payment_account">
            <a-select v-model:value="paymentForm.payment_account" placeholder="选择付款账户">
              <a-select-option value="ACCOUNT_001">基本户 - **********</a-select-option>
              <a-select-option value="ACCOUNT_002">一般户 - **********</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="预计付款时间" name="scheduled_date">
            <a-date-picker
              v-model:value="paymentForm.scheduled_date"
              placeholder="选择预计付款时间"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="付款备注" name="remark">
            <a-textarea
              v-model:value="paymentForm.remark"
              placeholder="请输入付款备注"
              :rows="3"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 编辑付款信息模态框 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑付款信息"
      width="600px"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        layout="vertical"
      >
        <a-form-item label="收款方名称" name="payee_name">
          <a-input v-model:value="editForm.payee_name" placeholder="收款方名称" />
        </a-form-item>
        
        <a-form-item label="收款方账号" name="payee_account">
          <a-input v-model:value="editForm.payee_account" placeholder="收款方账号" />
        </a-form-item>
        
        <a-form-item label="开户行" name="payee_bank">
          <a-input v-model:value="editForm.payee_bank" placeholder="开户行" />
        </a-form-item>
        
        <a-form-item label="付款金额" name="amount">
          <a-input-number
            v-model:value="editForm.amount"
            :min="0"
            :precision="2"
            placeholder="付款金额"
            style="width: 100%"
            addon-after="元"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情查看模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="付款详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentPayment">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="付款单号">{{ currentPayment.payment_code }}</a-descriptions-item>
          <a-descriptions-item label="来源类型">
            <a-tag :color="getSourceTypeColor(currentPayment.source_type)">
              {{ getSourceTypeText(currentPayment.source_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="来源单据">{{ currentPayment.source_id }}</a-descriptions-item>
          <a-descriptions-item label="付款金额">¥{{ formatAmount(currentPayment.amount) }}</a-descriptions-item>
          <a-descriptions-item label="收款方名称">{{ currentPayment.payee_name }}</a-descriptions-item>
          <a-descriptions-item label="收款方账号">{{ currentPayment.payee_account }}</a-descriptions-item>
          <a-descriptions-item label="开户行" :span="2">{{ currentPayment.payee_bank }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentPayment.created_at }}</a-descriptions-item>
          <a-descriptions-item label="付款状态">
            <a-tag :color="getStatusColor(currentPayment.status)">
              {{ getStatusText(currentPayment.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="实际付款时间">
            {{ currentPayment.paid_at || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>付款历史</a-divider>
        
        <a-timeline>
          <a-timeline-item color="blue">
            <template #dot>
              <FileTextOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">创建付款单</div>
              <div class="timeline-desc">系统自动生成付款单</div>
              <div class="timeline-time">{{ currentPayment.created_at }}</div>
            </div>
          </a-timeline-item>
          
          <a-timeline-item 
            v-if="currentPayment.status === 'PAID'" 
            color="green"
          >
            <template #dot>
              <CheckCircleOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">付款成功</div>
              <div class="timeline-desc">付款已成功执行</div>
              <div class="timeline-time">{{ currentPayment.paid_at }}</div>
            </div>
          </a-timeline-item>
          
          <a-timeline-item 
            v-else-if="currentPayment.status === 'FAILED'" 
            color="red"
          >
            <template #dot>
              <CloseCircleOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">付款失败</div>
              <div class="timeline-desc">付款执行失败，请重新处理</div>
              <div class="timeline-time">{{ currentPayment.updated_at }}</div>
            </div>
          </a-timeline-item>
          
          <a-timeline-item 
            v-else 
            color="gray"
          >
            <template #dot>
              <ClockCircleOutlined />
            </template>
            <div class="timeline-content">
              <div class="timeline-title">等待付款</div>
              <div class="timeline-desc">付款单等待处理</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  CreditCardOutlined,
  EyeOutlined,
  EditOutlined,
  MoreOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import type { Payment } from '@/types'

const loading = ref(false)
const sourceTypeFilter = ref<string>()
const statusFilter = ref<string>()
const dateRange = ref()
const tableData = ref<Payment[]>([])
const selectedRowKeys = ref<string[]>([])
const paymentModalVisible = ref(false)
const editModalVisible = ref(false)
const detailModalVisible = ref(false)
const currentPayment = ref<Payment | null>(null)
const paymentFormRef = ref()
const editFormRef = ref()

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: Payment) => ({
    disabled: record.status !== 'PENDING'
  })
}))

const columns = [
  { title: '付款单号', dataIndex: 'payment_code', key: 'payment_code', width: 150 },
  { title: '来源类型', key: 'source_type', width: 100 },
  { title: '来源单据', dataIndex: 'source_id', key: 'source_id', width: 150 },
  { title: '收款方信息', key: 'payee_info', width: 200 },
  { title: '付款金额', key: 'amount', width: 120 },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
  { title: '付款状态', key: 'status', width: 100 },
  { title: '实际付款时间', dataIndex: 'paid_at', key: 'paid_at', width: 150 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const paymentForm = reactive({
  payment_method: '',
  payment_account: '',
  scheduled_date: null,
  remark: ''
})

const paymentFormRules = {
  payment_method: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
  payment_account: [{ required: true, message: '请选择付款账户', trigger: 'change' }],
  scheduled_date: [{ required: true, message: '请选择预计付款时间', trigger: 'change' }]
}

const editForm = reactive({
  id: '',
  payee_name: '',
  payee_account: '',
  payee_bank: '',
  amount: 0
})

const editFormRules = {
  payee_name: [{ required: true, message: '请输入收款方名称', trigger: 'blur' }],
  payee_account: [{ required: true, message: '请输入收款方账号', trigger: 'blur' }],
  payee_bank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
  amount: [{ required: true, message: '请输入付款金额', trigger: 'blur' }]
}

const getSourceTypeColor = (type: string) => {
  const colors = {
    'EXPENSE': 'blue',
    'CONTRACT_PAY': 'green'
  }
  return colors[type] || 'default'
}

const getSourceTypeText = (type: string) => {
  const texts = {
    'EXPENSE': '报销付款',
    'CONTRACT_PAY': '合同付款'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'processing',
    'PAID': 'success',
    'FAILED': 'error'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'PENDING': '待付款',
    'PAID': '已付款',
    'FAILED': '付款失败'
  }
  return texts[status] || status
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const loadPayments = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取付款单列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        payment_code: 'PAY20240723001',
        source_id: 'BX20240723001',
        source_type: 'EXPENSE',
        payee_name: '张三',
        payee_account: '****************',
        payee_bank: '中国工商银行上海分行',
        amount: 4850,
        status: 'PENDING',
        paid_at: null,
        created_at: '2024-07-23 16:30:00',
        updated_at: '2024-07-23 16:30:00'
      },
      {
        id: '2',
        payment_code: 'PAY20240722001',
        source_id: 'CON20240722001',
        source_type: 'CONTRACT_PAY',
        payee_name: '上海医疗器械有限公司',
        payee_account: '****************',
        payee_bank: '中国建设银行上海分行',
        amount: 150000,
        status: 'PAID',
        paid_at: '2024-07-23 10:15:00',
        created_at: '2024-07-22 14:20:00',
        updated_at: '2024-07-23 10:15:00'
      }
    ]
    pagination.total = 2
  } catch (error) {
    message.error('加载付款单数据失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadPayments()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadPayments()
}

const viewDetails = (record: Payment) => {
  currentPayment.value = record
  detailModalVisible.value = true
}

const processPayment = (record: Payment) => {
  currentPayment.value = record
  paymentModalVisible.value = true
  Object.assign(paymentForm, {
    payment_method: '',
    payment_account: '',
    scheduled_date: null,
    remark: ''
  })
}

const showEditModal = (record: Payment) => {
  currentPayment.value = record
  editModalVisible.value = true
  Object.assign(editForm, {
    id: record.id,
    payee_name: record.payee_name,
    payee_account: record.payee_account,
    payee_bank: record.payee_bank,
    amount: record.amount
  })
}

const handlePaymentSubmit = async () => {
  try {
    await paymentFormRef.value.validate()
    
    // TODO: 调用付款处理API
    message.success('付款处理成功')
    paymentModalVisible.value = false
    loadPayments()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handlePaymentCancel = () => {
  paymentModalVisible.value = false
  paymentFormRef.value?.resetFields()
}

const handleEditSubmit = async () => {
  try {
    await editFormRef.value.validate()
    
    // TODO: 调用更新API
    message.success('付款信息更新成功')
    editModalVisible.value = false
    loadPayments()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleEditCancel = () => {
  editModalVisible.value = false
  editFormRef.value?.resetFields()
}

const batchPayment = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要付款的记录')
    return
  }
  
  // TODO: 实现批量付款功能
  message.info('批量付款功能开发中')
}

const exportPaymentVoucher = (record: Payment) => {
  // TODO: 实现导出付款凭证功能
  message.info('导出付款凭证功能开发中')
}

const viewPaymentHistory = (record: Payment) => {
  // TODO: 实现查看付款历史功能
  viewDetails(record)
}

const cancelPayment = async (id: string) => {
  try {
    // TODO: 调用取消付款API
    message.success('付款已取消')
    loadPayments()
  } catch (error) {
    message.error('取消付款失败')
  }
}

onMounted(() => {
  loadPayments()
})
</script>

<style scoped>
.payment-management {
  padding: 24px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.payee-info {
  line-height: 1.4;
}

.payee-name {
  font-weight: 500;
}

.payee-account {
  font-size: 12px;
  color: #666;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #666;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
}
</style>
