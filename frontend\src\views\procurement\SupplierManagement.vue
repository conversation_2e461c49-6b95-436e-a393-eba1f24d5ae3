<template>
  <div class="supplier-management">
    <a-page-header title="供应商管理" sub-title="供应商信息库、资质与绩效评估" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="statusFilter"
            placeholder="供应商状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="ACTIVE">正常</a-select-option>
            <a-select-option value="INACTIVE">停用</a-select-option>
            <a-select-option value="PENDING">待审核</a-select-option>
            <a-select-option value="BLACKLIST">黑名单</a-select-option>
          </a-select>
          <a-select
            v-model:value="categoryFilter"
            placeholder="供应商类别"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="MEDICAL_EQUIPMENT">医疗设备</a-select-option>
            <a-select-option value="MEDICAL_SUPPLIES">医用耗材</a-select-option>
            <a-select-option value="PHARMACEUTICAL">药品</a-select-option>
            <a-select-option value="OFFICE_SUPPLIES">办公用品</a-select-option>
            <a-select-option value="SERVICE">服务类</a-select-option>
          </a-select>
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索供应商名称、编码"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新增供应商
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'category'">
            <a-tag :color="getCategoryColor(record.category)">
              {{ getCategoryText(record.category) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'performance_score'">
            <div class="performance-score">
              <a-progress
                :percent="record.performance_score"
                size="small"
                :status="getScoreStatus(record.performance_score)"
              />
              <span class="score-text">{{ record.performance_score }}分</span>
            </div>
          </template>
          
          <template v-if="column.key === 'qualification_status'">
            <a-tag :color="record.qualification_valid ? 'green' : 'red'">
              {{ record.qualification_valid ? '有效' : '过期' }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="text" size="small" @click="showEditModal(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="text" size="small" @click="viewQualifications(record)">
                <FileTextOutlined />
                资质
              </a-button>
              <a-button type="text" size="small" @click="viewPerformance(record)">
                <BarChartOutlined />
                绩效
              </a-button>
              <a-dropdown>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="activate" v-if="record.status === 'INACTIVE'">
                      <a @click="changeStatus(record.id, 'ACTIVE')">启用</a>
                    </a-menu-item>
                    <a-menu-item key="deactivate" v-if="record.status === 'ACTIVE'">
                      <a @click="changeStatus(record.id, 'INACTIVE')">停用</a>
                    </a-menu-item>
                    <a-menu-item key="blacklist" danger>
                      <a @click="changeStatus(record.id, 'BLACKLIST')">加入黑名单</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑供应商模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑供应商' : '新增供应商'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="供应商编码" name="supplier_code">
              <a-input v-model:value="formData.supplier_code" placeholder="供应商编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="供应商名称" name="supplier_name">
              <a-input v-model:value="formData.supplier_name" placeholder="供应商名称" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="供应商类别" name="category">
              <a-select v-model:value="formData.category" placeholder="选择供应商类别">
                <a-select-option value="MEDICAL_EQUIPMENT">医疗设备</a-select-option>
                <a-select-option value="MEDICAL_SUPPLIES">医用耗材</a-select-option>
                <a-select-option value="PHARMACEUTICAL">药品</a-select-option>
                <a-select-option value="OFFICE_SUPPLIES">办公用品</a-select-option>
                <a-select-option value="SERVICE">服务类</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="统一社会信用代码" name="credit_code">
              <a-input v-model:value="formData.credit_code" placeholder="统一社会信用代码" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="联系人" name="contact_person">
              <a-input v-model:value="formData.contact_person" placeholder="联系人姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系电话" name="contact_phone">
              <a-input v-model:value="formData.contact_phone" placeholder="联系电话" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="联系地址" name="address">
          <a-input v-model:value="formData.address" placeholder="详细地址" />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开户银行" name="bank_name">
              <a-input v-model:value="formData.bank_name" placeholder="开户银行" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="银行账号" name="bank_account">
              <a-input v-model:value="formData.bank_account" placeholder="银行账号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="备注">
          <a-textarea v-model:value="formData.remark" placeholder="备注信息" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 供应商详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="供应商详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentSupplier">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="供应商编码">{{ currentSupplier.supplier_code }}</a-descriptions-item>
          <a-descriptions-item label="供应商名称">{{ currentSupplier.supplier_name }}</a-descriptions-item>
          <a-descriptions-item label="供应商类别">
            <a-tag :color="getCategoryColor(currentSupplier.category)">
              {{ getCategoryText(currentSupplier.category) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentSupplier.status)">
              {{ getStatusText(currentSupplier.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="统一社会信用代码">{{ currentSupplier.credit_code }}</a-descriptions-item>
          <a-descriptions-item label="联系人">{{ currentSupplier.contact_person }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ currentSupplier.contact_phone }}</a-descriptions-item>
          <a-descriptions-item label="联系地址" :span="2">{{ currentSupplier.address }}</a-descriptions-item>
          <a-descriptions-item label="开户银行">{{ currentSupplier.bank_name }}</a-descriptions-item>
          <a-descriptions-item label="银行账号">{{ currentSupplier.bank_account }}</a-descriptions-item>
          <a-descriptions-item label="绩效评分">
            <div class="performance-score">
              <a-progress
                :percent="currentSupplier.performance_score"
                size="small"
                :status="getScoreStatus(currentSupplier.performance_score)"
              />
              <span class="score-text">{{ currentSupplier.performance_score }}分</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="资质状态">
            <a-tag :color="currentSupplier.qualification_valid ? 'green' : 'red'">
              {{ currentSupplier.qualification_valid ? '有效' : '过期' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentSupplier.created_at }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ currentSupplier.updated_at }}</a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">{{ currentSupplier.remark || '无' }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 资质管理模态框 -->
    <a-modal
      v-model:open="qualificationModalVisible"
      title="资质管理"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentSupplier">
        <div class="qualification-header">
          <h4>{{ currentSupplier.supplier_name }} - 资质证书</h4>
          <a-button type="primary" @click="showAddQualificationModal">
            <PlusOutlined />
            添加资质
          </a-button>
        </div>
        
        <a-table
          :columns="qualificationColumns"
          :data-source="qualificationData"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.is_valid ? 'green' : 'red'">
                {{ record.is_valid ? '有效' : '过期' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'expire_date'">
              <span :class="{ 'expire-warning': isExpiringSoon(record.expire_date) }">
                {{ record.expire_date }}
              </span>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="text" size="small" @click="viewQualificationFile(record)">
                  <EyeOutlined />
                  查看
                </a-button>
                <a-button type="text" size="small" @click="downloadQualification(record)">
                  <DownloadOutlined />
                  下载
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 绩效评估模态框 -->
    <a-modal
      v-model:open="performanceModalVisible"
      title="绩效评估"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentSupplier">
        <div class="performance-header">
          <h4>{{ currentSupplier.supplier_name }} - 绩效评估</h4>
          <a-button type="primary" @click="showAddPerformanceModal">
            <PlusOutlined />
            新增评估
          </a-button>
        </div>
        
        <a-row :gutter="16" class="performance-stats">
          <a-col :span="6">
            <a-statistic
              title="综合评分"
              :value="currentSupplier.performance_score"
              suffix="分"
              :value-style="{ color: getScoreColor(currentSupplier.performance_score) }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="质量评分"
              :value="85"
              suffix="分"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="交付评分"
              :value="92"
              suffix="分"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="服务评分"
              :value="88"
              suffix="分"
              :value-style="{ color: '#722ed1' }"
            />
          </a-col>
        </a-row>
        
        <a-divider>评估历史</a-divider>
        
        <a-table
          :columns="performanceColumns"
          :data-source="performanceData"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'total_score'">
              <span :style="{ color: getScoreColor(record.total_score) }">
                {{ record.total_score }}分
              </span>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  FileTextOutlined,
  BarChartOutlined,
  MoreOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import type { Supplier } from '@/types'

const loading = ref(false)
const statusFilter = ref<string>()
const categoryFilter = ref<string>()
const searchKeyword = ref('')
const tableData = ref<Supplier[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const qualificationModalVisible = ref(false)
const performanceModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentSupplier = ref<Supplier | null>(null)
const qualificationData = ref([])
const performanceData = ref([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '供应商编码', dataIndex: 'supplier_code', key: 'supplier_code', width: 120 },
  { title: '供应商名称', dataIndex: 'supplier_name', key: 'supplier_name', ellipsis: true },
  { title: '类别', key: 'category', width: 100 },
  { title: '联系人', dataIndex: 'contact_person', key: 'contact_person', width: 100 },
  { title: '联系电话', dataIndex: 'contact_phone', key: 'contact_phone', width: 120 },
  { title: '绩效评分', key: 'performance_score', width: 120 },
  { title: '资质状态', key: 'qualification_status', width: 100 },
  { title: '状态', key: 'status', width: 100 },
  { title: '操作', key: 'action', width: 250, fixed: 'right' }
]

const qualificationColumns = [
  { title: '证书名称', dataIndex: 'cert_name', key: 'cert_name' },
  { title: '证书编号', dataIndex: 'cert_number', key: 'cert_number' },
  { title: '颁发机构', dataIndex: 'issuer', key: 'issuer' },
  { title: '有效期至', key: 'expire_date' },
  { title: '状态', key: 'status' },
  { title: '操作', key: 'action', width: 150 }
]

const performanceColumns = [
  { title: '评估期间', dataIndex: 'evaluation_period', key: 'evaluation_period' },
  { title: '质量评分', dataIndex: 'quality_score', key: 'quality_score' },
  { title: '交付评分', dataIndex: 'delivery_score', key: 'delivery_score' },
  { title: '服务评分', dataIndex: 'service_score', key: 'service_score' },
  { title: '综合评分', key: 'total_score' },
  { title: '评估人', dataIndex: 'evaluator', key: 'evaluator' },
  { title: '评估时间', dataIndex: 'created_at', key: 'created_at' }
]

const formData = reactive({
  id: '',
  supplier_code: '',
  supplier_name: '',
  category: '',
  credit_code: '',
  contact_person: '',
  contact_phone: '',
  address: '',
  bank_name: '',
  bank_account: '',
  remark: ''
})

const formRules = {
  supplier_code: [{ required: true, message: '请输入供应商编码', trigger: 'blur' }],
  supplier_name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择供应商类别', trigger: 'change' }],
  credit_code: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
  contact_person: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contact_phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
}

const getStatusColor = (status: string) => {
  const colors = {
    'ACTIVE': 'green',
    'INACTIVE': 'default',
    'PENDING': 'processing',
    'BLACKLIST': 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'ACTIVE': '正常',
    'INACTIVE': '停用',
    'PENDING': '待审核',
    'BLACKLIST': '黑名单'
  }
  return texts[status] || status
}

const getCategoryColor = (category: string) => {
  const colors = {
    'MEDICAL_EQUIPMENT': 'blue',
    'MEDICAL_SUPPLIES': 'green',
    'PHARMACEUTICAL': 'purple',
    'OFFICE_SUPPLIES': 'orange',
    'SERVICE': 'cyan'
  }
  return colors[category] || 'default'
}

const getCategoryText = (category: string) => {
  const texts = {
    'MEDICAL_EQUIPMENT': '医疗设备',
    'MEDICAL_SUPPLIES': '医用耗材',
    'PHARMACEUTICAL': '药品',
    'OFFICE_SUPPLIES': '办公用品',
    'SERVICE': '服务类'
  }
  return texts[category] || category
}

const getScoreStatus = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 70) return 'normal'
  return 'exception'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 70) return '#1890ff'
  return '#f5222d'
}

const isExpiringSoon = (expireDate: string) => {
  const expire = new Date(expireDate)
  const now = new Date()
  const diffDays = Math.ceil((expire.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays <= 30 && diffDays > 0
}

const loadSuppliers = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取供应商列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        supplier_code: 'SUP001',
        supplier_name: '上海医疗器械有限公司',
        category: 'MEDICAL_EQUIPMENT',
        credit_code: '91310000********9X',
        contact_person: '张经理',
        contact_phone: '021-********',
        address: '上海市浦东新区张江高科技园区',
        bank_name: '中国工商银行上海分行',
        bank_account: '********90********9',
        status: 'ACTIVE',
        performance_score: 92,
        qualification_valid: true,
        created_at: '2024-01-15 10:00:00',
        updated_at: '2024-07-20 14:30:00',
        remark: '优质供应商'
      },
      {
        id: '2',
        supplier_code: 'SUP002',
        supplier_name: '北京医用耗材供应商',
        category: 'MEDICAL_SUPPLIES',
        credit_code: '911100009********Y',
        contact_person: '李总',
        contact_phone: '010-********',
        address: '北京市朝阳区CBD商务区',
        bank_name: '中国建设银行北京分行',
        bank_account: '9********09********',
        status: 'ACTIVE',
        performance_score: 85,
        qualification_valid: false,
        created_at: '2024-02-20 09:15:00',
        updated_at: '2024-07-18 16:45:00',
        remark: '资质即将到期，需要更新'
      }
    ]
    pagination.total = 2
  } catch (error) {
    message.error('加载供应商数据失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadSuppliers()
}

const handleSearch = () => {
  pagination.current = 1
  loadSuppliers()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadSuppliers()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    supplier_code: '',
    supplier_name: '',
    category: '',
    credit_code: '',
    contact_person: '',
    contact_phone: '',
    address: '',
    bank_name: '',
    bank_account: '',
    remark: ''
  })
}

const showEditModal = (supplier: Supplier) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, supplier)
}

const viewDetails = (supplier: Supplier) => {
  currentSupplier.value = supplier
  detailModalVisible.value = true
}

const viewQualifications = (supplier: Supplier) => {
  currentSupplier.value = supplier
  // TODO: 加载资质数据
  qualificationData.value = [
    {
      id: '1',
      cert_name: '医疗器械经营许可证',
      cert_number: 'YXQ20240001',
      issuer: '上海市药品监督管理局',
      expire_date: '2025-12-31',
      is_valid: true
    },
    {
      id: '2',
      cert_name: 'ISO9001质量管理体系认证',
      cert_number: 'ISO20240002',
      issuer: '中国质量认证中心',
      expire_date: '2024-08-15',
      is_valid: false
    }
  ]
  qualificationModalVisible.value = true
}

const viewPerformance = (supplier: Supplier) => {
  currentSupplier.value = supplier
  // TODO: 加载绩效数据
  performanceData.value = [
    {
      id: '1',
      evaluation_period: '2024年第二季度',
      quality_score: 90,
      delivery_score: 95,
      service_score: 88,
      total_score: 92,
      evaluator: '采购部',
      created_at: '2024-07-01 10:00:00'
    },
    {
      id: '2',
      evaluation_period: '2024年第一季度',
      quality_score: 85,
      delivery_score: 90,
      service_score: 85,
      total_score: 87,
      evaluator: '采购部',
      created_at: '2024-04-01 10:00:00'
    }
  ]
  performanceModalVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('供应商信息更新成功')
    } else {
      // TODO: 调用创建API
      message.success('供应商添加成功')
    }
    
    modalVisible.value = false
    loadSuppliers()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const changeStatus = async (id: string, status: string) => {
  try {
    // TODO: 调用状态变更API
    const statusText = getStatusText(status)
    message.success(`供应商状态已更改为${statusText}`)
    loadSuppliers()
  } catch (error) {
    message.error('状态更改失败')
  }
}

const showAddQualificationModal = () => {
  // TODO: 实现添加资质功能
  message.info('添加资质功能开发中')
}

const showAddPerformanceModal = () => {
  // TODO: 实现新增评估功能
  message.info('新增评估功能开发中')
}

const viewQualificationFile = (qualification: any) => {
  // TODO: 实现查看资质文件功能
  message.info('查看资质文件功能开发中')
}

const downloadQualification = (qualification: any) => {
  // TODO: 实现下载资质文件功能
  message.info('下载资质文件功能开发中')
}

onMounted(() => {
  loadSuppliers()
})
</script>

<style scoped>
.supplier-management {
  padding: 24px;
}

.performance-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-text {
  font-size: 12px;
  font-weight: 500;
}

.qualification-header,
.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.performance-stats {
  margin-bottom: 24px;
}

.expire-warning {
  color: #fa8c16;
  font-weight: 500;
}
</style>
