package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type DepartmentRepository struct {
	db *gorm.DB
}

func NewDepartmentRepository(db *gorm.DB) *DepartmentRepository {
	return &DepartmentRepository{db: db}
}

func (r *DepartmentRepository) Create(dept *model.Department) error {
	return r.db.Create(dept).Error
}

func (r *DepartmentRepository) GetByID(id string) (*model.Department, error) {
	var dept model.Department
	err := r.db.Preload("Parent").Preload("Children").First(&dept, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

func (r *DepartmentRepository) Update(dept *model.Department) error {
	return r.db.Save(dept).Error
}

func (r *DepartmentRepository) Delete(id string) error {
	return r.db.Delete(&model.Department{}, "id = ?", id).Error
}

func (r *DepartmentRepository) List() ([]model.Department, error) {
	var departments []model.Department
	err := r.db.Preload("Parent").Preload("Children").
		Order("level ASC, sort_order ASC").Find(&departments).Error
	return departments, err
}

func (r *DepartmentRepository) GetTree() ([]model.Department, error) {
	var departments []model.Department
	err := r.db.Where("parent_id IS NULL").
		Preload("Children").
		Order("sort_order ASC").Find(&departments).Error
	return departments, err
}
