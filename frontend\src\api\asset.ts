import api from './index'
import type { ApiResponse, PaginationParams, PaginationResponse } from '@/types'

// 资产相关类型定义
export interface Asset {
  id: string
  asset_code: string
  asset_name: string
  category_id: string
  specification?: string
  brand?: string
  model?: string
  serial_number?: string
  purchase_date?: string
  purchase_price: number
  supplier_id?: string
  contract_id?: string
  receipt_id?: string
  department_id: string
  location_id?: string
  responsible_id?: string
  status: 'NORMAL' | 'USING' | 'MAINTENANCE' | 'SCRAPPED'
  depreciation_rate: number
  useful_life: number
  warranty_expiry?: string
  qr_code?: string
  barcode?: string
  remark?: string
  created_at: string
  updated_at: string
  category?: {
    id: string
    category_name: string
    category_code: string
  }
  supplier?: {
    id: string
    name: string
  }
  department?: {
    id: string
    name: string
  }
  location?: {
    id: string
    location_name: string
  }
  responsible?: {
    id: string
    real_name: string
  }
}

export interface AssetCategory {
  id: string
  category_code: string
  category_name: string
  parent_id?: string
  level: number
  sort_order: number
  is_active: boolean
  description?: string
  children?: AssetCategory[]
}

export interface AssetLocation {
  id: string
  location_code: string
  location_name: string
  parent_id?: string
  building?: string
  floor?: string
  room?: string
  description?: string
  children?: AssetLocation[]
}

export interface AssetMovement {
  id: string
  asset_id: string
  movement_type: 'BORROW' | 'RETURN' | 'TRANSFER' | 'DISPOSE'
  from_user_id?: string
  to_user_id?: string
  from_dept_id?: string
  to_dept_id?: string
  from_location_id?: string
  to_location_id?: string
  movement_date: string
  reason: string
  status: 'PENDING' | 'APPROVED' | 'COMPLETED'
  asset?: Asset
  from_user?: {
    id: string
    real_name: string
  }
  to_user?: {
    id: string
    real_name: string
  }
}

export interface AssetMaintenance {
  id: string
  asset_id: string
  maintenance_type: 'REPAIR' | 'MAINTAIN' | 'UPGRADE'
  maintenance_date: string
  description: string
  cost: number
  supplier_id?: string
  responsible_id: string
  next_date?: string
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED'
  asset?: Asset
  responsible?: {
    id: string
    real_name: string
  }
}

export interface CreateAssetRequest {
  asset_name: string
  category_id: string
  specification?: string
  brand?: string
  model?: string
  serial_number?: string
  purchase_date?: string
  purchase_price: number
  supplier_id?: string
  contract_id?: string
  receipt_id?: string
  department_id: string
  location_id?: string
  responsible_id?: string
  depreciation_rate?: number
  useful_life?: number
  warranty_expiry?: string
  remark?: string
}

export interface AssetMovementRequest {
  asset_id: string
  movement_type: 'BORROW' | 'RETURN' | 'TRANSFER' | 'DISPOSE'
  to_user_id?: string
  to_dept_id?: string
  to_location_id?: string
  reason: string
}

export interface AssetMaintenanceRequest {
  asset_id: string
  maintenance_type: 'REPAIR' | 'MAINTAIN' | 'UPGRADE'
  description: string
  cost?: number
  supplier_id?: string
  responsible_id: string
  next_date?: string
}

export interface AssetStatistics {
  total_count: number
  total_value: number
  status_stats: {
    status: string
    count: number
  }[]
  category_stats: {
    category_name: string
    count: number
  }[]
}

// 获取资产列表
export const getAssetList = (params: PaginationParams & {
  status?: string
  category_id?: string
  department_id?: string
  responsible_id?: string
}) => {
  return api.get<ApiResponse<PaginationResponse<Asset>>>('/assets', { params })
}

// 根据ID获取资产详情
export const getAssetById = (id: string) => {
  return api.get<ApiResponse<Asset>>(`/assets/${id}`)
}

// 创建资产
export const createAsset = (data: CreateAssetRequest) => {
  return api.post<ApiResponse<Asset>>('/assets', data)
}

// 创建资产变动
export const createAssetMovement = (data: AssetMovementRequest) => {
  return api.post<ApiResponse<null>>('/assets/movements', data)
}

// 创建资产维保记录
export const createAssetMaintenance = (data: AssetMaintenanceRequest) => {
  return api.post<ApiResponse<null>>('/assets/maintenances', data)
}

// 获取资产统计数据
export const getAssetStatistics = () => {
  return api.get<ApiResponse<AssetStatistics>>('/assets/statistics')
}

// 获取即将到期的维保
export const getUpcomingMaintenances = (days?: number) => {
  return api.get<ApiResponse<AssetMaintenance[]>>('/assets/maintenances/upcoming', {
    params: { days }
  })
}

// 获取即将到期的保修
export const getExpiringWarranties = (days?: number) => {
  return api.get<ApiResponse<Asset[]>>('/assets/warranties/expiring', {
    params: { days }
  })
}

// 生成资产二维码
export const generateAssetQRCode = (id: string) => {
  return api.get(`/assets/${id}/qrcode`)
}

// 打印资产标签
export const printAssetLabel = (id: string) => {
  return api.get(`/assets/${id}/label`, { responseType: 'blob' })
}

// 导出资产列表
export const exportAssets = () => {
  return api.get('/assets/export', { responseType: 'blob' })
}

// 导入资产数据
export const importAssets = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return api.post<ApiResponse<null>>('/assets/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
