import api from './index'
import type { Department, ApiResponse } from '@/types'

// 获取部门树形结构
export const getDepartmentTree = () => {
  return api.get<ApiResponse<Department[]>>('/departments/tree')
}

// 获取部门列表
export const getDepartmentList = () => {
  return api.get<ApiResponse<Department[]>>('/departments')
}

// 根据ID获取部门详情
export const getDepartmentById = (id: string) => {
  return api.get<ApiResponse<Department>>(`/departments/${id}`)
}

// 创建部门
export const createDepartment = (data: Partial<Department>) => {
  return api.post<ApiResponse<Department>>('/departments', data)
}

// 更新部门
export const updateDepartment = (id: string, data: Partial<Department>) => {
  return api.put<ApiResponse<Department>>(`/departments/${id}`, data)
}

// 删除部门
export const deleteDepartment = (id: string) => {
  return api.delete<ApiResponse<null>>(`/departments/${id}`)
}
