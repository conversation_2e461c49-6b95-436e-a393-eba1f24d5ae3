package model

import (
	"time"
)

// RiskEvent 风险事件表
type RiskEvent struct {
	ID              uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Title           string    `json:"title" gorm:"size:200;not null"`
	Description     string    `json:"description" gorm:"type:text;not null"`
	Category        string    `json:"category" gorm:"size:100;not null"`
	Level           string    `json:"level" gorm:"size:20;not null;comment:low,medium,high,critical"`
	Probability     int       `json:"probability" gorm:"not null;comment:1-5分"`
	Impact          int       `json:"impact" gorm:"not null;comment:1-5分"`
	RiskScore       float64   `json:"risk_score" gorm:"type:decimal(3,1)"`
	Status          string    `json:"status" gorm:"size:20;default:'open';comment:open,in_progress,closed"`
	ReporterID      uint      `json:"reporter_id" gorm:"not null"`
	ResponsibleDept string    `json:"responsible_dept" gorm:"size:100"`
	AssigneeID      *uint     `json:"assignee_id"`
	OccurredAt      time.Time `json:"occurred_at"`
	ClosedAt        *time.Time `json:"closed_at"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`

	// 关联
	Reporter    User             `json:"reporter" gorm:"foreignKey:ReporterID"`
	Assignee    *User            `json:"assignee" gorm:"foreignKey:AssigneeID"`
	Assessments []RiskAssessment `json:"assessments" gorm:"foreignKey:RiskEventID"`
}

// RiskAssessment 风险评估表
type RiskAssessment struct {
	ID                uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	RiskEventID       uint      `json:"risk_event_id" gorm:"not null"`
	AssessorID        uint      `json:"assessor_id" gorm:"not null"`
	ProbabilityScore  int       `json:"probability_score" gorm:"not null;comment:1-5分"`
	ImpactScore       int       `json:"impact_score" gorm:"not null;comment:1-5分"`
	RiskScore         float64   `json:"risk_score" gorm:"type:decimal(3,1)"`
	MitigationPlan    string    `json:"mitigation_plan" gorm:"type:text"`
	PreventiveMeasures string   `json:"preventive_measures" gorm:"type:text"`
	MonitoringPlan    string    `json:"monitoring_plan" gorm:"type:text"`
	ExpectedCompletion *time.Time `json:"expected_completion"`
	Status            string    `json:"status" gorm:"size:20;default:'draft';comment:draft,approved,rejected"`
	Remarks           string    `json:"remarks" gorm:"type:text"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`

	// 关联
	RiskEvent RiskEvent `json:"risk_event" gorm:"foreignKey:RiskEventID"`
	Assessor  User      `json:"assessor" gorm:"foreignKey:AssessorID"`
}

// ComplianceCheck 合规检查表
type ComplianceCheck struct {
	ID              uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Title           string    `json:"title" gorm:"size:200;not null"`
	Type            string    `json:"type" gorm:"size:50;not null;comment:internal,external,regulatory"`
	Category        string    `json:"category" gorm:"size:100;not null"`
	Description     string    `json:"description" gorm:"type:text"`
	CheckerID       uint      `json:"checker_id" gorm:"not null"`
	DepartmentID    uint      `json:"department_id" gorm:"not null"`
	PlannedStartDate time.Time `json:"planned_start_date"`
	PlannedEndDate  time.Time `json:"planned_end_date"`
	ActualStartDate *time.Time `json:"actual_start_date"`
	ActualEndDate   *time.Time `json:"actual_end_date"`
	Status          string    `json:"status" gorm:"size:20;default:'planned';comment:planned,in_progress,completed,cancelled"`
	OverallScore    float64   `json:"overall_score" gorm:"type:decimal(5,2)"`
	PassRate        float64   `json:"pass_rate" gorm:"type:decimal(5,2)"`
	Conclusion      string    `json:"conclusion" gorm:"type:text"`
	Recommendations string    `json:"recommendations" gorm:"type:text"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`

	// 关联
	Checker    User              `json:"checker" gorm:"foreignKey:CheckerID"`
	Department Department        `json:"department" gorm:"foreignKey:DepartmentID"`
	Items      []ComplianceItem  `json:"items" gorm:"foreignKey:CheckID"`
}

// ComplianceItem 合规检查项表
type ComplianceItem struct {
	ID          uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	CheckID     uint      `json:"check_id" gorm:"not null"`
	Title       string    `json:"title" gorm:"size:200;not null"`
	Description string    `json:"description" gorm:"type:text"`
	Standard    string    `json:"standard" gorm:"type:text"`
	Weight      float64   `json:"weight" gorm:"type:decimal(5,2);default:1.00"`
	Result      string    `json:"result" gorm:"size:20;comment:pass,fail,na"`
	Score       float64   `json:"score" gorm:"type:decimal(5,2)"`
	Evidence    string    `json:"evidence" gorm:"type:text"`
	Issues      string    `json:"issues" gorm:"type:text"`
	Suggestions string    `json:"suggestions" gorm:"type:text"`
	CheckerID   uint      `json:"checker_id" gorm:"not null"`
	CheckedAt   *time.Time `json:"checked_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 关联
	Check   ComplianceCheck `json:"check" gorm:"foreignKey:CheckID"`
	Checker User            `json:"checker" gorm:"foreignKey:CheckerID"`
}
