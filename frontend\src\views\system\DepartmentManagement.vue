<template>
  <div class="department-management">
    <a-page-header title="组织架构管理" sub-title="科室/部门的增、删、改、查、树形展示" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新增部门
          </a-button>
          <a-button @click="expandAll">
            <ExpandAltOutlined />
            展开全部
          </a-button>
          <a-button @click="collapseAll">
            <ShrinkOutlined />
            收起全部
          </a-button>
        </a-space>
      </template>

      <a-tree
        v-model:expandedKeys="expandedKeys"
        :tree-data="treeData"
        :field-names="{ children: 'children', title: 'name', key: 'id' }"
        show-line
        :show-icon="false"
      >
        <template #title="{ name, code, status, id }">
          <div class="tree-node">
            <span class="node-title">{{ name }}</span>
            <span class="node-code">[{{ code }}]</span>
            <a-tag :color="status === 1 ? 'green' : 'red'" size="small">
              {{ status === 1 ? '启用' : '禁用' }}
            </a-tag>
            <div class="node-actions">
              <a-button type="text" size="small" @click="showAddModal(id)">
                <PlusOutlined />
              </a-button>
              <a-button type="text" size="small" @click="showEditModal(id)">
                <EditOutlined />
              </a-button>
              <a-popconfirm
                title="确定要删除这个部门吗？"
                @confirm="deleteDepartment(id)"
              >
                <a-button type="text" size="small" danger>
                  <DeleteOutlined />
                </a-button>
              </a-popconfirm>
            </div>
          </div>
        </template>
      </a-tree>
    </a-card>

    <!-- 新增/编辑部门模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑部门' : '新增部门'"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="部门名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入部门名称" />
        </a-form-item>
        
        <a-form-item label="部门编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入部门编码" />
        </a-form-item>
        
        <a-form-item label="上级部门" name="parent_id">
          <a-tree-select
            v-model:value="formData.parent_id"
            :tree-data="treeSelectData"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择上级部门"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>
        
        <a-form-item label="排序" name="sort_order">
          <a-input-number
            v-model:value="formData.sort_order"
            :min="0"
            placeholder="排序号"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入部门描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExpandAltOutlined,
  ShrinkOutlined
} from '@ant-design/icons-vue'
import type { Department } from '@/types'

const expandedKeys = ref<string[]>([])
const treeData = ref<Department[]>([])
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const formData = reactive({
  id: '',
  name: '',
  code: '',
  parent_id: null as string | null,
  sort_order: 0,
  description: '',
  status: 1
})

const formRules = {
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入部门编码', trigger: 'blur' }]
}

// 用于树选择器的数据（排除当前编辑的部门）
const treeSelectData = computed(() => {
  const filterNode = (nodes: Department[]): Department[] => {
    return nodes.filter(node => node.id !== formData.id).map(node => ({
      ...node,
      children: node.children ? filterNode(node.children) : undefined
    }))
  }
  return filterNode(treeData.value)
})

const loadDepartments = async () => {
  try {
    // TODO: 调用API获取部门树形数据
    // const response = await getDepartmentTree()
    // treeData.value = response.data
    
    // 临时数据
    treeData.value = [
      {
        id: '1',
        name: '医院总部',
        code: 'HQ',
        parent_id: null,
        level: 1,
        sort_order: 1,
        description: '医院总部',
        status: 1,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        children: [
          {
            id: '2',
            name: '内科',
            code: 'NK',
            parent_id: '1',
            level: 2,
            sort_order: 1,
            description: '内科部门',
            status: 1,
            created_at: '2024-01-01',
            updated_at: '2024-01-01',
            children: [
              {
                id: '3',
                name: '心内科',
                code: 'XNK',
                parent_id: '2',
                level: 3,
                sort_order: 1,
                description: '心内科',
                status: 1,
                created_at: '2024-01-01',
                updated_at: '2024-01-01'
              }
            ]
          },
          {
            id: '4',
            name: '外科',
            code: 'WK',
            parent_id: '1',
            level: 2,
            sort_order: 2,
            description: '外科部门',
            status: 1,
            created_at: '2024-01-01',
            updated_at: '2024-01-01'
          }
        ]
      }
    ]
    
    // 默认展开第一级
    expandedKeys.value = ['1']
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

const showAddModal = (parentId?: string) => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    name: '',
    code: '',
    parent_id: parentId || null,
    sort_order: 0,
    description: '',
    status: 1
  })
}

const showEditModal = async (id: string) => {
  isEdit.value = true
  modalVisible.value = true
  
  try {
    // TODO: 调用API获取部门详情
    // const response = await getDepartmentById(id)
    // Object.assign(formData, response.data)
    
    // 临时数据
    const findDepartment = (nodes: Department[], targetId: string): Department | null => {
      for (const node of nodes) {
        if (node.id === targetId) return node
        if (node.children) {
          const found = findDepartment(node.children, targetId)
          if (found) return found
        }
      }
      return null
    }
    
    const dept = findDepartment(treeData.value, id)
    if (dept) {
      Object.assign(formData, dept)
    }
  } catch (error) {
    message.error('获取部门信息失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      // TODO: 调用更新API
      // await updateDepartment(formData.id, formData)
      message.success('部门更新成功')
    } else {
      // TODO: 调用创建API
      // await createDepartment(formData)
      message.success('部门创建成功')
    }
    
    modalVisible.value = false
    loadDepartments()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const deleteDepartment = async (id: string) => {
  try {
    // TODO: 调用删除API
    // await deleteDepartment(id)
    message.success('部门删除成功')
    loadDepartments()
  } catch (error) {
    message.error('删除失败')
  }
}

const expandAll = () => {
  const getAllKeys = (nodes: Department[]): string[] => {
    let keys: string[] = []
    nodes.forEach(node => {
      keys.push(node.id)
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  expandedKeys.value = getAllKeys(treeData.value)
}

const collapseAll = () => {
  expandedKeys.value = []
}

onMounted(() => {
  loadDepartments()
})
</script>

<style scoped>
.department-management {
  padding: 24px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-title {
  font-weight: 500;
}

.node-code {
  color: #666;
  font-size: 12px;
}

.node-actions {
  margin-left: auto;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}
</style>
