package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PreApplication 事前申请单 (tbl_pre_applications)
type PreApplication struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ApplicationCode string    `json:"application_code" gorm:"size:50;uniqueIndex;not null;comment:事前申请单号"`
	ApplicantID     uuid.UUID `json:"applicant_id" gorm:"type:uuid;not null;comment:申请人"`
	Type            string    `json:"type" gorm:"size:50;not null;comment:事前申请类型 TRAVEL/MEETING/TRAINING"`
	Title           string    `json:"title" gorm:"size:200;not null;comment:申请标题"`
	EstimatedAmount float64   `json:"estimated_amount" gorm:"type:decimal(18,2);not null;comment:预估总金额"`
	Status          string    `json:"status" gorm:"size:30;not null;default:'PENDING';comment:状态 PENDING/APPROVED/CLOSED"`
	Details         string    `json:"details" gorm:"type:jsonb;comment:详细信息JSON"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy       *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy       *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Applicant User `json:"applicant" gorm:"foreignKey:ApplicantID"`
}

// ExpenseApplication 报销申请单主表 (tbl_expense_applications)
type ExpenseApplication struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ApplicationCode string    `json:"application_code" gorm:"size:50;uniqueIndex;not null;comment:报销单号"`
	Title           string    `json:"title" gorm:"size:200;not null;comment:报销标题"`
	ApplicantID     uuid.UUID `json:"applicant_id" gorm:"type:uuid;not null;comment:申请人ID"`
	DepartmentID    uuid.UUID `json:"department_id" gorm:"type:uuid;not null;comment:费用归属部门"`
	TotalAmount     float64   `json:"total_amount" gorm:"type:decimal(18,2);not null;comment:报销总金额"`
	Status          string    `json:"status" gorm:"size:30;not null;default:'DRAFT';comment:状态 DRAFT/PENDING/APPROVED/REJECTED/PAID"`
	PreApplicationID *uuid.UUID `json:"pre_application_id" gorm:"type:uuid;comment:关联的事前申请ID"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy       *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy       *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Applicant       User                `json:"applicant" gorm:"foreignKey:ApplicantID"`
	Department      Department          `json:"department" gorm:"foreignKey:DepartmentID"`
	PreApplication  *PreApplication     `json:"pre_application" gorm:"foreignKey:PreApplicationID"`
	ExpenseDetails  []ExpenseDetail     `json:"expense_details" gorm:"foreignKey:ApplicationID"`
}

// ExpenseDetail 报销明细表 (tbl_expense_details)
type ExpenseDetail struct {
	ID            uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	ApplicationID uuid.UUID  `json:"application_id" gorm:"type:uuid;not null;comment:报销申请单ID"`
	BudgetItemID  uuid.UUID  `json:"budget_item_id" gorm:"type:uuid;not null;comment:关联的预算明细项ID"`
	Description   string     `json:"description" gorm:"size:500;not null;comment:费用描述"`
	Amount        float64    `json:"amount" gorm:"type:decimal(18,2);not null;comment:金额"`
	ExpenseDate   time.Time  `json:"expense_date" gorm:"not null;comment:费用发生日期"`
	InvoiceInfo   string     `json:"invoice_info" gorm:"type:jsonb;comment:发票信息JSON"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy     *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy     *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Application ExpenseApplication `json:"application" gorm:"foreignKey:ApplicationID"`
	BudgetItem  BudgetItem         `json:"budget_item" gorm:"foreignKey:BudgetItemID"`
}

// Payment 付款单 (tbl_payments)
type Payment struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	PaymentCode  string    `json:"payment_code" gorm:"size:50;uniqueIndex;not null;comment:付款单号"`
	SourceID     uuid.UUID `json:"source_id" gorm:"type:uuid;not null;comment:来源单据ID"`
	SourceType   string    `json:"source_type" gorm:"size:50;not null;comment:来源类型 EXPENSE/CONTRACT_PAY"`
	PayeeName    string    `json:"payee_name" gorm:"size:255;not null;comment:收款方名称"`
	PayeeAccount string    `json:"payee_account" gorm:"size:100;not null;comment:收款方账号"`
	PayeeBank    string    `json:"payee_bank" gorm:"size:255;not null;comment:收款方开户行"`
	Amount       float64   `json:"amount" gorm:"type:decimal(18,2);not null;comment:支付金额"`
	Status       string    `json:"status" gorm:"size:30;not null;default:'PENDING';comment:状态 PENDING/PAID/FAILED"`
	PaidAt       *time.Time `json:"paid_at" gorm:"comment:实际支付时间"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy    *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy    *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`
}
