package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type RiskEventRepository struct {
	db *gorm.DB
}

func NewRiskEventRepository(db *gorm.DB) *RiskEventRepository {
	return &RiskEventRepository{db: db}
}

func (r *RiskEventRepository) Create(event *model.RiskEvent) error {
	return r.db.Create(event).Error
}

func (r *RiskEventRepository) GetByID(id uint) (*model.RiskEvent, error) {
	var event model.RiskEvent
	err := r.db.Preload("Reporter").Preload("Assignee").Preload("Assessments").First(&event, id).Error
	if err != nil {
		return nil, err
	}
	return &event, nil
}

func (r *RiskEventRepository) Update(event *model.RiskEvent) error {
	return r.db.Save(event).Error
}

func (r *RiskEventRepository) Delete(id uint) error {
	return r.db.Delete(&model.RiskEvent{}, id).Error
}

func (r *RiskEventRepository) List(offset, limit int) ([]model.RiskEvent, int64, error) {
	var events []model.RiskEvent
	var total int64

	err := r.db.Model(&model.RiskEvent{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Preload("Reporter").Preload("Assignee").
		Order("created_at DESC").
		Offset(offset).Limit(limit).Find(&events).Error
	if err != nil {
		return nil, 0, err
	}

	return events, total, nil
}
