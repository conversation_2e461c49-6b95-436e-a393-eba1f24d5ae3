package service

import (
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
)

type RoleService struct {
	roleRepo *repository.RoleRepository
}

func NewRoleService(roleRepo *repository.RoleRepository) *RoleService {
	return &RoleService{roleRepo: roleRepo}
}

func (s *RoleService) Create(role *model.Role) error {
	return s.roleRepo.Create(role)
}

func (s *RoleService) GetByID(id uint) (*model.Role, error) {
	return s.roleRepo.GetByID(id)
}

func (s *RoleService) Update(role *model.Role) error {
	return s.roleRepo.Update(role)
}

func (s *RoleService) Delete(id uint) error {
	return s.roleRepo.Delete(id)
}

func (s *RoleService) List() ([]model.Role, error) {
	return s.roleRepo.List()
}
