<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>医院内部控制与运营管理系统</h2>
        <p>Hospital Internal Control & Operations Management System</p>
      </div>

      <a-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @finish="handleLogin"
      >
        <a-form-item name="principal">
          <a-input
            v-model:value="loginForm.principal"
            placeholder="请输入工号/邮箱/手机号"
            size="large"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item name="credential">
          <a-input-password
            v-model:value="loginForm.credential"
            placeholder="请输入密码"
            size="large"
            @pressEnter="handleLogin"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            size="large"
            :loading="loading"
            class="login-button"
            html-type="submit"
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { login } from '@/api/auth'
import type { LoginForm } from '@/types'

const router = useRouter()
const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive<LoginForm>({
  principal: '',
  credential: ''
})

const loginRules = {
  principal: [
    { required: true, message: '请输入工号/邮箱/手机号', trigger: 'blur' }
  ],
  credential: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
    loading.value = true

    const response = await login(loginForm)
    const { token, expires_in } = response.data

    localStorage.setItem('token', token)
    localStorage.setItem('token_expires', String(Date.now() + expires_in * 1000))

    message.success('登录成功')
    router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
    message.error('登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-top: 20px;
}

.login-button {
  width: 100%;
}
</style>
