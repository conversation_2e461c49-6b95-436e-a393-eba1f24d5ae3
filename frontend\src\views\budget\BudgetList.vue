<template>
  <div class="budget-list">
    <a-page-header title="预算管理" sub-title="年度预算的制定、预算科目设置" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="selectedYear"
            placeholder="选择年度"
            style="width: 120px"
            @change="handleYearChange"
          >
            <a-select-option v-for="year in yearOptions" :key="year" :value="year">
              {{ year }}年
            </a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedDepartment"
            placeholder="选择部门"
            style="width: 200px"
            allow-clear
            @change="handleDepartmentChange"
          >
            <a-select-option v-for="dept in departmentOptions" :key="dept.id" :value="dept.id">
              {{ dept.name }}
            </a-select-option>
          </a-select>
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            制定预算
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
        :expand-row-by-click="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'total_amount'">
            <span class="amount">¥{{ formatAmount(record.total_amount) }}</span>
          </template>
          
          <template v-if="column.key === 'execution_rate'">
            <a-progress
              :percent="getExecutionRate(record)"
              size="small"
              :status="getExecutionRate(record) > 90 ? 'exception' : 'normal'"
            />
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewBudgetItems(record)">
                <EyeOutlined />
                查看明细
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="showEditModal(record)"
                :disabled="record.status === 'ACTIVE'"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="approve" v-if="record.status === 'PENDING'">
                      <a @click="approveBudget(record.id)">批准预算</a>
                    </a-menu-item>
                    <a-menu-item key="adjust">
                      <a @click="showAdjustModal(record)">预算调整</a>
                    </a-menu-item>
                    <a-menu-item key="copy">
                      <a @click="copyBudget(record)">复制预算</a>
                    </a-menu-item>
                    <a-menu-item key="delete" danger v-if="record.status === 'DRAFT'">
                      <a @click="deleteBudget(record.id)">删除</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>

        <template #expandedRowRender="{ record }">
          <div class="budget-items">
            <a-table
              :columns="itemColumns"
              :data-source="record.budget_items || []"
              :pagination="false"
              size="small"
              row-key="id"
            >
              <template #bodyCell="{ column, record: item }">
                <template v-if="column.key === 'budget_amount'">
                  <span class="amount">¥{{ formatAmount(item.budget_amount) }}</span>
                </template>
                <template v-if="column.key === 'used_amount'">
                  <span class="amount used">¥{{ formatAmount(item.used_amount) }}</span>
                </template>
                <template v-if="column.key === 'available_amount'">
                  <span class="amount available">¥{{ formatAmount(item.available_amount) }}</span>
                </template>
                <template v-if="column.key === 'usage_rate'">
                  <a-progress
                    :percent="getUsageRate(item)"
                    size="small"
                    :status="getUsageRate(item) > 90 ? 'exception' : 'normal'"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑预算模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑预算' : '制定预算'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="预算年度" name="budget_year">
              <a-select v-model:value="formData.budget_year" placeholder="选择预算年度">
                <a-select-option v-for="year in yearOptions" :key="year" :value="year">
                  {{ year }}年
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="归属部门" name="department_id">
              <a-tree-select
                v-model:value="formData.department_id"
                :tree-data="departmentTreeOptions"
                :field-names="{ children: 'children', label: 'name', value: 'id' }"
                placeholder="选择归属部门"
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="预算项目">
          <a-table
            :columns="editItemColumns"
            :data-source="formData.budget_items"
            :pagination="false"
            size="small"
            row-key="temp_id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'item_name'">
                <a-input v-model:value="record.item_name" placeholder="项目名称" />
              </template>
              <template v-if="column.key === 'item_code'">
                <a-input v-model:value="record.item_code" placeholder="项目编码" />
              </template>
              <template v-if="column.key === 'category'">
                <a-select v-model:value="record.category" placeholder="选择分类">
                  <a-select-option value="人员费用">人员费用</a-select-option>
                  <a-select-option value="设备费用">设备费用</a-select-option>
                  <a-select-option value="材料费用">材料费用</a-select-option>
                  <a-select-option value="管理费用">管理费用</a-select-option>
                  <a-select-option value="其他费用">其他费用</a-select-option>
                </a-select>
              </template>
              <template v-if="column.key === 'budget_amount'">
                <a-input-number
                  v-model:value="record.budget_amount"
                  :min="0"
                  :precision="2"
                  placeholder="预算金额"
                  style="width: 100%"
                  @change="calculateTotal"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="text" size="small" danger @click="removeItem(index)">
                  <DeleteOutlined />
                </a-button>
              </template>
            </template>
          </a-table>
          
          <a-button type="dashed" block @click="addItem" style="margin-top: 16px">
            <PlusOutlined />
            添加预算项目
          </a-button>
          
          <div class="total-amount">
            <strong>预算总金额：¥{{ formatAmount(formData.total_amount) }}</strong>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 预算明细查看模态框 -->
    <a-modal
      v-model:open="itemsModalVisible"
      title="预算明细"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentBudget">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="预算年度">{{ currentBudget.budget_year }}年</a-descriptions-item>
          <a-descriptions-item label="归属部门">{{ currentBudget.department?.name }}</a-descriptions-item>
          <a-descriptions-item label="预算状态">
            <a-tag :color="getStatusColor(currentBudget.status)">
              {{ getStatusText(currentBudget.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预算总金额">¥{{ formatAmount(currentBudget.total_amount) }}</a-descriptions-item>
          <a-descriptions-item label="已使用金额">¥{{ formatAmount(getUsedAmount(currentBudget)) }}</a-descriptions-item>
          <a-descriptions-item label="剩余金额">¥{{ formatAmount(getAvailableAmount(currentBudget)) }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>预算项目明细</a-divider>
        
        <a-table
          :columns="detailItemColumns"
          :data-source="currentBudget.budget_items || []"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'budget_amount'">
              <span class="amount">¥{{ formatAmount(record.budget_amount) }}</span>
            </template>
            <template v-if="column.key === 'used_amount'">
              <span class="amount used">¥{{ formatAmount(record.used_amount) }}</span>
            </template>
            <template v-if="column.key === 'frozen_amount'">
              <span class="amount frozen">¥{{ formatAmount(record.frozen_amount) }}</span>
            </template>
            <template v-if="column.key === 'available_amount'">
              <span class="amount available">¥{{ formatAmount(record.available_amount) }}</span>
            </template>
            <template v-if="column.key === 'usage_rate'">
              <a-progress
                :percent="getUsageRate(record)"
                size="small"
                :status="getUsageRate(record) > 90 ? 'exception' : 'normal'"
              />
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined
} from '@ant-design/icons-vue'
import type { Budget, BudgetItem, Department } from '@/types'

const loading = ref(false)
const selectedYear = ref(new Date().getFullYear())
const selectedDepartment = ref<string>()
const tableData = ref<Budget[]>([])
const departmentOptions = ref<Department[]>([])
const departmentTreeOptions = ref<Department[]>([])
const modalVisible = ref(false)
const itemsModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentBudget = ref<Budget | null>(null)

const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  return [currentYear - 1, currentYear, currentYear + 1]
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '预算年度', dataIndex: 'budget_year', key: 'budget_year', width: 100 },
  { title: '归属部门', dataIndex: ['department', 'name'], key: 'department', width: 150 },
  { title: '预算总金额', key: 'total_amount', width: 150 },
  { title: '执行进度', key: 'execution_rate', width: 120 },
  { title: '状态', key: 'status', width: 100 },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const itemColumns = [
  { title: '项目名称', dataIndex: 'item_name', key: 'item_name' },
  { title: '项目编码', dataIndex: 'item_code', key: 'item_code' },
  { title: '分类', dataIndex: 'category', key: 'category' },
  { title: '预算金额', key: 'budget_amount' },
  { title: '已使用', key: 'used_amount' },
  { title: '可用金额', key: 'available_amount' },
  { title: '使用率', key: 'usage_rate' }
]

const editItemColumns = [
  { title: '项目名称', key: 'item_name', width: 150 },
  { title: '项目编码', key: 'item_code', width: 120 },
  { title: '分类', key: 'category', width: 120 },
  { title: '预算金额', key: 'budget_amount', width: 150 },
  { title: '操作', key: 'action', width: 80 }
]

const detailItemColumns = [
  { title: '项目名称', dataIndex: 'item_name', key: 'item_name' },
  { title: '项目编码', dataIndex: 'item_code', key: 'item_code' },
  { title: '分类', dataIndex: 'category', key: 'category' },
  { title: '预算金额', key: 'budget_amount' },
  { title: '已使用', key: 'used_amount' },
  { title: '冻结金额', key: 'frozen_amount' },
  { title: '可用金额', key: 'available_amount' },
  { title: '使用率', key: 'usage_rate' },
  { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true }
]

const formData = reactive({
  id: '',
  budget_year: new Date().getFullYear(),
  department_id: '',
  total_amount: 0,
  budget_items: [] as any[]
})

const formRules = {
  budget_year: [{ required: true, message: '请选择预算年度', trigger: 'change' }],
  department_id: [{ required: true, message: '请选择归属部门', trigger: 'change' }]
}

const getStatusColor = (status: string) => {
  const colors = {
    'DRAFT': 'default',
    'PENDING': 'processing',
    'APPROVED': 'success',
    'ACTIVE': 'green'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'DRAFT': '草稿',
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'ACTIVE': '执行中'
  }
  return texts[status] || status
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const getExecutionRate = (budget: Budget) => {
  if (!budget.budget_items || budget.budget_items.length === 0) return 0
  const totalUsed = budget.budget_items.reduce((sum, item) => sum + (item.used_amount || 0), 0)
  return Math.round((totalUsed / budget.total_amount) * 100)
}

const getUsageRate = (item: BudgetItem) => {
  if (!item.budget_amount) return 0
  return Math.round((item.used_amount / item.budget_amount) * 100)
}

const getUsedAmount = (budget: Budget) => {
  if (!budget.budget_items) return 0
  return budget.budget_items.reduce((sum, item) => sum + (item.used_amount || 0), 0)
}

const getAvailableAmount = (budget: Budget) => {
  if (!budget.budget_items) return budget.total_amount
  const totalUsed = budget.budget_items.reduce((sum, item) => sum + (item.used_amount || 0), 0)
  return budget.total_amount - totalUsed
}

const loadBudgets = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取预算列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        budget_year: 2024,
        department_id: '2',
        total_amount: 1000000,
        status: 'ACTIVE',
        created_at: '2024-01-15 10:00:00',
        updated_at: '2024-01-15 10:00:00',
        department: { id: '2', name: '内科', code: 'NK' },
        budget_items: [
          {
            id: '1',
            budget_id: '1',
            item_name: '医疗设备采购',
            item_code: 'YLSB001',
            category: '设备费用',
            budget_amount: 500000,
            used_amount: 350000,
            frozen_amount: 50000,
            available_amount: 100000,
            description: '心电图机、血压计等医疗设备采购'
          },
          {
            id: '2',
            budget_id: '1',
            item_name: '医用耗材',
            item_code: 'YYHC001',
            category: '材料费用',
            budget_amount: 300000,
            used_amount: 180000,
            frozen_amount: 20000,
            available_amount: 100000,
            description: '一次性医用耗材采购'
          },
          {
            id: '3',
            budget_id: '1',
            item_name: '培训费用',
            item_code: 'PXFY001',
            category: '管理费用',
            budget_amount: 200000,
            used_amount: 80000,
            frozen_amount: 0,
            available_amount: 120000,
            description: '医护人员培训费用'
          }
        ]
      }
    ]
    pagination.total = 1
  } catch (error) {
    message.error('加载预算数据失败')
  } finally {
    loading.value = false
  }
}

const loadDepartments = async () => {
  try {
    // TODO: 调用API获取部门数据
    const depts = [
      { id: '1', name: '医院总部', code: 'HQ' },
      { id: '2', name: '内科', code: 'NK' },
      { id: '3', name: '心内科', code: 'XNK' },
      { id: '4', name: '外科', code: 'WK' }
    ]
    departmentOptions.value = depts
    departmentTreeOptions.value = [
      {
        id: '1',
        name: '医院总部',
        code: 'HQ',
        children: [
          { id: '2', name: '内科', code: 'NK' },
          { id: '3', name: '心内科', code: 'XNK' },
          { id: '4', name: '外科', code: 'WK' }
        ]
      }
    ]
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

const handleYearChange = () => {
  pagination.current = 1
  loadBudgets()
}

const handleDepartmentChange = () => {
  pagination.current = 1
  loadBudgets()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadBudgets()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    budget_year: selectedYear.value,
    department_id: '',
    total_amount: 0,
    budget_items: []
  })
}

const showEditModal = (budget: Budget) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, {
    ...budget,
    budget_items: budget.budget_items?.map((item, index) => ({
      ...item,
      temp_id: index
    })) || []
  })
}

const viewBudgetItems = (budget: Budget) => {
  currentBudget.value = budget
  itemsModalVisible.value = true
}

const addItem = () => {
  formData.budget_items.push({
    temp_id: Date.now(),
    item_name: '',
    item_code: '',
    category: '',
    budget_amount: 0,
    description: ''
  })
}

const removeItem = (index: number) => {
  formData.budget_items.splice(index, 1)
  calculateTotal()
}

const calculateTotal = () => {
  formData.total_amount = formData.budget_items.reduce((sum, item) => sum + (item.budget_amount || 0), 0)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (formData.budget_items.length === 0) {
      message.error('请至少添加一个预算项目')
      return
    }
    
    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('预算更新成功')
    } else {
      // TODO: 调用创建API
      message.success('预算制定成功')
    }
    
    modalVisible.value = false
    loadBudgets()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const approveBudget = async (id: string) => {
  try {
    // TODO: 调用审批API
    message.success('预算审批成功')
    loadBudgets()
  } catch (error) {
    message.error('审批失败')
  }
}

const showAdjustModal = (budget: Budget) => {
  // TODO: 实现预算调整功能
  message.info('预算调整功能开发中')
}

const copyBudget = (budget: Budget) => {
  // TODO: 实现预算复制功能
  message.info('预算复制功能开发中')
}

const deleteBudget = async (id: string) => {
  try {
    // TODO: 调用删除API
    message.success('预算删除成功')
    loadBudgets()
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  loadBudgets()
  loadDepartments()
})
</script>

<style scoped>
.budget-list {
  padding: 24px;
}

.budget-items {
  margin: 16px 0;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.amount.used {
  color: #f5222d;
}

.amount.frozen {
  color: #fa8c16;
}

.amount.available {
  color: #52c41a;
}

.total-amount {
  text-align: right;
  margin-top: 16px;
  padding: 12px;
  background: #f0f2f5;
  border-radius: 6px;
}
</style>
