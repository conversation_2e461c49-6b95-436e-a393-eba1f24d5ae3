<template>
  <div class="expense-applications">
    <a-page-header title="报销申请" sub-title="各类费用的报销申请和审批" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="statusFilter"
            placeholder="申请状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="DRAFT">草稿</a-select-option>
            <a-select-option value="PENDING">待审批</a-select-option>
            <a-select-option value="APPROVED">已批准</a-select-option>
            <a-select-option value="REJECTED">已拒绝</a-select-option>
            <a-select-option value="PAID">已支付</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="dateRange"
            placeholder="选择日期范围"
            @change="handleFilterChange"
          />
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新建报销
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
        :expand-row-by-click="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'total_amount'">
            <span class="amount">¥{{ formatAmount(record.total_amount) }}</span>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'pre_application'">
            <a-tag v-if="record.pre_application_id" color="blue">
              关联事前申请
            </a-tag>
            <span v-else>-</span>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="showEditModal(record)"
                :disabled="!['DRAFT', 'REJECTED'].includes(record.status)"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="submitApplication(record.id)"
                :disabled="record.status !== 'DRAFT'"
              >
                <SendOutlined />
                提交
              </a-button>
              <a-dropdown>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="copy">
                      <a @click="copyApplication(record)">复制申请</a>
                    </a-menu-item>
                    <a-menu-item key="print">
                      <a @click="printApplication(record)">打印申请</a>
                    </a-menu-item>
                    <a-menu-item key="delete" danger v-if="record.status === 'DRAFT'">
                      <a @click="deleteApplication(record.id)">删除</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>

        <template #expandedRowRender="{ record }">
          <div class="expense-details">
            <a-table
              :columns="detailColumns"
              :data-source="record.expense_details || []"
              :pagination="false"
              size="small"
              row-key="id"
            >
              <template #bodyCell="{ column, record: detail }">
                <template v-if="column.key === 'amount'">
                  <span class="amount">¥{{ formatAmount(detail.amount) }}</span>
                </template>
                <template v-if="column.key === 'expense_date'">
                  {{ formatDate(detail.expense_date) }}
                </template>
                <template v-if="column.key === 'invoice_info'">
                  <a-tag v-if="detail.invoice_info" color="green">有发票</a-tag>
                  <a-tag v-else color="orange">无发票</a-tag>
                </template>
              </template>
            </a-table>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑报销申请模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑报销申请' : '新建报销申请'"
      width="1000px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="报销单号" name="application_code">
              <a-input v-model:value="formData.application_code" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="费用归属部门" name="department_id">
              <a-tree-select
                v-model:value="formData.department_id"
                :tree-data="departmentOptions"
                :field-names="{ children: 'children', label: 'name', value: 'id' }"
                placeholder="选择费用归属部门"
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="关联事前申请" name="pre_application_id">
              <a-select
                v-model:value="formData.pre_application_id"
                placeholder="选择关联的事前申请"
                allow-clear
                @change="handlePreApplicationChange"
              >
                <a-select-option 
                  v-for="app in preApplicationOptions" 
                  :key="app.id" 
                  :value="app.id"
                >
                  {{ app.application_code }} - {{ app.title }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="报销标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入报销标题" />
        </a-form-item>
        
        <a-form-item label="报销明细">
          <a-table
            :columns="editDetailColumns"
            :data-source="formData.expense_details"
            :pagination="false"
            size="small"
            row-key="temp_id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'budget_item_id'">
                <a-select
                  v-model:value="record.budget_item_id"
                  placeholder="选择预算项目"
                  style="width: 100%"
                  show-search
                  :filter-option="filterBudgetItem"
                >
                  <a-select-option 
                    v-for="item in budgetItemOptions" 
                    :key="item.id" 
                    :value="item.id"
                  >
                    {{ item.item_name }} ({{ item.item_code }})
                  </a-select-option>
                </a-select>
              </template>
              <template v-if="column.key === 'description'">
                <a-input v-model:value="record.description" placeholder="费用描述" />
              </template>
              <template v-if="column.key === 'amount'">
                <a-input-number
                  v-model:value="record.amount"
                  :min="0"
                  :precision="2"
                  placeholder="金额"
                  style="width: 100%"
                  @change="calculateTotal"
                />
              </template>
              <template v-if="column.key === 'expense_date'">
                <a-date-picker
                  v-model:value="record.expense_date"
                  placeholder="费用发生日期"
                  style="width: 100%"
                />
              </template>
              <template v-if="column.key === 'invoice_info'">
                <a-input v-model:value="record.invoice_info" placeholder="发票信息" />
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="text" size="small" @click="uploadInvoice(index)">
                    <UploadOutlined />
                  </a-button>
                  <a-button type="text" size="small" danger @click="removeDetail(index)">
                    <DeleteOutlined />
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
          
          <a-button type="dashed" block @click="addDetail" style="margin-top: 16px">
            <PlusOutlined />
            添加报销明细
          </a-button>
          
          <div class="total-amount">
            <strong>报销总金额：¥{{ formatAmount(formData.total_amount) }}</strong>
          </div>
        </a-form-item>
        
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            multiple
            list-type="picture-card"
          >
            <div>
              <PlusOutlined />
              <div style="margin-top: 8px">上传</div>
            </div>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情查看模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="报销申请详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentRecord">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="报销单号">{{ currentRecord.application_code }}</a-descriptions-item>
          <a-descriptions-item label="报销标题" :span="2">{{ currentRecord.title }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentRecord.applicant?.user_name }}</a-descriptions-item>
          <a-descriptions-item label="费用归属部门">{{ currentRecord.department?.name }}</a-descriptions-item>
          <a-descriptions-item label="报销总金额">¥{{ formatAmount(currentRecord.total_amount) }}</a-descriptions-item>
          <a-descriptions-item label="申请时间">{{ currentRecord.created_at }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="关联事前申请">
            <a-tag v-if="currentRecord.pre_application_id" color="blue">
              {{ currentRecord.pre_application?.application_code }}
            </a-tag>
            <span v-else>-</span>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>报销明细</a-divider>
        
        <a-table
          :columns="viewDetailColumns"
          :data-source="currentRecord.expense_details || []"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'amount'">
              <span class="amount">¥{{ formatAmount(record.amount) }}</span>
            </template>
            <template v-if="column.key === 'expense_date'">
              {{ formatDate(record.expense_date) }}
            </template>
            <template v-if="column.key === 'invoice_info'">
              <a-tag v-if="record.invoice_info" color="green">有发票</a-tag>
              <a-tag v-else color="orange">无发票</a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  SendOutlined,
  MoreOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import type { ExpenseApplication, ExpenseDetail, Department, BudgetItem, PreApplication } from '@/types'

const loading = ref(false)
const statusFilter = ref<string>()
const dateRange = ref()
const tableData = ref<ExpenseApplication[]>([])
const departmentOptions = ref<Department[]>([])
const budgetItemOptions = ref<BudgetItem[]>([])
const preApplicationOptions = ref<PreApplication[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentRecord = ref<ExpenseApplication | null>(null)
const fileList = ref([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '报销单号', dataIndex: 'application_code', key: 'application_code', width: 150 },
  { title: '报销标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '报销金额', key: 'total_amount', width: 120 },
  { title: '申请人', dataIndex: ['applicant', 'user_name'], key: 'applicant', width: 100 },
  { title: '费用部门', dataIndex: ['department', 'name'], key: 'department', width: 120 },
  { title: '事前申请', key: 'pre_application', width: 100 },
  { title: '申请时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
  { title: '状态', key: 'status', width: 100 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

const detailColumns = [
  { title: '预算项目', dataIndex: ['budget_item', 'item_name'], key: 'budget_item' },
  { title: '费用描述', dataIndex: 'description', key: 'description' },
  { title: '金额', key: 'amount' },
  { title: '费用日期', key: 'expense_date' },
  { title: '发票', key: 'invoice_info' }
]

const editDetailColumns = [
  { title: '预算项目', key: 'budget_item_id', width: 200 },
  { title: '费用描述', key: 'description', width: 150 },
  { title: '金额', key: 'amount', width: 120 },
  { title: '费用日期', key: 'expense_date', width: 150 },
  { title: '发票信息', key: 'invoice_info', width: 120 },
  { title: '操作', key: 'action', width: 100 }
]

const viewDetailColumns = [
  { title: '预算项目', dataIndex: ['budget_item', 'item_name'], key: 'budget_item' },
  { title: '费用描述', dataIndex: 'description', key: 'description' },
  { title: '金额', key: 'amount' },
  { title: '费用日期', key: 'expense_date' },
  { title: '发票', key: 'invoice_info' }
]

const formData = reactive({
  id: '',
  application_code: '',
  title: '',
  department_id: '',
  total_amount: 0,
  pre_application_id: '',
  expense_details: [] as any[]
})

const formRules = {
  title: [{ required: true, message: '请输入报销标题', trigger: 'blur' }],
  department_id: [{ required: true, message: '请选择费用归属部门', trigger: 'change' }]
}

const getStatusColor = (status: string) => {
  const colors = {
    'DRAFT': 'default',
    'PENDING': 'processing',
    'APPROVED': 'success',
    'REJECTED': 'error',
    'PAID': 'green'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'DRAFT': '草稿',
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已拒绝',
    'PAID': '已支付'
  }
  return texts[status] || status
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const formatDate = (date: string) => {
  return date ? new Date(date).toLocaleDateString() : '-'
}

const generateApplicationCode = () => {
  const now = new Date()
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
  const timeStr = now.getTime().toString().slice(-4)
  return `BX${dateStr}${timeStr}`
}

const filterBudgetItem = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

const loadApplications = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取报销申请列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        application_code: 'BX20240723001',
        title: '北京出差费用报销',
        applicant_id: '1',
        department_id: '2',
        total_amount: 4850,
        status: 'APPROVED',
        pre_application_id: '1',
        created_at: '2024-07-23 10:30:00',
        updated_at: '2024-07-23 15:20:00',
        applicant: {
          id: '1',
          user_name: '张三'
        },
        department: {
          id: '2',
          name: '内科'
        },
        pre_application: {
          id: '1',
          application_code: 'PRE20240723001'
        },
        expense_details: [
          {
            id: '1',
            description: '高铁票',
            amount: 1200,
            expense_date: '2024-08-01',
            invoice_info: '发票号：12345678',
            budget_item: {
              id: '1',
              item_name: '差旅费'
            }
          },
          {
            id: '2',
            description: '住宿费',
            amount: 2400,
            expense_date: '2024-08-01',
            invoice_info: '发票号：87654321',
            budget_item: {
              id: '1',
              item_name: '差旅费'
            }
          },
          {
            id: '3',
            description: '餐费',
            amount: 1250,
            expense_date: '2024-08-02',
            invoice_info: '',
            budget_item: {
              id: '1',
              item_name: '差旅费'
            }
          }
        ]
      }
    ]
    pagination.total = 1
  } catch (error) {
    message.error('加载报销申请数据失败')
  } finally {
    loading.value = false
  }
}

const loadDepartments = async () => {
  try {
    // TODO: 调用API获取部门数据
    departmentOptions.value = [
      {
        id: '1',
        name: '医院总部',
        code: 'HQ',
        children: [
          { id: '2', name: '内科', code: 'NK' },
          { id: '3', name: '心内科', code: 'XNK' },
          { id: '4', name: '外科', code: 'WK' }
        ]
      }
    ]
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

const loadBudgetItems = async () => {
  try {
    // TODO: 调用API获取预算项目数据
    budgetItemOptions.value = [
      {
        id: '1',
        item_name: '差旅费',
        item_code: 'CLF001',
        budget_amount: 100000
      },
      {
        id: '2',
        item_name: '培训费',
        item_code: 'PXF001',
        budget_amount: 50000
      },
      {
        id: '3',
        item_name: '会议费',
        item_code: 'HYF001',
        budget_amount: 30000
      }
    ]
  } catch (error) {
    message.error('加载预算项目数据失败')
  }
}

const loadPreApplications = async () => {
  try {
    // TODO: 调用API获取已批准的事前申请
    preApplicationOptions.value = [
      {
        id: '1',
        application_code: 'PRE20240723001',
        title: '北京医疗器械展览会差旅',
        status: 'APPROVED'
      }
    ]
  } catch (error) {
    message.error('加载事前申请数据失败')
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadApplications()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadApplications()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    application_code: generateApplicationCode(),
    title: '',
    department_id: '',
    total_amount: 0,
    pre_application_id: '',
    expense_details: []
  })
  fileList.value = []
}

const showEditModal = (record: ExpenseApplication) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, {
    ...record,
    expense_details: record.expense_details?.map((detail, index) => ({
      ...detail,
      temp_id: index
    })) || []
  })
  fileList.value = []
}

const viewDetails = (record: ExpenseApplication) => {
  currentRecord.value = record
  detailModalVisible.value = true
}

const handlePreApplicationChange = (value: string) => {
  if (value) {
    const preApp = preApplicationOptions.value.find(app => app.id === value)
    if (preApp) {
      formData.title = `${preApp.title} - 报销申请`
    }
  }
}

const addDetail = () => {
  formData.expense_details.push({
    temp_id: Date.now(),
    budget_item_id: '',
    description: '',
    amount: 0,
    expense_date: null,
    invoice_info: ''
  })
}

const removeDetail = (index: number) => {
  formData.expense_details.splice(index, 1)
  calculateTotal()
}

const calculateTotal = () => {
  formData.total_amount = formData.expense_details.reduce((sum, item) => sum + (item.amount || 0), 0)
}

const uploadInvoice = (index: number) => {
  // TODO: 实现发票上传功能
  message.info('发票上传功能开发中')
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (formData.expense_details.length === 0) {
      message.error('请至少添加一条报销明细')
      return
    }
    
    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('报销申请更新成功')
    } else {
      // TODO: 调用创建API
      message.success('报销申请创建成功')
    }
    
    modalVisible.value = false
    loadApplications()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
  fileList.value = []
}

const submitApplication = async (id: string) => {
  try {
    // TODO: 调用提交API
    message.success('报销申请已提交，等待审批')
    loadApplications()
  } catch (error) {
    message.error('提交失败')
  }
}

const copyApplication = (record: ExpenseApplication) => {
  // TODO: 实现复制申请功能
  message.info('复制申请功能开发中')
}

const printApplication = (record: ExpenseApplication) => {
  // TODO: 实现打印申请功能
  message.info('打印申请功能开发中')
}

const deleteApplication = async (id: string) => {
  try {
    // TODO: 调用删除API
    message.success('报销申请已删除')
    loadApplications()
  } catch (error) {
    message.error('删除失败')
  }
}

const beforeUpload = (file: any) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
  }
  return false // 阻止自动上传
}

const handleRemove = (file: any) => {
  const index = fileList.value.indexOf(file)
  const newFileList = fileList.value.slice()
  newFileList.splice(index, 1)
  fileList.value = newFileList
}

onMounted(() => {
  loadApplications()
  loadDepartments()
  loadBudgetItems()
  loadPreApplications()
})
</script>

<style scoped>
.expense-applications {
  padding: 24px;
}

.expense-details {
  margin: 16px 0;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.total-amount {
  text-align: right;
  margin-top: 16px;
  padding: 12px;
  background: #f0f2f5;
  border-radius: 6px;
}
</style>
