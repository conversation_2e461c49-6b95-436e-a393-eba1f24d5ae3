package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type ReceiptRepository struct {
	db *gorm.DB
}

func NewReceiptRepository(db *gorm.DB) *ReceiptRepository {
	return &ReceiptRepository{db: db}
}

// CreateReceipt 创建入库单
func (r *ReceiptRepository) CreateReceipt(receipt *model.ReceiptRecord) error {
	return r.db.Create(receipt).Error
}

// GetReceiptByID 根据ID获取入库单
func (r *ReceiptRepository) GetReceiptByID(id string) (*model.ReceiptRecord, error) {
	var receipt model.ReceiptRecord
	err := r.db.Preload("Order").Preload("Supplier").Preload("Receiver").
		Preload("Inspector").Preload("ReceiptItems").
		Where("id = ?", id).First(&receipt).Error
	if err != nil {
		return nil, err
	}
	return &receipt, nil
}

// GetReceiptByCode 根据入库单号获取入库单
func (r *ReceiptRepository) GetReceiptByCode(code string) (*model.ReceiptRecord, error) {
	var receipt model.ReceiptRecord
	err := r.db.Preload("Order").Preload("Supplier").Preload("Receiver").
		Preload("Inspector").Preload("ReceiptItems").
		Where("receipt_code = ?", code).First(&receipt).Error
	if err != nil {
		return nil, err
	}
	return &receipt, nil
}

// UpdateReceipt 更新入库单
func (r *ReceiptRepository) UpdateReceipt(receipt *model.ReceiptRecord) error {
	return r.db.Save(receipt).Error
}

// DeleteReceipt 删除入库单
func (r *ReceiptRepository) DeleteReceipt(id string) error {
	return r.db.Where("id = ?", id).Delete(&model.ReceiptRecord{}).Error
}

// GetReceipts 获取入库单列表
func (r *ReceiptRepository) GetReceipts(offset, limit int, status, supplierID, startDate, endDate string) ([]model.ReceiptRecord, int64, error) {
	var receipts []model.ReceiptRecord
	var total int64

	query := r.db.Model(&model.ReceiptRecord{}).
		Preload("Order").Preload("Supplier").Preload("Receiver").Preload("Inspector")

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 供应商筛选
	if supplierID != "" {
		query = query.Where("supplier_id = ?", supplierID)
	}

	// 日期范围筛选
	if startDate != "" && endDate != "" {
		query = query.Where("receipt_date BETWEEN ? AND ?", startDate, endDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&receipts).Error
	return receipts, total, err
}

// GetReceiptsByOrderID 根据采购订单ID获取入库单列表
func (r *ReceiptRepository) GetReceiptsByOrderID(orderID string) ([]model.ReceiptRecord, error) {
	var receipts []model.ReceiptRecord
	err := r.db.Preload("ReceiptItems").Where("order_id = ?", orderID).Find(&receipts).Error
	return receipts, err
}

// CreateReceiptItem 创建入库明细
func (r *ReceiptRepository) CreateReceiptItem(item *model.ReceiptItem) error {
	return r.db.Create(item).Error
}

// UpdateReceiptItem 更新入库明细
func (r *ReceiptRepository) UpdateReceiptItem(item *model.ReceiptItem) error {
	return r.db.Save(item).Error
}

// GetReceiptItemsByReceiptID 根据入库单ID获取明细列表
func (r *ReceiptRepository) GetReceiptItemsByReceiptID(receiptID string) ([]model.ReceiptItem, error) {
	var items []model.ReceiptItem
	err := r.db.Where("receipt_id = ?", receiptID).Find(&items).Error
	return items, err
}

// CreateInspectionRecord 创建质检记录
func (r *ReceiptRepository) CreateInspectionRecord(record *model.InspectionRecord) error {
	return r.db.Create(record).Error
}

// GetInspectionRecordsByReceiptID 根据入库单ID获取质检记录
func (r *ReceiptRepository) GetInspectionRecordsByReceiptID(receiptID string) ([]model.InspectionRecord, error) {
	var records []model.InspectionRecord
	err := r.db.Preload("Inspector").Where("receipt_id = ?", receiptID).Find(&records).Error
	return records, err
}

// UpdateReceiptStatus 更新入库单状态
func (r *ReceiptRepository) UpdateReceiptStatus(id, status string) error {
	return r.db.Model(&model.ReceiptRecord{}).Where("id = ?", id).Update("status", status).Error
}

// BatchUpdateReceiptItems 批量更新入库明细
func (r *ReceiptRepository) BatchUpdateReceiptItems(items []model.ReceiptItem) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		for _, item := range items {
			if err := tx.Save(&item).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetPendingInspectionReceipts 获取待质检的入库单
func (r *ReceiptRepository) GetPendingInspectionReceipts(inspectorID string) ([]model.ReceiptRecord, error) {
	var receipts []model.ReceiptRecord
	query := r.db.Preload("Order").Preload("Supplier").Preload("Receiver").Preload("ReceiptItems").
		Where("status IN ?", []string{model.ReceiptStatusReceived, model.ReceiptStatusInspecting})

	if inspectorID != "" {
		query = query.Where("inspector_id = ? OR inspector_id IS NULL", inspectorID)
	}

	err := query.Order("receipt_date ASC").Find(&receipts).Error
	return receipts, err
}

// GetReceiptStatistics 获取入库统计数据
func (r *ReceiptRepository) GetReceiptStatistics(startDate, endDate string) (map[string]interface{}, error) {
	var result map[string]interface{} = make(map[string]interface{})

	// 总入库单数
	var totalCount int64
	query := r.db.Model(&model.ReceiptRecord{})
	if startDate != "" && endDate != "" {
		query = query.Where("receipt_date BETWEEN ? AND ?", startDate, endDate)
	}
	query.Count(&totalCount)
	result["total_count"] = totalCount

	// 各状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	query = r.db.Model(&model.ReceiptRecord{}).Select("status, COUNT(*) as count")
	if startDate != "" && endDate != "" {
		query = query.Where("receipt_date BETWEEN ? AND ?", startDate, endDate)
	}
	query.Group("status").Find(&statusStats)
	result["status_stats"] = statusStats

	// 总入库金额
	var totalAmount float64
	query = r.db.Model(&model.ReceiptRecord{}).Select("COALESCE(SUM(total_amount), 0)")
	if startDate != "" && endDate != "" {
		query = query.Where("receipt_date BETWEEN ? AND ?", startDate, endDate)
	}
	query.Scan(&totalAmount)
	result["total_amount"] = totalAmount

	return result, nil
}
