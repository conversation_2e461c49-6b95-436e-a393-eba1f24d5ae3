import api from './index'
import type { 
  ApprovalTask, 
  ProcessedTask, 
  ApprovalFlow, 
  ApiResponse, 
  PaginationParams, 
  PaginationResponse 
} from '@/types'

// 待办任务
export const getApprovalTasks = (params: PaginationParams & { 
  businessType?: string; 
  priority?: string 
}) => {
  return api.get<ApiResponse<PaginationResponse<ApprovalTask>>>('/approval/tasks', { params })
}

export const getApprovalTaskById = (id: string) => {
  return api.get<ApiResponse<ApprovalTask>>(`/approval/tasks/${id}`)
}

export const approveTask = (id: string, data: {
  comment?: string;
  next_action?: 'AUTO' | 'MANUAL';
  next_approver?: string;
}) => {
  return api.post<ApiResponse<null>>(`/approval/tasks/${id}/approve`, data)
}

export const rejectTask = (id: string, data: {
  comment: string;
}) => {
  return api.post<ApiResponse<null>>(`/approval/tasks/${id}/reject`, data)
}

export const batchApprove = (ids: string[], data: {
  comment?: string;
  next_action?: 'AUTO' | 'MANUAL';
  next_approver?: string;
}) => {
  return api.post<ApiResponse<null>>('/approval/tasks/batch-approve', { ids, ...data })
}

export const getTaskStats = () => {
  return api.get<ApiResponse<{
    total: number;
    today: number;
    overdue: number;
    weekCompleted: number;
  }>>('/approval/tasks/stats')
}

// 已办任务
export const getProcessedTasks = (params: PaginationParams & { 
  businessType?: string; 
  action?: string;
  startDate?: string;
  endDate?: string;
}) => {
  return api.get<ApiResponse<PaginationResponse<ProcessedTask>>>('/approval/processed-tasks', { params })
}

export const getProcessedTaskById = (id: string) => {
  return api.get<ApiResponse<ProcessedTask>>(`/approval/processed-tasks/${id}`)
}

export const getProcessedStats = () => {
  return api.get<ApiResponse<{
    thisMonth: number;
    approvalRate: number;
    avgProcessTime: number;
    total: number;
  }>>('/approval/processed-tasks/stats')
}

export const exportProcessedTasks = (params: {
  businessType?: string;
  action?: string;
  startDate?: string;
  endDate?: string;
  format?: string;
}) => {
  return api.get('/approval/processed-tasks/export', { params, responseType: 'blob' })
}

// 审批历史
export const getApprovalHistory = (params: PaginationParams & { 
  keyword?: string;
  businessType?: string; 
  status?: string;
  startDate?: string;
  endDate?: string;
}) => {
  return api.get<ApiResponse<PaginationResponse<ApprovalFlow>>>('/approval/history', { params })
}

export const getApprovalFlowById = (id: string) => {
  return api.get<ApiResponse<ApprovalFlow>>(`/approval/flows/${id}`)
}

export const getApprovalFlowByBusinessId = (businessType: string, businessId: string) => {
  return api.get<ApiResponse<ApprovalFlow>>(`/approval/flows/business/${businessType}/${businessId}`)
}

export const exportApprovalFlow = (id: string, format: string = 'pdf') => {
  return api.get(`/approval/flows/${id}/export`, { 
    params: { format }, 
    responseType: 'blob' 
  })
}

// 审批流程模板
export const getFlowTemplates = (businessType?: string) => {
  return api.get<ApiResponse<any[]>>('/approval/templates', { 
    params: { businessType } 
  })
}

export const getFlowTemplateById = (id: string) => {
  return api.get<ApiResponse<any>>(`/approval/templates/${id}`)
}

export const createFlowTemplate = (data: any) => {
  return api.post<ApiResponse<any>>('/approval/templates', data)
}

export const updateFlowTemplate = (id: string, data: any) => {
  return api.put<ApiResponse<any>>(`/approval/templates/${id}`, data)
}

export const deleteFlowTemplate = (id: string) => {
  return api.delete<ApiResponse<null>>(`/approval/templates/${id}`)
}

// 审批人管理
export const getApprovers = (params: { 
  departmentId?: string; 
  roleId?: string; 
  keyword?: string 
}) => {
  return api.get<ApiResponse<any[]>>('/approval/approvers', { params })
}

export const getApproversByStep = (templateId: string, step: number) => {
  return api.get<ApiResponse<any[]>>(`/approval/templates/${templateId}/steps/${step}/approvers`)
}

// 委托管理
export const getDelegations = () => {
  return api.get<ApiResponse<any[]>>('/approval/delegations')
}

export const createDelegation = (data: {
  delegate_to: string;
  business_types: string[];
  start_date: string;
  end_date: string;
  reason: string;
}) => {
  return api.post<ApiResponse<any>>('/approval/delegations', data)
}

export const updateDelegation = (id: string, data: any) => {
  return api.put<ApiResponse<any>>(`/approval/delegations/${id}`, data)
}

export const deleteDelegation = (id: string) => {
  return api.delete<ApiResponse<null>>(`/approval/delegations/${id}`)
}

// 审批提醒
export const getApprovalReminders = () => {
  return api.get<ApiResponse<any[]>>('/approval/reminders')
}

export const createApprovalReminder = (data: {
  task_id: string;
  remind_type: 'EMAIL' | 'SMS' | 'SYSTEM';
  remind_time: string;
  message: string;
}) => {
  return api.post<ApiResponse<any>>('/approval/reminders', data)
}

export const markReminderAsRead = (id: string) => {
  return api.post<ApiResponse<null>>(`/approval/reminders/${id}/read`)
}

// 审批统计
export const getApprovalStatistics = (params: {
  startDate: string;
  endDate: string;
  businessType?: string;
  departmentId?: string;
}) => {
  return api.get<ApiResponse<{
    totalTasks: number;
    completedTasks: number;
    avgProcessTime: number;
    approvalRate: number;
    businessTypeStats: any[];
    departmentStats: any[];
    timeStats: any[];
  }>>('/approval/statistics', { params })
}

export const getApprovalReport = (params: {
  startDate: string;
  endDate: string;
  businessType?: string;
  departmentId?: string;
  format?: string;
}) => {
  return api.get('/approval/report', { params, responseType: 'blob' })
}
