package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type ExpenseRepository struct {
	db *gorm.DB
}

func NewExpenseRepository(db *gorm.DB) *ExpenseRepository {
	return &ExpenseRepository{db: db}
}

func (r *ExpenseRepository) CreateApplication(application *model.ExpenseApplication) error {
	return r.db.Create(application).Error
}

func (r *ExpenseRepository) GetApplicationByID(id string) (*model.ExpenseApplication, error) {
	var application model.ExpenseApplication
	err := r.db.Preload("Applicant").
		Preload("Department").
		Preload("ExpenseDetails").
		Preload("ExpenseDetails.BudgetItem").
		First(&application, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &application, nil
}

func (r *ExpenseRepository) UpdateApplication(application *model.ExpenseApplication) error {
	return r.db.Save(application).Error
}

func (r *ExpenseRepository) DeleteApplication(id string) error {
	return r.db.Delete(&model.ExpenseApplication{}, "id = ?", id).Error
}

func (r *ExpenseRepository) GetApplicationsByUser(userID string, offset, limit int, status, startDate, endDate string) ([]model.ExpenseApplication, int64, error) {
	var applications []model.ExpenseApplication
	var total int64

	query := r.db.Model(&model.ExpenseApplication{}).Where("applicant_id = ?", userID)

	// 状态过滤
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 日期过滤
	if startDate != "" {
		query = query.Where("created_at >= ?", startDate)
	}
	if endDate != "" {
		query = query.Where("created_at <= ?", endDate+" 23:59:59")
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Preload("Applicant").
		Preload("Department").
		Order("created_at DESC").
		Offset(offset).Limit(limit).Find(&applications).Error
	if err != nil {
		return nil, 0, err
	}

	return applications, total, nil
}

func (r *ExpenseRepository) GetApplicationsByStatus(status string, offset, limit int) ([]model.ExpenseApplication, int64, error) {
	var applications []model.ExpenseApplication
	var total int64

	err := r.db.Model(&model.ExpenseApplication{}).Where("status = ?", status).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Preload("Applicant").
		Preload("Department").
		Where("status = ?", status).
		Order("created_at DESC").
		Offset(offset).Limit(limit).Find(&applications).Error
	if err != nil {
		return nil, 0, err
	}

	return applications, total, nil
}

func (r *ExpenseRepository) CreatePreApplication(preApp *model.PreApplication) error {
	return r.db.Create(preApp).Error
}

func (r *ExpenseRepository) GetPreApplicationByID(id string) (*model.PreApplication, error) {
	var preApp model.PreApplication
	err := r.db.Preload("Applicant").First(&preApp, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &preApp, nil
}

func (r *ExpenseRepository) UpdatePreApplication(preApp *model.PreApplication) error {
	return r.db.Save(preApp).Error
}

func (r *ExpenseRepository) DeletePreApplication(id string) error {
	return r.db.Delete(&model.PreApplication{}, "id = ?", id).Error
}

func (r *ExpenseRepository) CreatePayment(payment *model.Payment) error {
	return r.db.Create(payment).Error
}

func (r *ExpenseRepository) GetPaymentByID(id string) (*model.Payment, error) {
	var payment model.Payment
	err := r.db.First(&payment, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &payment, nil
}

func (r *ExpenseRepository) UpdatePayment(payment *model.Payment) error {
	return r.db.Save(payment).Error
}

func (r *ExpenseRepository) GetPaymentsByStatus(status string, offset, limit int) ([]model.Payment, int64, error) {
	var payments []model.Payment
	var total int64

	err := r.db.Model(&model.Payment{}).Where("status = ?", status).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Where("status = ?", status).
		Order("created_at DESC").
		Offset(offset).Limit(limit).Find(&payments).Error
	if err != nil {
		return nil, 0, err
	}

	return payments, total, nil
}
