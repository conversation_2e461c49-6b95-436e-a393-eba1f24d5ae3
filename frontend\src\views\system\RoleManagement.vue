<template>
  <div class="role-management">
    <a-page-header title="角色管理" sub-title="角色的增、删、改、查、权限分配" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索角色名称、编码"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新增角色
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'permissions'">
            <a-tooltip title="点击查看详细权限">
              <a-button type="link" size="small" @click="showPermissionModal(record)">
                查看权限 ({{ getPermissionCount(record.permissions) }})
              </a-button>
            </a-tooltip>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="showEditModal(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="text" size="small" @click="showPermissionAssignModal(record)">
                <KeyOutlined />
                分配权限
              </a-button>
              <a-popconfirm
                title="确定要删除这个角色吗？"
                @confirm="deleteRole(record.id)"
              >
                <a-button type="text" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑角色模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑角色' : '新增角色'"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="角色名称" name="role_name">
          <a-input v-model:value="formData.role_name" placeholder="请输入角色名称" />
        </a-form-item>
        
        <a-form-item label="角色编码" name="role_code">
          <a-input v-model:value="formData.role_code" placeholder="请输入角色编码" />
        </a-form-item>
        
        <a-form-item label="角色描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入角色描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限查看模态框 -->
    <a-modal
      v-model:open="permissionViewVisible"
      title="角色权限详情"
      :footer="null"
      width="800px"
    >
      <div v-if="currentRole">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="角色名称">{{ currentRole.role_name }}</a-descriptions-item>
          <a-descriptions-item label="角色编码">{{ currentRole.role_code }}</a-descriptions-item>
          <a-descriptions-item label="角色描述" :span="2">{{ currentRole.description }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>权限列表</a-divider>
        
        <a-tree
          :tree-data="currentRolePermissions"
          :field-names="{ children: 'children', title: 'name', key: 'code' }"
          checkable
          :checked-keys="getCheckedPermissions(currentRole.permissions)"
          disabled
        />
      </div>
    </a-modal>

    <!-- 权限分配模态框 -->
    <a-modal
      v-model:open="permissionAssignVisible"
      title="分配权限"
      width="800px"
      @ok="handlePermissionSubmit"
      @cancel="handlePermissionCancel"
    >
      <div v-if="currentRole">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="角色名称">{{ currentRole.role_name }}</a-descriptions-item>
          <a-descriptions-item label="角色编码">{{ currentRole.role_code }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>选择权限</a-divider>
        
        <a-tree
          v-model:checkedKeys="selectedPermissions"
          :tree-data="allPermissions"
          :field-names="{ children: 'children', title: 'name', key: 'code' }"
          checkable
          check-strictly
          :default-expand-all="true"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined
} from '@ant-design/icons-vue'
import type { Role } from '@/types'

const loading = ref(false)
const searchKeyword = ref('')
const tableData = ref<Role[]>([])
const modalVisible = ref(false)
const permissionViewVisible = ref(false)
const permissionAssignVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentRole = ref<Role | null>(null)
const selectedPermissions = ref<string[]>([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '角色名称', dataIndex: 'role_name', key: 'role_name' },
  { title: '角色编码', dataIndex: 'role_code', key: 'role_code' },
  { title: '角色描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '权限', key: 'permissions' },
  { title: '状态', key: 'status' },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at' },
  { title: '操作', key: 'action', width: 250 }
]

const formData = reactive({
  id: '',
  role_name: '',
  role_code: '',
  description: '',
  status: 1
})

const formRules = {
  role_name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  role_code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
}

// 所有权限的树形结构
const allPermissions = ref([
  {
    name: '系统管理',
    code: 'system',
    children: [
      { name: '用户管理', code: 'system:user' },
      { name: '角色管理', code: 'system:role' },
      { name: '部门管理', code: 'system:dept' }
    ]
  },
  {
    name: '预算管理',
    code: 'budget',
    children: [
      { name: '预算制定', code: 'budget:create' },
      { name: '预算查看', code: 'budget:view' },
      { name: '预算调整', code: 'budget:adjust' }
    ]
  },
  {
    name: '支出控制',
    code: 'expense',
    children: [
      { name: '报销申请', code: 'expense:apply' },
      { name: '报销审批', code: 'expense:approve' },
      { name: '报销查看', code: 'expense:view' }
    ]
  },
  {
    name: '审批管理',
    code: 'approval',
    children: [
      { name: '待办任务', code: 'approval:todo' },
      { name: '已办任务', code: 'approval:done' },
      { name: '审批历史', code: 'approval:history' }
    ]
  }
])

const currentRolePermissions = ref([])

const getPermissionCount = (permissions: string) => {
  try {
    const perms = JSON.parse(permissions || '[]')
    return Array.isArray(perms) ? perms.length : 0
  } catch {
    return 0
  }
}

const getCheckedPermissions = (permissions: string) => {
  try {
    return JSON.parse(permissions || '[]')
  } catch {
    return []
  }
}

const loadRoles = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取角色列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        role_name: '系统管理员',
        role_code: 'ADMIN',
        description: '系统管理员，拥有所有权限',
        permissions: '["system:user","system:role","system:dept","budget:create","budget:view","expense:apply","approval:todo"]',
        status: 1,
        created_at: '2024-01-01 10:00:00',
        updated_at: '2024-01-01 10:00:00'
      },
      {
        id: '2',
        role_name: '财务人员',
        role_code: 'FINANCE',
        description: '财务人员，负责预算和支出管理',
        permissions: '["budget:view","expense:approve","approval:todo","approval:done"]',
        status: 1,
        created_at: '2024-01-01 10:00:00',
        updated_at: '2024-01-01 10:00:00'
      },
      {
        id: '3',
        role_name: '普通用户',
        role_code: 'USER',
        description: '普通用户，只能申请报销',
        permissions: '["expense:apply","expense:view"]',
        status: 1,
        created_at: '2024-01-01 10:00:00',
        updated_at: '2024-01-01 10:00:00'
      }
    ]
    pagination.total = 3
  } catch (error) {
    message.error('加载角色数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadRoles()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadRoles()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    role_name: '',
    role_code: '',
    description: '',
    status: 1
  })
}

const showEditModal = (role: Role) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, role)
}

const showPermissionModal = (role: Role) => {
  currentRole.value = role
  currentRolePermissions.value = allPermissions.value
  permissionViewVisible.value = true
}

const showPermissionAssignModal = (role: Role) => {
  currentRole.value = role
  selectedPermissions.value = getCheckedPermissions(role.permissions)
  permissionAssignVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('角色更新成功')
    } else {
      // TODO: 调用创建API
      message.success('角色创建成功')
    }
    
    modalVisible.value = false
    loadRoles()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const handlePermissionSubmit = async () => {
  try {
    // TODO: 调用分配权限API
    message.success('权限分配成功')
    permissionAssignVisible.value = false
    loadRoles()
  } catch (error) {
    message.error('权限分配失败')
  }
}

const handlePermissionCancel = () => {
  permissionAssignVisible.value = false
  selectedPermissions.value = []
}

const deleteRole = async (id: string) => {
  try {
    // TODO: 调用删除API
    message.success('角色删除成功')
    loadRoles()
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.role-management {
  padding: 24px;
}
</style>
