package service

import (
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
)

type DepartmentService struct {
	deptRepo *repository.DepartmentRepository
}

func NewDepartmentService(deptRepo *repository.DepartmentRepository) *DepartmentService {
	return &DepartmentService{deptRepo: deptRepo}
}

func (s *DepartmentService) Create(dept *model.Department) error {
	return s.deptRepo.Create(dept)
}

func (s *DepartmentService) GetByID(id string) (*model.Department, error) {
	return s.deptRepo.GetByID(id)
}

func (s *DepartmentService) Update(dept *model.Department) error {
	return s.deptRepo.Update(dept)
}

func (s *DepartmentService) Delete(id string) error {
	return s.deptRepo.Delete(id)
}

func (s *DepartmentService) List() ([]model.Department, error) {
	return s.deptRepo.List()
}

func (s *DepartmentService) GetTree() ([]model.Department, error) {
	return s.deptRepo.GetTree()
}
