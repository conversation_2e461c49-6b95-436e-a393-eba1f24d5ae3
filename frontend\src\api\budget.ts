import api from './index'
import type { Budget, BudgetItem, BudgetAdjustment, ApiResponse, PaginationParams, PaginationResponse } from '@/types'

// 预算管理
export const getBudgetList = (params: PaginationParams & { year?: number; departmentId?: string }) => {
  return api.get<ApiResponse<PaginationResponse<Budget>>>('/budgets', { params })
}

export const getBudgetById = (id: string) => {
  return api.get<ApiResponse<Budget>>(`/budgets/${id}`)
}

export const createBudget = (data: Partial<Budget>) => {
  return api.post<ApiResponse<Budget>>('/budgets', data)
}

export const updateBudget = (id: string, data: Partial<Budget>) => {
  return api.put<ApiResponse<Budget>>(`/budgets/${id}`, data)
}

export const deleteBudget = (id: string) => {
  return api.delete<ApiResponse<null>>(`/budgets/${id}`)
}

export const approveBudget = (id: string) => {
  return api.post<ApiResponse<null>>(`/budgets/${id}/approve`)
}

// 预算项目管理
export const getBudgetItems = (budgetId: string) => {
  return api.get<ApiResponse<BudgetItem[]>>(`/budgets/${budgetId}/items`)
}

export const createBudgetItem = (budgetId: string, data: Partial<BudgetItem>) => {
  return api.post<ApiResponse<BudgetItem>>(`/budgets/${budgetId}/items`, data)
}

export const updateBudgetItem = (itemId: string, data: Partial<BudgetItem>) => {
  return api.put<ApiResponse<BudgetItem>>(`/budget-items/${itemId}`, data)
}

export const deleteBudgetItem = (itemId: string) => {
  return api.delete<ApiResponse<null>>(`/budget-items/${itemId}`)
}

// 预算监控
export const getBudgetMonitorData = (params: { year?: number; departmentId?: string; category?: string; alertLevel?: string }) => {
  return api.get<ApiResponse<BudgetItem[]>>('/budget-monitor', { params })
}

export const getBudgetStats = (params: { year?: number; departmentId?: string }) => {
  return api.get<ApiResponse<{
    totalBudget: number
    usedAmount: number
    frozenAmount: number
    availableAmount: number
  }>>('/budget-stats', { params })
}

export const exportBudgetReport = (params: { year?: number; departmentId?: string; format?: string }) => {
  return api.get('/budget-report/export', { params, responseType: 'blob' })
}

// 预算调整
export const getBudgetAdjustmentList = (params: PaginationParams & { status?: string }) => {
  return api.get<ApiResponse<PaginationResponse<BudgetAdjustment>>>('/budget-adjustments', { params })
}

export const getBudgetAdjustmentById = (id: string) => {
  return api.get<ApiResponse<BudgetAdjustment>>(`/budget-adjustments/${id}`)
}

export const createBudgetAdjustment = (data: Partial<BudgetAdjustment>) => {
  return api.post<ApiResponse<BudgetAdjustment>>('/budget-adjustments', data)
}

export const updateBudgetAdjustment = (id: string, data: Partial<BudgetAdjustment>) => {
  return api.put<ApiResponse<BudgetAdjustment>>(`/budget-adjustments/${id}`, data)
}

export const withdrawBudgetAdjustment = (id: string) => {
  return api.post<ApiResponse<null>>(`/budget-adjustments/${id}/withdraw`)
}

export const approveBudgetAdjustment = (id: string, comment?: string) => {
  return api.post<ApiResponse<null>>(`/budget-adjustments/${id}/approve`, { comment })
}

export const rejectBudgetAdjustment = (id: string, comment: string) => {
  return api.post<ApiResponse<null>>(`/budget-adjustments/${id}/reject`, { comment })
}
