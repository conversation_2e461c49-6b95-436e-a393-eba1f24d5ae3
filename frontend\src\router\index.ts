import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { title: '仪表盘', requiresAuth: true }
  },
  // 系统管理
  {
    path: '/system/departments',
    name: 'DepartmentManagement',
    component: () => import('@/views/system/DepartmentManagement.vue'),
    meta: { title: '组织架构管理', requiresAuth: true }
  },
  {
    path: '/system/users',
    name: 'UserManagement',
    component: () => import('@/views/system/UserManagement.vue'),
    meta: { title: '用户管理', requiresAuth: true }
  },
  {
    path: '/system/roles',
    name: 'RoleManagement',
    component: () => import('@/views/system/RoleManagement.vue'),
    meta: { title: '角色管理', requiresAuth: true }
  },

  // 预算管理
  {
    path: '/budget/list',
    name: 'BudgetList',
    component: () => import('@/views/budget/BudgetList.vue'),
    meta: { title: '预算制定', requiresAuth: true }
  },
  {
    path: '/budget/monitor',
    name: 'BudgetMonitor',
    component: () => import('@/views/budget/BudgetMonitor.vue'),
    meta: { title: '预算执行监控', requiresAuth: true }
  },
  {
    path: '/budget/adjustment',
    name: 'BudgetAdjustment',
    component: () => import('@/views/budget/BudgetAdjustment.vue'),
    meta: { title: '预算调整申请', requiresAuth: true }
  },

  // 支出控制
  {
    path: '/expense/pre-applications',
    name: 'PreApplication',
    component: () => import('@/views/expense/PreApplication.vue'),
    meta: { title: '事前申请', requiresAuth: true }
  },
  {
    path: '/expense/applications',
    name: 'ExpenseApplications',
    component: () => import('@/views/expense/ExpenseApplications.vue'),
    meta: { title: '报销申请', requiresAuth: true }
  },
  {
    path: '/expense/payments',
    name: 'PaymentManagement',
    component: () => import('@/views/expense/PaymentManagement.vue'),
    meta: { title: '付款管理', requiresAuth: true }
  },

  // 审批管理
  {
    path: '/approval/tasks',
    name: 'ApprovalTasks',
    component: () => import('@/views/approval/ApprovalTasks.vue'),
    meta: { title: '待办任务', requiresAuth: true }
  },
  {
    path: '/approval/processed',
    name: 'ProcessedTasks',
    component: () => import('@/views/approval/ProcessedTasks.vue'),
    meta: { title: '已办任务', requiresAuth: true }
  },
  {
    path: '/approval/history',
    name: 'ApprovalHistory',
    component: () => import('@/views/approval/ApprovalHistory.vue'),
    meta: { title: '审批历史', requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router
