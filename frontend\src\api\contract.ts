import api from './index'
import type { ApiResponse, PaginationParams, PaginationResponse } from '@/types'

// 合同相关类型定义
export interface Contract {
  id: string
  contract_code: string
  contract_name: string
  contract_type: string
  counterparty_id?: string
  counterparty_name: string
  total_amount: number
  start_date?: string
  end_date?: string
  status: 'DRAFT' | 'PENDING' | 'ACTIVE' | 'COMPLETED'
  created_at: string
  updated_at: string
  counterparty?: {
    id: string
    name: string
    credit_code: string
  }
  payment_schedules: PaymentSchedule[]
}

export interface PaymentSchedule {
  id: string
  contract_id: string
  phase_name: string
  due_date?: string
  amount: number
  status: 'PENDING' | 'PAID'
  created_at: string
  updated_at: string
}

export interface CreateContractRequest {
  contract_name: string
  contract_type: string
  counterparty_id?: string
  counterparty_name: string
  total_amount: number
  start_date?: string
  end_date?: string
  payment_schedules: {
    phase_name: string
    due_date: string
    amount: number
  }[]
  remark?: string
}

export interface UpdateContractRequest {
  contract_name?: string
  counterparty_name?: string
  total_amount?: number
  start_date?: string
  end_date?: string
  payment_schedules?: {
    phase_name: string
    due_date: string
    amount: number
  }[]
  remark?: string
}

export interface ContractStatistics {
  total_count: number
  total_amount: number
  status_stats: {
    status: string
    count: number
  }[]
  type_stats: {
    contract_type: string
    count: number
  }[]
}

// 获取合同列表
export const getContractList = (params: PaginationParams & {
  status?: string
  contract_type?: string
  counterparty_id?: string
  start_date?: string
  end_date?: string
}) => {
  return api.get<ApiResponse<PaginationResponse<Contract>>>('/contracts', { params })
}

// 根据ID获取合同详情
export const getContractById = (id: string) => {
  return api.get<ApiResponse<Contract>>(`/contracts/${id}`)
}

// 创建合同
export const createContract = (data: CreateContractRequest) => {
  return api.post<ApiResponse<Contract>>('/contracts', data)
}

// 更新合同
export const updateContract = (id: string, data: UpdateContractRequest) => {
  return api.put<ApiResponse<null>>(`/contracts/${id}`, data)
}

// 提交合同审批
export const submitContract = (id: string) => {
  return api.post<ApiResponse<null>>(`/contracts/${id}/submit`)
}

// 审批合同
export const approveContract = (id: string) => {
  return api.post<ApiResponse<null>>(`/contracts/${id}/approve`)
}

// 获取即将到期的合同
export const getExpiringContracts = (days?: number) => {
  return api.get<ApiResponse<Contract[]>>('/contracts/expiring', {
    params: { days }
  })
}

// 获取合同统计数据
export const getContractStatistics = (start_date?: string, end_date?: string) => {
  return api.get<ApiResponse<ContractStatistics>>('/contracts/statistics', {
    params: { start_date, end_date }
  })
}

// 获取待付款计划
export const getPendingPaymentSchedules = () => {
  return api.get<ApiResponse<PaymentSchedule[]>>('/contracts/payment-schedules/pending')
}

// 导出合同
export const exportContract = (id: string) => {
  return api.get(`/contracts/${id}/export`, { responseType: 'blob' })
}

// 打印合同
export const printContract = (id: string) => {
  return api.get(`/contracts/${id}/print`, { responseType: 'blob' })
}
