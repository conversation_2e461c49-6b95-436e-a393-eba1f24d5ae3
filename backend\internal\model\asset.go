package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AssetCategory 资产分类表 (tbl_asset_categories)
type AssetCategory struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	CategoryCode string   `json:"category_code" gorm:"size:50;uniqueIndex;not null;comment:分类编码"`
	CategoryName string   `json:"category_name" gorm:"size:100;not null;comment:分类名称"`
	ParentID    *uuid.UUID `json:"parent_id" gorm:"type:uuid;comment:父分类ID"`
	Level       int       `json:"level" gorm:"not null;default:1;comment:分类层级"`
	SortOrder   int       `json:"sort_order" gorm:"default:0;comment:排序"`
	IsActive    bool      `json:"is_active" gorm:"not null;default:true;comment:是否启用"`
	Description string    `json:"description" gorm:"type:text;comment:分类描述"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy   *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Parent   *AssetCategory  `json:"parent" gorm:"foreignKey:ParentID"`
	Children []AssetCategory `json:"children" gorm:"foreignKey:ParentID"`
	Assets   []Asset         `json:"assets" gorm:"foreignKey:CategoryID"`
}

// Asset 资产卡片表 (tbl_assets)
type Asset struct {
	ID               uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	AssetCode        string     `json:"asset_code" gorm:"size:50;uniqueIndex;not null;comment:资产编号"`
	AssetName        string     `json:"asset_name" gorm:"size:255;not null;comment:资产名称"`
	CategoryID       uuid.UUID  `json:"category_id" gorm:"type:uuid;not null;comment:资产分类ID"`
	Specification    string     `json:"specification" gorm:"size:500;comment:规格型号"`
	Brand            string     `json:"brand" gorm:"size:100;comment:品牌"`
	Model            string     `json:"model" gorm:"size:100;comment:型号"`
	SerialNumber     string     `json:"serial_number" gorm:"size:100;comment:序列号"`
	PurchaseDate     *time.Time `json:"purchase_date" gorm:"comment:采购日期"`
	PurchasePrice    float64    `json:"purchase_price" gorm:"type:decimal(18,2);comment:采购价格"`
	SupplierID       *uuid.UUID `json:"supplier_id" gorm:"type:uuid;comment:供应商ID"`
	ContractID       *uuid.UUID `json:"contract_id" gorm:"type:uuid;comment:关联合同ID"`
	ReceiptID        *uuid.UUID `json:"receipt_id" gorm:"type:uuid;comment:关联入库单ID"`
	DepartmentID     uuid.UUID  `json:"department_id" gorm:"type:uuid;not null;comment:所属部门"`
	LocationID       *uuid.UUID `json:"location_id" gorm:"type:uuid;comment:存放位置ID"`
	ResponsibleID    *uuid.UUID `json:"responsible_id" gorm:"type:uuid;comment:责任人ID"`
	Status           string     `json:"status" gorm:"size:30;not null;default:'NORMAL';comment:资产状态 NORMAL/USING/MAINTENANCE/SCRAPPED"`
	DepreciationRate float64    `json:"depreciation_rate" gorm:"type:decimal(5,2);default:0;comment:折旧率"`
	UsefulLife       int        `json:"useful_life" gorm:"default:0;comment:使用年限(月)"`
	WarrantyExpiry   *time.Time `json:"warranty_expiry" gorm:"comment:保修到期日"`
	QRCode           string     `json:"qr_code" gorm:"size:255;comment:二维码"`
	Barcode          string     `json:"barcode" gorm:"size:255;comment:条形码"`
	Remark           string     `json:"remark" gorm:"type:text;comment:备注"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy        *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy        *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Category     AssetCategory      `json:"category" gorm:"foreignKey:CategoryID"`
	Supplier     *Supplier          `json:"supplier" gorm:"foreignKey:SupplierID"`
	Contract     *Contract          `json:"contract" gorm:"foreignKey:ContractID"`
	Receipt      *ReceiptRecord     `json:"receipt" gorm:"foreignKey:ReceiptID"`
	Department   Department         `json:"department" gorm:"foreignKey:DepartmentID"`
	Location     *AssetLocation     `json:"location" gorm:"foreignKey:LocationID"`
	Responsible  *User              `json:"responsible" gorm:"foreignKey:ResponsibleID"`
	Movements    []AssetMovement    `json:"movements" gorm:"foreignKey:AssetID"`
	Maintenances []AssetMaintenance `json:"maintenances" gorm:"foreignKey:AssetID"`
}

// AssetLocation 资产位置表 (tbl_asset_locations)
type AssetLocation struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	LocationCode string   `json:"location_code" gorm:"size:50;uniqueIndex;not null;comment:位置编码"`
	LocationName string   `json:"location_name" gorm:"size:100;not null;comment:位置名称"`
	ParentID    *uuid.UUID `json:"parent_id" gorm:"type:uuid;comment:父位置ID"`
	Building    string    `json:"building" gorm:"size:100;comment:建筑物"`
	Floor       string    `json:"floor" gorm:"size:50;comment:楼层"`
	Room        string    `json:"room" gorm:"size:50;comment:房间"`
	Description string    `json:"description" gorm:"type:text;comment:位置描述"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy   *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Parent   *AssetLocation  `json:"parent" gorm:"foreignKey:ParentID"`
	Children []AssetLocation `json:"children" gorm:"foreignKey:ParentID"`
	Assets   []Asset         `json:"assets" gorm:"foreignKey:LocationID"`
}

// AssetMovement 资产变动记录表 (tbl_asset_movements)
type AssetMovement struct {
	ID            uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	AssetID       uuid.UUID  `json:"asset_id" gorm:"type:uuid;not null;comment:资产ID"`
	MovementType  string     `json:"movement_type" gorm:"size:30;not null;comment:变动类型 BORROW/RETURN/TRANSFER/DISPOSE"`
	FromUserID    *uuid.UUID `json:"from_user_id" gorm:"type:uuid;comment:原使用人ID"`
	ToUserID      *uuid.UUID `json:"to_user_id" gorm:"type:uuid;comment:新使用人ID"`
	FromDeptID    *uuid.UUID `json:"from_dept_id" gorm:"type:uuid;comment:原部门ID"`
	ToDeptID      *uuid.UUID `json:"to_dept_id" gorm:"type:uuid;comment:新部门ID"`
	FromLocationID *uuid.UUID `json:"from_location_id" gorm:"type:uuid;comment:原位置ID"`
	ToLocationID  *uuid.UUID `json:"to_location_id" gorm:"type:uuid;comment:新位置ID"`
	MovementDate  time.Time  `json:"movement_date" gorm:"not null;comment:变动日期"`
	Reason        string     `json:"reason" gorm:"type:text;comment:变动原因"`
	ApprovalID    *uuid.UUID `json:"approval_id" gorm:"type:uuid;comment:审批单ID"`
	Status        string     `json:"status" gorm:"size:30;not null;default:'PENDING';comment:状态 PENDING/APPROVED/COMPLETED"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy     *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy     *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Asset        Asset          `json:"asset" gorm:"foreignKey:AssetID"`
	FromUser     *User          `json:"from_user" gorm:"foreignKey:FromUserID"`
	ToUser       *User          `json:"to_user" gorm:"foreignKey:ToUserID"`
	FromDept     *Department    `json:"from_dept" gorm:"foreignKey:FromDeptID"`
	ToDept       *Department    `json:"to_dept" gorm:"foreignKey:ToDeptID"`
	FromLocation *AssetLocation `json:"from_location" gorm:"foreignKey:FromLocationID"`
	ToLocation   *AssetLocation `json:"to_location" gorm:"foreignKey:ToLocationID"`
}

// AssetMaintenance 资产维保记录表 (tbl_asset_maintenances)
type AssetMaintenance struct {
	ID              uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	AssetID         uuid.UUID  `json:"asset_id" gorm:"type:uuid;not null;comment:资产ID"`
	MaintenanceType string     `json:"maintenance_type" gorm:"size:30;not null;comment:维保类型 REPAIR/MAINTAIN/UPGRADE"`
	MaintenanceDate time.Time  `json:"maintenance_date" gorm:"not null;comment:维保日期"`
	Description     string     `json:"description" gorm:"type:text;not null;comment:维保内容"`
	Cost            float64    `json:"cost" gorm:"type:decimal(18,2);comment:维保费用"`
	SupplierID      *uuid.UUID `json:"supplier_id" gorm:"type:uuid;comment:维保供应商ID"`
	ResponsibleID   uuid.UUID  `json:"responsible_id" gorm:"type:uuid;not null;comment:负责人ID"`
	NextDate        *time.Time `json:"next_date" gorm:"comment:下次维保日期"`
	Status          string     `json:"status" gorm:"size:30;not null;default:'COMPLETED';comment:状态 PLANNED/IN_PROGRESS/COMPLETED"`
	Attachments     string     `json:"attachments" gorm:"type:jsonb;comment:附件JSON"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy       *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy       *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Asset       Asset     `json:"asset" gorm:"foreignKey:AssetID"`
	Supplier    *Supplier `json:"supplier" gorm:"foreignKey:SupplierID"`
	Responsible User      `json:"responsible" gorm:"foreignKey:ResponsibleID"`
}

// AssetInventory 资产盘点表 (tbl_asset_inventories)
type AssetInventory struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	InventoryCode string    `json:"inventory_code" gorm:"size:50;uniqueIndex;not null;comment:盘点单号"`
	InventoryName string    `json:"inventory_name" gorm:"size:255;not null;comment:盘点名称"`
	DepartmentID  *uuid.UUID `json:"department_id" gorm:"type:uuid;comment:盘点部门ID"`
	CategoryID    *uuid.UUID `json:"category_id" gorm:"type:uuid;comment:盘点分类ID"`
	StartDate     time.Time `json:"start_date" gorm:"not null;comment:盘点开始日期"`
	EndDate       *time.Time `json:"end_date" gorm:"comment:盘点结束日期"`
	Status        string    `json:"status" gorm:"size:30;not null;default:'PLANNING';comment:状态 PLANNING/IN_PROGRESS/COMPLETED"`
	TotalAssets   int       `json:"total_assets" gorm:"default:0;comment:应盘资产数"`
	CheckedAssets int       `json:"checked_assets" gorm:"default:0;comment:已盘资产数"`
	Description   string    `json:"description" gorm:"type:text;comment:盘点说明"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy     *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy     *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Department    *Department           `json:"department" gorm:"foreignKey:DepartmentID"`
	Category      *AssetCategory        `json:"category" gorm:"foreignKey:CategoryID"`
	InventoryItems []AssetInventoryItem `json:"inventory_items" gorm:"foreignKey:InventoryID"`
}

// AssetInventoryItem 资产盘点明细表 (tbl_asset_inventory_items)
type AssetInventoryItem struct {
	ID            uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	InventoryID   uuid.UUID  `json:"inventory_id" gorm:"type:uuid;not null;comment:盘点单ID"`
	AssetID       uuid.UUID  `json:"asset_id" gorm:"type:uuid;not null;comment:资产ID"`
	ExpectedStatus string    `json:"expected_status" gorm:"size:30;not null;comment:账面状态"`
	ActualStatus  string     `json:"actual_status" gorm:"size:30;comment:实际状态"`
	CheckResult   string     `json:"check_result" gorm:"size:30;comment:盘点结果 NORMAL/SURPLUS/DEFICIT/DAMAGED"`
	CheckDate     *time.Time `json:"check_date" gorm:"comment:盘点日期"`
	CheckerID     *uuid.UUID `json:"checker_id" gorm:"type:uuid;comment:盘点人ID"`
	Remark        string     `json:"remark" gorm:"type:text;comment:备注"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联
	Inventory AssetInventory `json:"inventory" gorm:"foreignKey:InventoryID"`
	Asset     Asset          `json:"asset" gorm:"foreignKey:AssetID"`
	Checker   *User          `json:"checker" gorm:"foreignKey:CheckerID"`
}

// TableName 设置表名
func (AssetCategory) TableName() string {
	return "tbl_asset_categories"
}

func (Asset) TableName() string {
	return "tbl_assets"
}

func (AssetLocation) TableName() string {
	return "tbl_asset_locations"
}

func (AssetMovement) TableName() string {
	return "tbl_asset_movements"
}

func (AssetMaintenance) TableName() string {
	return "tbl_asset_maintenances"
}

func (AssetInventory) TableName() string {
	return "tbl_asset_inventories"
}

func (AssetInventoryItem) TableName() string {
	return "tbl_asset_inventory_items"
}

// 状态常量
const (
	AssetStatusNormal      = "NORMAL"      // 正常
	AssetStatusUsing       = "USING"       // 使用中
	AssetStatusMaintenance = "MAINTENANCE" // 维修中
	AssetStatusScrapped    = "SCRAPPED"    // 已报废
)

const (
	MovementTypeBorrow   = "BORROW"   // 借用
	MovementTypeReturn   = "RETURN"   // 归还
	MovementTypeTransfer = "TRANSFER" // 调拨
	MovementTypeDispose  = "DISPOSE"  // 处置
)

const (
	MaintenanceTypeRepair   = "REPAIR"   // 维修
	MaintenanceTypeMaintain = "MAINTAIN" // 保养
	MaintenanceTypeUpgrade  = "UPGRADE"  // 升级
)

const (
	InventoryStatusPlanning   = "PLANNING"    // 计划中
	InventoryStatusInProgress = "IN_PROGRESS" // 进行中
	InventoryStatusCompleted  = "COMPLETED"   // 已完成
)

const (
	CheckResultNormal  = "NORMAL"  // 正常
	CheckResultSurplus = "SURPLUS" // 盘盈
	CheckResultDeficit = "DEFICIT" // 盘亏
	CheckResultDamaged = "DAMAGED" // 损坏
)
