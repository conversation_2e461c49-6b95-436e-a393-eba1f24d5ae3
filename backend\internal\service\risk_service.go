package service

import (
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
)

type RiskService struct {
	riskRepo *repository.RiskEventRepository
}

func NewRiskService(riskRepo *repository.RiskEventRepository) *RiskService {
	return &RiskService{riskRepo: riskRepo}
}

func (s *RiskService) CreateEvent(event *model.RiskEvent) error {
	// 计算风险评分
	event.RiskScore = float64(event.Probability * event.Impact)
	return s.riskRepo.Create(event)
}

func (s *RiskService) GetEventByID(id uint) (*model.RiskEvent, error) {
	return s.riskRepo.GetByID(id)
}

func (s *RiskService) UpdateEvent(event *model.RiskEvent) error {
	// 重新计算风险评分
	event.RiskScore = float64(event.Probability * event.Impact)
	return s.riskRepo.Update(event)
}

func (s *RiskService) DeleteEvent(id uint) error {
	return s.riskRepo.Delete(id)
}

func (s *RiskService) ListEvents(page, pageSize int) ([]model.RiskEvent, int64, error) {
	offset := (page - 1) * pageSize
	return s.riskRepo.List(offset, pageSize)
}
