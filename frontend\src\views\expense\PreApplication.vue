<template>
  <div class="pre-application">
    <a-page-header title="事前申请" sub-title="差旅、会议、培训等事前申请" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-select
            v-model:value="typeFilter"
            placeholder="申请类型"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="TRAVEL">差旅申请</a-select-option>
            <a-select-option value="MEETING">会议申请</a-select-option>
            <a-select-option value="TRAINING">培训申请</a-select-option>
          </a-select>
          <a-select
            v-model:value="statusFilter"
            placeholder="申请状态"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="PENDING">待审批</a-select-option>
            <a-select-option value="APPROVED">已批准</a-select-option>
            <a-select-option value="CLOSED">已关闭</a-select-option>
          </a-select>
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新建申请
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'estimated_amount'">
            <span class="amount">¥{{ formatAmount(record.estimated_amount) }}</span>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="viewDetails(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="showEditModal(record)"
                :disabled="record.status !== 'PENDING'"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-button 
                type="text" 
                size="small" 
                @click="createExpenseFromPre(record)"
                :disabled="record.status !== 'APPROVED'"
              >
                <FileAddOutlined />
                创建报销
              </a-button>
              <a-popconfirm
                title="确定要取消这个申请吗？"
                @confirm="cancelApplication(record.id)"
                :disabled="record.status !== 'PENDING'"
              >
                <a-button 
                  type="text" 
                  size="small" 
                  danger
                  :disabled="record.status !== 'PENDING'"
                >
                  <StopOutlined />
                  取消
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑申请模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑事前申请' : '新建事前申请'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="申请单号" name="application_code">
              <a-input v-model:value="formData.application_code" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="申请类型" name="type">
              <a-select v-model:value="formData.type" placeholder="选择申请类型" @change="handleTypeChange">
                <a-select-option value="TRAVEL">差旅申请</a-select-option>
                <a-select-option value="MEETING">会议申请</a-select-option>
                <a-select-option value="TRAINING">培训申请</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="申请标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入申请标题" />
        </a-form-item>
        
        <a-form-item label="预估总金额" name="estimated_amount">
          <a-input-number
            v-model:value="formData.estimated_amount"
            :min="0"
            :precision="2"
            placeholder="预估总金额"
            style="width: 100%"
            addon-after="元"
          />
        </a-form-item>
        
        <!-- 差旅申请详细信息 -->
        <div v-if="formData.type === 'TRAVEL'">
          <a-divider>差旅信息</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="出发地" name="departure">
                <a-input v-model:value="travelDetails.departure" placeholder="出发地" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="目的地" name="destination">
                <a-input v-model:value="travelDetails.destination" placeholder="目的地" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="出发时间" name="start_date">
                <a-date-picker
                  v-model:value="travelDetails.start_date"
                  placeholder="选择出发时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="返回时间" name="end_date">
                <a-date-picker
                  v-model:value="travelDetails.end_date"
                  placeholder="选择返回时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="差旅事由">
            <a-textarea v-model:value="travelDetails.purpose" placeholder="请说明差旅事由" :rows="3" />
          </a-form-item>
        </div>
        
        <!-- 会议申请详细信息 -->
        <div v-if="formData.type === 'MEETING'">
          <a-divider>会议信息</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="会议名称" name="meeting_name">
                <a-input v-model:value="meetingDetails.meeting_name" placeholder="会议名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="会议地点" name="meeting_location">
                <a-input v-model:value="meetingDetails.meeting_location" placeholder="会议地点" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="会议时间" name="meeting_date">
                <a-date-picker
                  v-model:value="meetingDetails.meeting_date"
                  placeholder="选择会议时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="参会人数" name="attendees_count">
                <a-input-number
                  v-model:value="meetingDetails.attendees_count"
                  :min="1"
                  placeholder="参会人数"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="会议目的">
            <a-textarea v-model:value="meetingDetails.purpose" placeholder="请说明会议目的" :rows="3" />
          </a-form-item>
        </div>
        
        <!-- 培训申请详细信息 -->
        <div v-if="formData.type === 'TRAINING'">
          <a-divider>培训信息</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="培训名称" name="training_name">
                <a-input v-model:value="trainingDetails.training_name" placeholder="培训名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="培训机构" name="training_org">
                <a-input v-model:value="trainingDetails.training_org" placeholder="培训机构" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="培训开始时间" name="start_date">
                <a-date-picker
                  v-model:value="trainingDetails.start_date"
                  placeholder="选择开始时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="培训结束时间" name="end_date">
                <a-date-picker
                  v-model:value="trainingDetails.end_date"
                  placeholder="选择结束时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="培训内容">
            <a-textarea v-model:value="trainingDetails.content" placeholder="请说明培训内容" :rows="3" />
          </a-form-item>
        </div>
        
        <a-form-item label="费用明细">
          <a-table
            :columns="detailColumns"
            :data-source="formData.cost_details"
            :pagination="false"
            size="small"
            row-key="temp_id"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'item_name'">
                <a-input v-model:value="record.item_name" placeholder="费用项目" />
              </template>
              <template v-if="column.key === 'amount'">
                <a-input-number
                  v-model:value="record.amount"
                  :min="0"
                  :precision="2"
                  placeholder="金额"
                  style="width: 100%"
                  @change="calculateTotal"
                />
              </template>
              <template v-if="column.key === 'description'">
                <a-input v-model:value="record.description" placeholder="说明" />
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="text" size="small" danger @click="removeDetail(index)">
                  <DeleteOutlined />
                </a-button>
              </template>
            </template>
          </a-table>
          
          <a-button type="dashed" block @click="addDetail" style="margin-top: 16px">
            <PlusOutlined />
            添加费用项目
          </a-button>
        </a-form-item>
        
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            multiple
          >
            <a-button>
              <UploadOutlined />
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情查看模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="事前申请详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="申请单号">{{ currentRecord.application_code }}</a-descriptions-item>
          <a-descriptions-item label="申请类型">
            <a-tag :color="getTypeColor(currentRecord.type)">
              {{ getTypeText(currentRecord.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申请标题" :span="2">{{ currentRecord.title }}</a-descriptions-item>
          <a-descriptions-item label="预估金额">¥{{ formatAmount(currentRecord.estimated_amount) }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentRecord.applicant?.user_name }}</a-descriptions-item>
          <a-descriptions-item label="申请时间">{{ currentRecord.created_at }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>详细信息</a-divider>
        
        <div v-if="currentRecord.details">
          <pre>{{ JSON.stringify(JSON.parse(currentRecord.details), null, 2) }}</pre>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  FileAddOutlined,
  StopOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import type { PreApplication } from '@/types'

const loading = ref(false)
const typeFilter = ref<string>()
const statusFilter = ref<string>()
const tableData = ref<PreApplication[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentRecord = ref<PreApplication | null>(null)
const fileList = ref([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '申请单号', dataIndex: 'application_code', key: 'application_code', width: 150 },
  { title: '申请类型', key: 'type', width: 100 },
  { title: '申请标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '预估金额', key: 'estimated_amount', width: 120 },
  { title: '申请人', dataIndex: ['applicant', 'user_name'], key: 'applicant', width: 100 },
  { title: '申请时间', dataIndex: 'created_at', key: 'created_at', width: 150 },
  { title: '状态', key: 'status', width: 100 },
  { title: '操作', key: 'action', width: 250, fixed: 'right' }
]

const detailColumns = [
  { title: '费用项目', key: 'item_name', width: 150 },
  { title: '金额', key: 'amount', width: 120 },
  { title: '说明', key: 'description' },
  { title: '操作', key: 'action', width: 80 }
]

const formData = reactive({
  id: '',
  application_code: '',
  type: '',
  title: '',
  estimated_amount: 0,
  cost_details: [] as any[]
})

const travelDetails = reactive({
  departure: '',
  destination: '',
  start_date: null,
  end_date: null,
  purpose: ''
})

const meetingDetails = reactive({
  meeting_name: '',
  meeting_location: '',
  meeting_date: null,
  attendees_count: 1,
  purpose: ''
})

const trainingDetails = reactive({
  training_name: '',
  training_org: '',
  start_date: null,
  end_date: null,
  content: ''
})

const formRules = {
  type: [{ required: true, message: '请选择申请类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入申请标题', trigger: 'blur' }],
  estimated_amount: [{ required: true, message: '请输入预估金额', trigger: 'blur' }]
}

const getTypeColor = (type: string) => {
  const colors = {
    'TRAVEL': 'blue',
    'MEETING': 'green',
    'TRAINING': 'orange'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: string) => {
  const texts = {
    'TRAVEL': '差旅申请',
    'MEETING': '会议申请',
    'TRAINING': '培训申请'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'processing',
    'APPROVED': 'success',
    'CLOSED': 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'CLOSED': '已关闭'
  }
  return texts[status] || status
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const generateApplicationCode = () => {
  const now = new Date()
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
  const timeStr = now.getTime().toString().slice(-4)
  return `PRE${dateStr}${timeStr}`
}

const loadApplications = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取事前申请列表
    // 临时数据
    tableData.value = [
      {
        id: '1',
        application_code: 'PRE20240723001',
        type: 'TRAVEL',
        title: '北京医疗器械展览会差旅',
        estimated_amount: 5000,
        status: 'APPROVED',
        details: '{"departure":"上海","destination":"北京","start_date":"2024-08-01","end_date":"2024-08-03","purpose":"参加医疗器械展览会"}',
        created_at: '2024-07-23 09:30:00',
        updated_at: '2024-07-23 10:15:00',
        applicant: {
          id: '1',
          user_name: '张三'
        }
      },
      {
        id: '2',
        application_code: 'PRE20240722001',
        type: 'TRAINING',
        title: '医护人员急救技能培训',
        estimated_amount: 8000,
        status: 'PENDING',
        details: '{"training_name":"急救技能培训","training_org":"红十字会","start_date":"2024-08-10","end_date":"2024-08-12","content":"心肺复苏、外伤处理等急救技能"}',
        created_at: '2024-07-22 14:20:00',
        updated_at: '2024-07-22 14:20:00',
        applicant: {
          id: '2',
          user_name: '李四'
        }
      }
    ]
    pagination.total = 2
  } catch (error) {
    message.error('加载事前申请数据失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadApplications()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadApplications()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    application_code: generateApplicationCode(),
    type: '',
    title: '',
    estimated_amount: 0,
    cost_details: []
  })
  resetDetails()
  fileList.value = []
}

const showEditModal = (record: PreApplication) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, record)
  
  // 解析详细信息
  if (record.details) {
    const details = JSON.parse(record.details)
    if (record.type === 'TRAVEL') {
      Object.assign(travelDetails, details)
    } else if (record.type === 'MEETING') {
      Object.assign(meetingDetails, details)
    } else if (record.type === 'TRAINING') {
      Object.assign(trainingDetails, details)
    }
  }
  
  fileList.value = []
}

const viewDetails = (record: PreApplication) => {
  currentRecord.value = record
  detailModalVisible.value = true
}

const handleTypeChange = () => {
  resetDetails()
}

const resetDetails = () => {
  Object.assign(travelDetails, {
    departure: '',
    destination: '',
    start_date: null,
    end_date: null,
    purpose: ''
  })
  Object.assign(meetingDetails, {
    meeting_name: '',
    meeting_location: '',
    meeting_date: null,
    attendees_count: 1,
    purpose: ''
  })
  Object.assign(trainingDetails, {
    training_name: '',
    training_org: '',
    start_date: null,
    end_date: null,
    content: ''
  })
}

const addDetail = () => {
  formData.cost_details.push({
    temp_id: Date.now(),
    item_name: '',
    amount: 0,
    description: ''
  })
}

const removeDetail = (index: number) => {
  formData.cost_details.splice(index, 1)
  calculateTotal()
}

const calculateTotal = () => {
  formData.estimated_amount = formData.cost_details.reduce((sum, item) => sum + (item.amount || 0), 0)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 构建详细信息
    let details = {}
    if (formData.type === 'TRAVEL') {
      details = travelDetails
    } else if (formData.type === 'MEETING') {
      details = meetingDetails
    } else if (formData.type === 'TRAINING') {
      details = trainingDetails
    }
    
    const submitData = {
      ...formData,
      details: JSON.stringify(details)
    }
    
    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('事前申请更新成功')
    } else {
      // TODO: 调用创建API
      message.success('事前申请提交成功，等待审批')
    }
    
    modalVisible.value = false
    loadApplications()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
  resetDetails()
  fileList.value = []
}

const createExpenseFromPre = (record: PreApplication) => {
  // TODO: 跳转到报销申请页面，并预填事前申请信息
  message.info('跳转到报销申请页面')
}

const cancelApplication = async (id: string) => {
  try {
    // TODO: 调用取消API
    message.success('申请已取消')
    loadApplications()
  } catch (error) {
    message.error('取消失败')
  }
}

const beforeUpload = (file: any) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
  }
  return false // 阻止自动上传
}

const handleRemove = (file: any) => {
  const index = fileList.value.indexOf(file)
  const newFileList = fileList.value.slice()
  newFileList.splice(index, 1)
  fileList.value = newFileList
}

onMounted(() => {
  loadApplications()
})
</script>

<style scoped>
.pre-application {
  padding: 24px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}
</style>
