package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type BudgetRepository struct {
	db *gorm.DB
}

func NewBudgetRepository(db *gorm.DB) *BudgetRepository {
	return &BudgetRepository{db: db}
}

func (r *BudgetRepository) CreateBudget(budget *model.Budget) error {
	return r.db.Create(budget).Error
}

func (r *BudgetRepository) GetBudgetByID(id string) (*model.Budget, error) {
	var budget model.Budget
	err := r.db.Preload("Department").Preload("BudgetItems").First(&budget, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &budget, nil
}

func (r *BudgetRepository) UpdateBudget(budget *model.Budget) error {
	return r.db.Save(budget).Error
}

func (r *BudgetRepository) DeleteBudget(id string) error {
	return r.db.Delete(&model.Budget{}, "id = ?", id).Error
}

func (r *BudgetRepository) GetBudgetsByDepartment(departmentID string, year int) ([]model.Budget, error) {
	var budgets []model.Budget
	query := r.db.Preload("Department").Preload("BudgetItems").
		Where("department_id = ?", departmentID)
	
	if year > 0 {
		query = query.Where("budget_year = ?", year)
	}
	
	err := query.Find(&budgets).Error
	return budgets, err
}

func (r *BudgetRepository) GetBudgetsByYear(year int, offset, limit int) ([]model.Budget, int64, error) {
	var budgets []model.Budget
	var total int64

	err := r.db.Model(&model.Budget{}).Where("budget_year = ?", year).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Preload("Department").
		Where("budget_year = ?", year).
		Order("created_at DESC").
		Offset(offset).Limit(limit).Find(&budgets).Error
	if err != nil {
		return nil, 0, err
	}

	return budgets, total, nil
}

func (r *BudgetRepository) CreateBudgetItem(item *model.BudgetItem) error {
	return r.db.Create(item).Error
}

func (r *BudgetRepository) GetBudgetItemByID(id string) (*model.BudgetItem, error) {
	var item model.BudgetItem
	err := r.db.Preload("Budget").First(&item, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

func (r *BudgetRepository) UpdateBudgetItem(item *model.BudgetItem) error {
	return r.db.Save(item).Error
}

func (r *BudgetRepository) DeleteBudgetItem(id string) error {
	return r.db.Delete(&model.BudgetItem{}, "id = ?", id).Error
}

func (r *BudgetRepository) GetBudgetItemsByBudget(budgetID string) ([]model.BudgetItem, error) {
	var items []model.BudgetItem
	err := r.db.Where("budget_id = ?", budgetID).Find(&items).Error
	return items, err
}

func (r *BudgetRepository) FreezeBudgetAmount(itemID string, amount float64) error {
	return r.db.Model(&model.BudgetItem{}).
		Where("id = ?", itemID).
		Update("frozen_amount", gorm.Expr("frozen_amount + ?", amount)).
		Update("available_amount", gorm.Expr("available_amount - ?", amount)).Error
}

func (r *BudgetRepository) ReleaseBudgetAmount(itemID string, amount float64) error {
	return r.db.Model(&model.BudgetItem{}).
		Where("id = ?", itemID).
		Update("frozen_amount", gorm.Expr("frozen_amount - ?", amount)).
		Update("available_amount", gorm.Expr("available_amount + ?", amount)).Error
}

func (r *BudgetRepository) UseBudgetAmount(itemID string, amount float64) error {
	return r.db.Model(&model.BudgetItem{}).
		Where("id = ?", itemID).
		Update("frozen_amount", gorm.Expr("frozen_amount - ?", amount)).
		Update("used_amount", gorm.Expr("used_amount + ?", amount)).Error
}

func (r *BudgetRepository) CreateBudgetAdjustment(adjustment *model.BudgetAdjustment) error {
	return r.db.Create(adjustment).Error
}

func (r *BudgetRepository) GetBudgetAdjustmentByID(id string) (*model.BudgetAdjustment, error) {
	var adjustment model.BudgetAdjustment
	err := r.db.Preload("BudgetItem").Preload("Applicant").First(&adjustment, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &adjustment, nil
}

func (r *BudgetRepository) UpdateBudgetAdjustment(adjustment *model.BudgetAdjustment) error {
	return r.db.Save(adjustment).Error
}

func (r *BudgetRepository) GetBudgetAdjustmentsByStatus(status string, offset, limit int) ([]model.BudgetAdjustment, int64, error) {
	var adjustments []model.BudgetAdjustment
	var total int64

	err := r.db.Model(&model.BudgetAdjustment{}).Where("status = ?", status).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.db.Preload("BudgetItem").Preload("Applicant").
		Where("status = ?", status).
		Order("created_at DESC").
		Offset(offset).Limit(limit).Find(&adjustments).Error
	if err != nil {
		return nil, 0, err
	}

	return adjustments, total, nil
}
