package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type ContractRepository struct {
	db *gorm.DB
}

func NewContractRepository(db *gorm.DB) *ContractRepository {
	return &ContractRepository{db: db}
}

// CreateContract 创建合同
func (r *ContractRepository) CreateContract(contract *model.Contract) error {
	return r.db.Create(contract).Error
}

// GetContractByID 根据ID获取合同
func (r *ContractRepository) GetContractByID(id string) (*model.Contract, error) {
	var contract model.Contract
	err := r.db.Preload("Counterparty").Preload("PaymentSchedules").
		Where("id = ?", id).First(&contract).Error
	if err != nil {
		return nil, err
	}
	return &contract, nil
}

// GetContractByCode 根据合同编号获取合同
func (r *ContractRepository) GetContractByCode(code string) (*model.Contract, error) {
	var contract model.Contract
	err := r.db.Preload("Counterparty").Preload("PaymentSchedules").
		Where("contract_code = ?", code).First(&contract).Error
	if err != nil {
		return nil, err
	}
	return &contract, nil
}

// UpdateContract 更新合同
func (r *ContractRepository) UpdateContract(contract *model.Contract) error {
	return r.db.Save(contract).Error
}

// DeleteContract 删除合同
func (r *ContractRepository) DeleteContract(id string) error {
	return r.db.Where("id = ?", id).Delete(&model.Contract{}).Error
}

// GetContracts 获取合同列表
func (r *ContractRepository) GetContracts(offset, limit int, status, contractType, counterpartyID, startDate, endDate string) ([]model.Contract, int64, error) {
	var contracts []model.Contract
	var total int64

	query := r.db.Model(&model.Contract{}).Preload("Counterparty")

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 合同类型筛选
	if contractType != "" {
		query = query.Where("contract_type = ?", contractType)
	}

	// 对方单位筛选
	if counterpartyID != "" {
		query = query.Where("counterparty_id = ?", counterpartyID)
	}

	// 日期范围筛选
	if startDate != "" && endDate != "" {
		query = query.Where("start_date BETWEEN ? AND ?", startDate, endDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&contracts).Error
	return contracts, total, err
}

// GetContractsByCounterparty 根据对方单位获取合同列表
func (r *ContractRepository) GetContractsByCounterparty(counterpartyID string) ([]model.Contract, error) {
	var contracts []model.Contract
	err := r.db.Where("counterparty_id = ?", counterpartyID).Find(&contracts).Error
	return contracts, err
}

// UpdateContractStatus 更新合同状态
func (r *ContractRepository) UpdateContractStatus(id, status string) error {
	return r.db.Model(&model.Contract{}).Where("id = ?", id).Update("status", status).Error
}

// GetExpiringContracts 获取即将到期的合同
func (r *ContractRepository) GetExpiringContracts(days int) ([]model.Contract, error) {
	var contracts []model.Contract
	err := r.db.Preload("Counterparty").
		Where("status = ? AND end_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL ? DAY", 
			"ACTIVE", days).
		Find(&contracts).Error
	return contracts, err
}

// GetContractStatistics 获取合同统计数据
func (r *ContractRepository) GetContractStatistics(startDate, endDate string) (map[string]interface{}, error) {
	var result map[string]interface{} = make(map[string]interface{})

	// 总合同数
	var totalCount int64
	query := r.db.Model(&model.Contract{})
	if startDate != "" && endDate != "" {
		query = query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	}
	query.Count(&totalCount)
	result["total_count"] = totalCount

	// 各状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	query = r.db.Model(&model.Contract{}).Select("status, COUNT(*) as count")
	if startDate != "" && endDate != "" {
		query = query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	}
	query.Group("status").Find(&statusStats)
	result["status_stats"] = statusStats

	// 各类型统计
	var typeStats []struct {
		ContractType string `json:"contract_type"`
		Count        int64  `json:"count"`
	}
	query = r.db.Model(&model.Contract{}).Select("contract_type, COUNT(*) as count")
	if startDate != "" && endDate != "" {
		query = query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	}
	query.Group("contract_type").Find(&typeStats)
	result["type_stats"] = typeStats

	// 总合同金额
	var totalAmount float64
	query = r.db.Model(&model.Contract{}).Select("COALESCE(SUM(total_amount), 0)")
	if startDate != "" && endDate != "" {
		query = query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	}
	query.Scan(&totalAmount)
	result["total_amount"] = totalAmount

	return result, nil
}

// CreatePaymentSchedule 创建付款计划
func (r *ContractRepository) CreatePaymentSchedule(schedule *model.ContractPaymentSchedule) error {
	return r.db.Create(schedule).Error
}

// GetPaymentSchedulesByContractID 根据合同ID获取付款计划
func (r *ContractRepository) GetPaymentSchedulesByContractID(contractID string) ([]model.ContractPaymentSchedule, error) {
	var schedules []model.ContractPaymentSchedule
	err := r.db.Where("contract_id = ?", contractID).Order("due_date ASC").Find(&schedules).Error
	return schedules, err
}

// UpdatePaymentSchedule 更新付款计划
func (r *ContractRepository) UpdatePaymentSchedule(schedule *model.ContractPaymentSchedule) error {
	return r.db.Save(schedule).Error
}

// DeletePaymentSchedule 删除付款计划
func (r *ContractRepository) DeletePaymentSchedule(id string) error {
	return r.db.Where("id = ?", id).Delete(&model.ContractPaymentSchedule{}).Error
}

// GetPendingPaymentSchedules 获取待付款的计划
func (r *ContractRepository) GetPendingPaymentSchedules() ([]model.ContractPaymentSchedule, error) {
	var schedules []model.ContractPaymentSchedule
	err := r.db.Preload("Contract").Preload("Contract.Counterparty").
		Where("status = ? AND due_date <= CURRENT_DATE + INTERVAL 7 DAY", "PENDING").
		Order("due_date ASC").Find(&schedules).Error
	return schedules, err
}

// BatchUpdatePaymentSchedules 批量更新付款计划
func (r *ContractRepository) BatchUpdatePaymentSchedules(schedules []model.ContractPaymentSchedule) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		for _, schedule := range schedules {
			if err := tx.Save(&schedule).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
