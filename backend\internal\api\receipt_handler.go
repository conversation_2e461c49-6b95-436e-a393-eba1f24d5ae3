package api

import (
	"hospital-management/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ReceiptHandler struct {
	receiptService *service.ReceiptService
}

func NewReceiptHandler(receiptService *service.ReceiptService) *ReceiptHandler {
	return &ReceiptHandler{receiptService: receiptService}
}

// CreateReceipt 创建入库单
func (h *ReceiptHandler) CreateReceipt(c *gin.Context) {
	var req service.CreateReceiptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权访问",
		})
		return
	}

	receipt, err := h.receiptService.CreateReceipt(userID.(string), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "入库单创建成功",
		"data":    receipt,
	})
}

// GetReceipts 获取入库单列表
func (h *ReceiptHandler) GetReceipts(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	status := c.Query("status")
	supplierID := c.Query("supplier_id")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	receipts, total, err := h.receiptService.GetReceipts(page, pageSize, status, supplierID, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取入库单列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":      receipts,
			"total":      total,
			"page":       page,
			"page_size":  pageSize,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetReceiptByID 根据ID获取入库单详情
func (h *ReceiptHandler) GetReceiptByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "入库单ID不能为空",
		})
		return
	}

	receipt, err := h.receiptService.GetReceiptByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "入库单不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    receipt,
	})
}

// InspectReceipt 质检入库单
func (h *ReceiptHandler) InspectReceipt(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "入库单ID不能为空",
		})
		return
	}

	var req service.InspectReceiptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := h.receiptService.InspectReceipt(id, &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "质检完成",
	})
}

// GetPendingInspectionReceipts 获取待质检的入库单
func (h *ReceiptHandler) GetPendingInspectionReceipts(c *gin.Context) {
	inspectorID := c.Query("inspector_id")

	receipts, err := h.receiptService.GetPendingInspectionReceipts(inspectorID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取待质检入库单失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    receipts,
	})
}

// GetReceiptStatistics 获取入库统计数据
func (h *ReceiptHandler) GetReceiptStatistics(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	stats, err := h.receiptService.GetReceiptStatistics(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取统计数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// UpdateReceiptStatus 更新入库单状态
func (h *ReceiptHandler) UpdateReceiptStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "入库单ID不能为空",
		})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// TODO: 实现状态更新逻辑
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "状态更新成功",
	})
}

// ExportReceipt 导出入库单
func (h *ReceiptHandler) ExportReceipt(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "入库单ID不能为空",
		})
		return
	}

	// TODO: 实现导出功能
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "导出功能开发中",
	})
}

// PrintReceipt 打印入库单
func (h *ReceiptHandler) PrintReceipt(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "入库单ID不能为空",
		})
		return
	}

	// TODO: 实现打印功能
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "打印功能开发中",
	})
}
