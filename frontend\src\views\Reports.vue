<template>
  <div class="reports">
    <h1>报告中心</h1>
    <p>各类报告生成和管理</p>
    
    <el-card>
      <template #header>
        <span>报告管理</span>
      </template>
      <div class="placeholder">
        报告中心功能待实现
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('Reports mounted')
})
</script>

<style scoped>
.reports {
  padding: 20px;
}

.placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  border-radius: 4px;
}
</style>
