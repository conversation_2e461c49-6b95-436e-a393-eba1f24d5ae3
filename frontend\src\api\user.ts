import api from './index'
import type { User, ApiResponse, PaginationParams, PaginationResponse } from '@/types'

// 获取用户列表
export const getUserList = (params: PaginationParams & { keyword?: string }) => {
  return api.get<ApiResponse<PaginationResponse<User>>>('/users', { params })
}

// 根据ID获取用户详情
export const getUserById = (id: string) => {
  return api.get<ApiResponse<User>>(`/users/${id}`)
}

// 创建用户
export const createUser = (data: Partial<User> & { password: string }) => {
  return api.post<ApiResponse<User>>('/users', data)
}

// 更新用户
export const updateUser = (id: string, data: Partial<User>) => {
  return api.put<ApiResponse<User>>(`/users/${id}`, data)
}

// 删除用户
export const deleteUser = (id: string) => {
  return api.delete<ApiResponse<null>>(`/users/${id}`)
}

// 分配用户角色
export const assignUserRoles = (userId: string, roleIds: string[]) => {
  return api.post<ApiResponse<null>>(`/users/${userId}/roles`, { role_ids: roleIds })
}

// 获取用户的角色列表
export const getUserRoles = (userId: string) => {
  return api.get<ApiResponse<any[]>>(`/users/${userId}/roles`)
}

// 重置用户密码
export const resetUserPassword = (userId: string, newPassword: string) => {
  return api.post<ApiResponse<null>>(`/users/${userId}/reset-password`, { password: newPassword })
}
