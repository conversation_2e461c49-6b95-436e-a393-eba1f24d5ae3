package service

import (
	"fmt"
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
	"time"

	"github.com/google/uuid"
)

type ContractService struct {
	contractRepo *repository.ContractRepository
	supplierRepo *repository.SupplierRepository
}

func NewContractService(contractRepo *repository.ContractRepository, supplierRepo *repository.SupplierRepository) *ContractService {
	return &ContractService{
		contractRepo: contractRepo,
		supplierRepo: supplierRepo,
	}
}

// CreateContractRequest 创建合同请求
type CreateContractRequest struct {
	ContractName     string                    `json:"contract_name" binding:"required"`
	ContractType     string                    `json:"contract_type" binding:"required"`
	CounterpartyID   string                    `json:"counterparty_id"`
	CounterpartyName string                    `json:"counterparty_name" binding:"required"`
	TotalAmount      float64                   `json:"total_amount" binding:"required"`
	StartDate        string                    `json:"start_date"`
	EndDate          string                    `json:"end_date"`
	PaymentSchedules []PaymentScheduleRequest  `json:"payment_schedules"`
	Remark           string                    `json:"remark"`
}

type PaymentScheduleRequest struct {
	PhaseName string  `json:"phase_name" binding:"required"`
	DueDate   string  `json:"due_date" binding:"required"`
	Amount    float64 `json:"amount" binding:"required"`
}

// CreateContract 创建合同
func (s *ContractService) CreateContract(userID string, req *CreateContractRequest) (*model.Contract, error) {
	// 解析用户ID
	creatorID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("用户ID格式错误")
	}

	// 解析对方单位ID
	var counterpartyID *uuid.UUID
	if req.CounterpartyID != "" {
		id, err := uuid.Parse(req.CounterpartyID)
		if err != nil {
			return nil, fmt.Errorf("对方单位ID格式错误")
		}
		counterpartyID = &id
	}

	// 生成合同编号
	contractCode := s.generateContractCode(req.ContractType)

	// 创建合同
	contract := &model.Contract{
		ContractCode:     contractCode,
		ContractName:     req.ContractName,
		ContractType:     req.ContractType,
		CounterpartyID:   counterpartyID,
		CounterpartyName: req.CounterpartyName,
		TotalAmount:      req.TotalAmount,
		Status:           "DRAFT",
		CreatedBy:        &creatorID,
	}

	// 处理开始日期
	if req.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", req.StartDate)
		if err == nil {
			contract.StartDate = &startDate
		}
	}

	// 处理结束日期
	if req.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", req.EndDate)
		if err == nil {
			contract.EndDate = &endDate
		}
	}

	// 创建付款计划
	var paymentSchedules []model.ContractPaymentSchedule
	for _, scheduleReq := range req.PaymentSchedules {
		dueDate, err := time.Parse("2006-01-02", scheduleReq.DueDate)
		if err != nil {
			return nil, fmt.Errorf("付款日期格式错误: %v", err)
		}

		schedule := model.ContractPaymentSchedule{
			PhaseName: scheduleReq.PhaseName,
			DueDate:   &dueDate,
			Amount:    scheduleReq.Amount,
			Status:    "PENDING",
			CreatedBy: &creatorID,
		}
		paymentSchedules = append(paymentSchedules, schedule)
	}

	contract.PaymentSchedules = paymentSchedules

	// 保存到数据库
	if err := s.contractRepo.CreateContract(contract); err != nil {
		return nil, fmt.Errorf("创建合同失败: %v", err)
	}

	return contract, nil
}

// GetContractByID 根据ID获取合同
func (s *ContractService) GetContractByID(id string) (*model.Contract, error) {
	return s.contractRepo.GetContractByID(id)
}

// GetContracts 获取合同列表
func (s *ContractService) GetContracts(page, pageSize int, status, contractType, counterpartyID, startDate, endDate string) ([]model.Contract, int64, error) {
	offset := (page - 1) * pageSize
	return s.contractRepo.GetContracts(offset, pageSize, status, contractType, counterpartyID, startDate, endDate)
}

// UpdateContractRequest 更新合同请求
type UpdateContractRequest struct {
	ContractName     string                   `json:"contract_name"`
	CounterpartyName string                   `json:"counterparty_name"`
	TotalAmount      float64                  `json:"total_amount"`
	StartDate        string                   `json:"start_date"`
	EndDate          string                   `json:"end_date"`
	PaymentSchedules []PaymentScheduleRequest `json:"payment_schedules"`
	Remark           string                   `json:"remark"`
}

// UpdateContract 更新合同
func (s *ContractService) UpdateContract(id, userID string, req *UpdateContractRequest) error {
	// 获取现有合同
	contract, err := s.contractRepo.GetContractByID(id)
	if err != nil {
		return fmt.Errorf("获取合同失败: %v", err)
	}

	// 检查状态是否允许修改
	if contract.Status != "DRAFT" {
		return fmt.Errorf("合同状态不允许修改")
	}

	// 解析用户ID
	updaterID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("用户ID格式错误")
	}

	// 更新合同信息
	if req.ContractName != "" {
		contract.ContractName = req.ContractName
	}
	if req.CounterpartyName != "" {
		contract.CounterpartyName = req.CounterpartyName
	}
	if req.TotalAmount > 0 {
		contract.TotalAmount = req.TotalAmount
	}

	// 处理日期
	if req.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", req.StartDate)
		if err == nil {
			contract.StartDate = &startDate
		}
	}
	if req.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", req.EndDate)
		if err == nil {
			contract.EndDate = &endDate
		}
	}

	contract.UpdatedBy = &updaterID

	// 更新付款计划
	if len(req.PaymentSchedules) > 0 {
		// 删除现有付款计划
		for _, schedule := range contract.PaymentSchedules {
			s.contractRepo.DeletePaymentSchedule(schedule.ID.String())
		}

		// 创建新的付款计划
		var newSchedules []model.ContractPaymentSchedule
		for _, scheduleReq := range req.PaymentSchedules {
			dueDate, err := time.Parse("2006-01-02", scheduleReq.DueDate)
			if err != nil {
				return fmt.Errorf("付款日期格式错误: %v", err)
			}

			schedule := model.ContractPaymentSchedule{
				ContractID: contract.ID,
				PhaseName:  scheduleReq.PhaseName,
				DueDate:    &dueDate,
				Amount:     scheduleReq.Amount,
				Status:     "PENDING",
				CreatedBy:  &updaterID,
			}
			
			if err := s.contractRepo.CreatePaymentSchedule(&schedule); err != nil {
				return fmt.Errorf("创建付款计划失败: %v", err)
			}
			newSchedules = append(newSchedules, schedule)
		}
		contract.PaymentSchedules = newSchedules
	}

	// 保存合同
	if err := s.contractRepo.UpdateContract(contract); err != nil {
		return fmt.Errorf("更新合同失败: %v", err)
	}

	return nil
}

// SubmitContract 提交合同审批
func (s *ContractService) SubmitContract(id, userID string) error {
	contract, err := s.contractRepo.GetContractByID(id)
	if err != nil {
		return fmt.Errorf("获取合同失败: %v", err)
	}

	if contract.Status != "DRAFT" {
		return fmt.Errorf("合同状态不允许提交")
	}

	// 更新状态为待审批
	if err := s.contractRepo.UpdateContractStatus(id, "PENDING"); err != nil {
		return fmt.Errorf("更新合同状态失败: %v", err)
	}

	// TODO: 启动审批流程

	return nil
}

// ApproveContract 审批通过合同
func (s *ContractService) ApproveContract(id, userID string) error {
	contract, err := s.contractRepo.GetContractByID(id)
	if err != nil {
		return fmt.Errorf("获取合同失败: %v", err)
	}

	if contract.Status != "PENDING" {
		return fmt.Errorf("合同状态不允许审批")
	}

	// 更新状态为生效
	if err := s.contractRepo.UpdateContractStatus(id, "ACTIVE"); err != nil {
		return fmt.Errorf("更新合同状态失败: %v", err)
	}

	return nil
}

// GetExpiringContracts 获取即将到期的合同
func (s *ContractService) GetExpiringContracts(days int) ([]model.Contract, error) {
	return s.contractRepo.GetExpiringContracts(days)
}

// GetContractStatistics 获取合同统计数据
func (s *ContractService) GetContractStatistics(startDate, endDate string) (map[string]interface{}, error) {
	return s.contractRepo.GetContractStatistics(startDate, endDate)
}

// GetPendingPaymentSchedules 获取待付款计划
func (s *ContractService) GetPendingPaymentSchedules() ([]model.ContractPaymentSchedule, error) {
	return s.contractRepo.GetPendingPaymentSchedules()
}

// generateContractCode 生成合同编号
func (s *ContractService) generateContractCode(contractType string) string {
	now := time.Now()
	prefix := "HT"
	switch contractType {
	case "采购":
		prefix = "CG"
	case "服务":
		prefix = "FW"
	case "基建":
		prefix = "JJ"
	}
	return fmt.Sprintf("%s%s%04d", prefix, now.Format("20060102"), now.Unix()%10000)
}
