package service

import (
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
)

type PerformanceService struct {
	performanceRepo *repository.PerformanceIndicatorRepository
}

func NewPerformanceService(performanceRepo *repository.PerformanceIndicatorRepository) *PerformanceService {
	return &PerformanceService{performanceRepo: performanceRepo}
}

func (s *PerformanceService) CreateIndicator(indicator *model.PerformanceIndicator) error {
	return s.performanceRepo.Create(indicator)
}

func (s *PerformanceService) GetIndicatorByID(id uint) (*model.PerformanceIndicator, error) {
	return s.performanceRepo.GetByID(id)
}

func (s *PerformanceService) UpdateIndicator(indicator *model.PerformanceIndicator) error {
	return s.performanceRepo.Update(indicator)
}

func (s *PerformanceService) DeleteIndicator(id uint) error {
	return s.performanceRepo.Delete(id)
}

func (s *PerformanceService) ListIndicators(page, pageSize int) ([]model.PerformanceIndicator, int64, error) {
	offset := (page - 1) * pageSize
	return s.performanceRepo.List(offset, pageSize)
}
