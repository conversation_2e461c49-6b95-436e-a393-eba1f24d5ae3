<template>
  <div class="compliance">
    <h1>合规管理</h1>
    <p>合规检查和监督管理</p>
    
    <el-card>
      <template #header>
        <span>合规检查管理</span>
      </template>
      <div class="placeholder">
        合规管理功能待实现
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('Compliance mounted')
})
</script>

<style scoped>
.compliance {
  padding: 20px;
}

.placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  border-radius: 4px;
}
</style>
