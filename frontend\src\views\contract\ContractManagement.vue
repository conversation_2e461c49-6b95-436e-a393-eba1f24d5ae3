<template>
  <div class="contract-management">
    <a-card>
      <template #title>
        <span>合同管理</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            新建合同
          </a-button>
          <a-button @click="loadStatistics">
            <template #icon><BarChartOutlined /></template>
            统计分析
          </a-button>
          <a-button @click="loadExpiringContracts">
            <template #icon><ClockCircleOutlined /></template>
            到期提醒
          </a-button>
        </a-space>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-form">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-select
              v-model:value="searchForm.status"
              placeholder="选择状态"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="DRAFT">草稿</a-select-option>
              <a-select-option value="PENDING">待审批</a-select-option>
              <a-select-option value="ACTIVE">生效中</a-select-option>
              <a-select-option value="COMPLETED">已完成</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="searchForm.contract_type"
              placeholder="合同类型"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="采购">采购合同</a-select-option>
              <a-select-option value="服务">服务合同</a-select-option>
              <a-select-option value="基建">基建合同</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.counterparty_id"
              placeholder="选择对方单位"
              allow-clear
              show-search
              @change="handleSearch"
            >
              <a-select-option
                v-for="supplier in supplierOptions"
                :key="supplier.id"
                :value="supplier.id"
              >
                {{ supplier.name }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-range-picker
              v-model:value="dateRange"
              @change="handleDateChange"
            />
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'contract_type'">
            <a-tag color="blue">{{ record.contract_type }}</a-tag>
          </template>
          <template v-else-if="column.key === 'total_amount'">
            <span class="amount">¥{{ record.total_amount?.toFixed(2) }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetails(record)">
                详情
              </a-button>
              <a-button
                v-if="record.status === 'DRAFT'"
                type="link"
                size="small"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>
              <a-button
                v-if="record.status === 'DRAFT'"
                type="link"
                size="small"
                @click="submitContract(record.id)"
              >
                提交审批
              </a-button>
              <a-button
                v-if="record.status === 'PENDING'"
                type="link"
                size="small"
                @click="approveContract(record.id)"
              >
                审批
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="printContract(record)">
                      <PrinterOutlined /> 打印
                    </a-menu-item>
                    <a-menu-item @click="exportContract(record)">
                      <ExportOutlined /> 导出
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新建/编辑合同弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑合同' : '新建合同'"
      width="1000px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="合同名称" name="contract_name">
              <a-input v-model:value="formData.contract_name" placeholder="请输入合同名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合同类型" name="contract_type">
              <a-select v-model:value="formData.contract_type" placeholder="选择合同类型">
                <a-select-option value="采购">采购合同</a-select-option>
                <a-select-option value="服务">服务合同</a-select-option>
                <a-select-option value="基建">基建合同</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="对方单位" name="counterparty_name">
              <a-input v-model:value="formData.counterparty_name" placeholder="请输入对方单位名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合同金额" name="total_amount">
              <a-input-number
                v-model:value="formData.total_amount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入合同金额"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="合同开始日期">
              <a-date-picker
                v-model:value="formData.start_date"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合同结束日期">
              <a-date-picker
                v-model:value="formData.end_date"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 付款计划 -->
        <a-form-item label="付款计划">
          <div class="payment-schedules">
            <div class="schedule-header">
              <a-button type="dashed" @click="addPaymentSchedule">
                <template #icon><PlusOutlined /></template>
                添加付款计划
              </a-button>
            </div>
            <a-table
              :columns="scheduleColumns"
              :data-source="formData.payment_schedules"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'phase_name'">
                  <a-input v-model:value="record.phase_name" />
                </template>
                <template v-else-if="column.key === 'due_date'">
                  <a-date-picker v-model:value="record.due_date" />
                </template>
                <template v-else-if="column.key === 'amount'">
                  <a-input-number
                    v-model:value="record.amount"
                    :min="0"
                    :precision="2"
                    @change="calculateScheduleTotal"
                  />
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-button
                    type="link"
                    size="small"
                    danger
                    @click="removePaymentSchedule(index)"
                  >
                    删除
                  </a-button>
                </template>
              </template>
            </a-table>
            <div class="schedule-total">
              <span>付款计划总额: ¥{{ scheduleTotal.toFixed(2) }}</span>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="合同详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="currentContract">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="合同编号">
            {{ currentContract.contract_code }}
          </a-descriptions-item>
          <a-descriptions-item label="合同名称">
            {{ currentContract.contract_name }}
          </a-descriptions-item>
          <a-descriptions-item label="合同类型">
            <a-tag color="blue">{{ currentContract.contract_type }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentContract.status)">
              {{ getStatusText(currentContract.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="对方单位">
            {{ currentContract.counterparty_name }}
          </a-descriptions-item>
          <a-descriptions-item label="合同金额">
            <span class="amount">¥{{ currentContract.total_amount?.toFixed(2) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="开始日期">
            {{ currentContract.start_date || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="结束日期">
            {{ currentContract.end_date || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>付款计划</a-divider>
        <a-table
          :columns="detailScheduleColumns"
          :data-source="currentContract.payment_schedules"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'PAID' ? 'green' : 'orange'">
                {{ record.status === 'PAID' ? '已付款' : '待付款' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'amount'">
              <span class="amount">¥{{ record.amount?.toFixed(2) }}</span>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  BarChartOutlined,
  ClockCircleOutlined,
  PrinterOutlined,
  ExportOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getContractList,
  getContractById,
  createContract,
  updateContract,
  submitContract as submitContractApi,
  approveContract as approveContractApi,
  getExpiringContracts,
  getContractStatistics,
  printContract as printContractApi,
  exportContract as exportContractApi,
  type Contract,
  type CreateContractRequest,
  type UpdateContractRequest
} from '@/api/contract'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref<Contract[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const isEdit = ref(false)
const currentContract = ref<Contract | null>(null)

// 搜索表单
const searchForm = reactive({
  status: '',
  contract_type: '',
  counterparty_id: '',
  start_date: '',
  end_date: ''
})

const dateRange = ref<[Dayjs, Dayjs] | null>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '合同编号',
    dataIndex: 'contract_code',
    key: 'contract_code',
    width: 150
  },
  {
    title: '合同名称',
    dataIndex: 'contract_name',
    key: 'contract_name',
    width: 200
  },
  {
    title: '合同类型',
    dataIndex: 'contract_type',
    key: 'contract_type',
    width: 100
  },
  {
    title: '对方单位',
    dataIndex: 'counterparty_name',
    key: 'counterparty_name',
    width: 200
  },
  {
    title: '合同金额',
    dataIndex: 'total_amount',
    key: 'total_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '开始日期',
    dataIndex: 'start_date',
    key: 'start_date',
    width: 120
  },
  {
    title: '结束日期',
    dataIndex: 'end_date',
    key: 'end_date',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right'
  }
]

// 新建/编辑表单数据
const formData = reactive<CreateContractRequest & { payment_schedules: any[] }>({
  contract_name: '',
  contract_type: '',
  counterparty_name: '',
  total_amount: 0,
  start_date: null,
  end_date: null,
  payment_schedules: [],
  remark: ''
})

// 表单验证规则
const formRules = {
  contract_name: [{ required: true, message: '请输入合同名称' }],
  contract_type: [{ required: true, message: '请选择合同类型' }],
  counterparty_name: [{ required: true, message: '请输入对方单位名称' }],
  total_amount: [{ required: true, message: '请输入合同金额' }]
}

// 选项数据
const supplierOptions = ref<any[]>([])

// 表单引用
const formRef = ref()

// 付款计划列配置
const scheduleColumns: TableColumnsType = [
  { title: '付款阶段', dataIndex: 'phase_name', key: 'phase_name' },
  { title: '预计付款日期', dataIndex: 'due_date', key: 'due_date', width: 150 },
  { title: '付款金额', dataIndex: 'amount', key: 'amount', width: 150 },
  { title: '操作', key: 'action', width: 80 }
]

// 详情付款计划列配置
const detailScheduleColumns: TableColumnsType = [
  { title: '付款阶段', dataIndex: 'phase_name', key: 'phase_name' },
  { title: '预计付款日期', dataIndex: 'due_date', key: 'due_date', width: 150 },
  { title: '付款金额', dataIndex: 'amount', key: 'amount', width: 150 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 }
]

// 计算付款计划总额
const scheduleTotal = computed(() => {
  return formData.payment_schedules.reduce((sum, item) => sum + (item.amount || 0), 0)
})

// 方法定义
const loadContracts = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }

    const response = await getContractList(params)
    if (response.data.success) {
      tableData.value = response.data.data.items
      pagination.total = response.data.data.total
    }
  } catch (error) {
    message.error('加载合同列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadContracts()
}

const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    searchForm.start_date = dates[0].format('YYYY-MM-DD')
    searchForm.end_date = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.start_date = ''
    searchForm.end_date = ''
  }
  handleSearch()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadContracts()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  // 重置表单数据
  Object.assign(formData, {
    contract_name: '',
    contract_type: '',
    counterparty_name: '',
    total_amount: 0,
    start_date: null,
    end_date: null,
    payment_schedules: [],
    remark: ''
  })
}

const showEditModal = (record: Contract) => {
  isEdit.value = true
  modalVisible.value = true
  currentContract.value = record

  // 填充表单数据
  Object.assign(formData, {
    contract_name: record.contract_name,
    contract_type: record.contract_type,
    counterparty_name: record.counterparty_name,
    total_amount: record.total_amount,
    start_date: record.start_date ? dayjs(record.start_date) : null,
    end_date: record.end_date ? dayjs(record.end_date) : null,
    payment_schedules: record.payment_schedules.map(schedule => ({
      phase_name: schedule.phase_name,
      due_date: schedule.due_date ? dayjs(schedule.due_date) : null,
      amount: schedule.amount
    })),
    remark: ''
  })
}

const addPaymentSchedule = () => {
  formData.payment_schedules.push({
    phase_name: '',
    due_date: null,
    amount: 0
  })
}

const removePaymentSchedule = (index: number) => {
  formData.payment_schedules.splice(index, 1)
}

const calculateScheduleTotal = () => {
  // 触发计算属性更新
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    const submitData = {
      ...formData,
      start_date: formData.start_date ? dayjs(formData.start_date).format('YYYY-MM-DD') : undefined,
      end_date: formData.end_date ? dayjs(formData.end_date).format('YYYY-MM-DD') : undefined,
      payment_schedules: formData.payment_schedules.map(schedule => ({
        phase_name: schedule.phase_name,
        due_date: dayjs(schedule.due_date).format('YYYY-MM-DD'),
        amount: schedule.amount
      }))
    }

    if (isEdit.value) {
      const response = await updateContract(currentContract.value!.id, submitData)
      if (response.data.success) {
        message.success('合同更新成功')
      }
    } else {
      const response = await createContract(submitData)
      if (response.data.success) {
        message.success('合同创建成功')
      }
    }

    modalVisible.value = false
    loadContracts()
  } catch (error) {
    console.error('提交合同失败:', error)
    message.error(isEdit.value ? '更新合同失败' : '创建合同失败')
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const viewDetails = async (record: Contract) => {
  try {
    const response = await getContractById(record.id)
    if (response.data.success) {
      currentContract.value = response.data.data
      detailModalVisible.value = true
    }
  } catch (error) {
    message.error('获取合同详情失败')
  }
}

const submitContract = async (id: string) => {
  try {
    const response = await submitContractApi(id)
    if (response.data.success) {
      message.success('合同提交成功')
      loadContracts()
    }
  } catch (error) {
    message.error('提交合同失败')
  }
}

const approveContract = async (id: string) => {
  try {
    const response = await approveContractApi(id)
    if (response.data.success) {
      message.success('合同审批通过')
      loadContracts()
    }
  } catch (error) {
    message.error('审批合同失败')
  }
}

const loadStatistics = async () => {
  try {
    const response = await getContractStatistics(searchForm.start_date, searchForm.end_date)
    if (response.data.success) {
      // TODO: 显示统计数据
      message.info('统计功能开发中')
    }
  } catch (error) {
    message.error('获取统计数据失败')
  }
}

const loadExpiringContracts = async () => {
  try {
    const response = await getExpiringContracts(30)
    if (response.data.success) {
      // TODO: 显示即将到期的合同
      message.info(`即将到期的合同: ${response.data.data.length} 个`)
    }
  } catch (error) {
    message.error('获取到期合同失败')
  }
}

const printContract = async (record: Contract) => {
  try {
    const response = await printContractApi(record.id)
    // TODO: 处理打印
    message.info('打印功能开发中')
  } catch (error) {
    message.error('打印失败')
  }
}

const exportContract = async (record: Contract) => {
  try {
    const response = await exportContractApi(record.id)
    // TODO: 处理导出
    message.info('导出功能开发中')
  } catch (error) {
    message.error('导出失败')
  }
}

// 工具方法
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'DRAFT': 'default',
    'PENDING': 'orange',
    'ACTIVE': 'green',
    'COMPLETED': 'blue'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'DRAFT': '草稿',
    'PENDING': '待审批',
    'ACTIVE': '生效中',
    'COMPLETED': '已完成'
  }
  return textMap[status] || status
}

// 加载基础数据
const loadSuppliers = async () => {
  try {
    // TODO: 调用供应商API
    supplierOptions.value = [
      { id: '1', name: '供应商A', credit_code: '123456789' },
      { id: '2', name: '供应商B', credit_code: '987654321' }
    ]
  } catch (error) {
    message.error('加载供应商数据失败')
  }
}

onMounted(() => {
  loadContracts()
  loadSuppliers()
})
</script>

<style scoped>
.contract-management {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount {
  font-weight: 500;
  color: #1890ff;
}

.payment-schedules {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.schedule-header {
  margin-bottom: 16px;
}

.schedule-total {
  margin-top: 16px;
  text-align: right;
  font-weight: 500;
  color: #1890ff;
}
</style>
