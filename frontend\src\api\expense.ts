import api from './index'
import type { 
  PreApplication, 
  ExpenseApplication, 
  Payment, 
  ApiResponse, 
  PaginationParams, 
  PaginationResponse 
} from '@/types'

// 事前申请
export const getPreApplicationList = (params: PaginationParams & { 
  type?: string; 
  status?: string 
}) => {
  return api.get<ApiResponse<PaginationResponse<PreApplication>>>('/pre-applications', { params })
}

export const getPreApplicationById = (id: string) => {
  return api.get<ApiResponse<PreApplication>>(`/pre-applications/${id}`)
}

export const createPreApplication = (data: Partial<PreApplication>) => {
  return api.post<ApiResponse<PreApplication>>('/pre-applications', data)
}

export const updatePreApplication = (id: string, data: Partial<PreApplication>) => {
  return api.put<ApiResponse<PreApplication>>(`/pre-applications/${id}`, data)
}

export const cancelPreApplication = (id: string) => {
  return api.post<ApiResponse<null>>(`/pre-applications/${id}/cancel`)
}

export const getApprovedPreApplications = () => {
  return api.get<ApiResponse<PreApplication[]>>('/pre-applications/approved')
}

// 报销申请
export const getExpenseApplicationList = (params: PaginationParams & { 
  status?: string; 
  startDate?: string; 
  endDate?: string 
}) => {
  return api.get<ApiResponse<PaginationResponse<ExpenseApplication>>>('/expense-applications', { params })
}

export const getExpenseApplicationById = (id: string) => {
  return api.get<ApiResponse<ExpenseApplication>>(`/expense-applications/${id}`)
}

export const createExpenseApplication = (data: Partial<ExpenseApplication>) => {
  return api.post<ApiResponse<ExpenseApplication>>('/expense-applications', data)
}

export const updateExpenseApplication = (id: string, data: Partial<ExpenseApplication>) => {
  return api.put<ApiResponse<ExpenseApplication>>(`/expense-applications/${id}`, data)
}

export const deleteExpenseApplication = (id: string) => {
  return api.delete<ApiResponse<null>>(`/expense-applications/${id}`)
}

export const submitExpenseApplication = (id: string) => {
  return api.post<ApiResponse<null>>(`/expense-applications/${id}/submit`)
}

export const copyExpenseApplication = (id: string) => {
  return api.post<ApiResponse<ExpenseApplication>>(`/expense-applications/${id}/copy`)
}

export const printExpenseApplication = (id: string) => {
  return api.get(`/expense-applications/${id}/print`, { responseType: 'blob' })
}

// 付款管理
export const getPaymentList = (params: PaginationParams & { 
  sourceType?: string; 
  status?: string; 
  startDate?: string; 
  endDate?: string 
}) => {
  return api.get<ApiResponse<PaginationResponse<Payment>>>('/payments', { params })
}

export const getPaymentById = (id: string) => {
  return api.get<ApiResponse<Payment>>(`/payments/${id}`)
}

export const updatePayment = (id: string, data: Partial<Payment>) => {
  return api.put<ApiResponse<Payment>>(`/payments/${id}`, data)
}

export const processPayment = (id: string, data: {
  payment_method: string;
  payment_account: string;
  scheduled_date: string;
  remark?: string;
}) => {
  return api.post<ApiResponse<null>>(`/payments/${id}/process`, data)
}

export const batchProcessPayment = (ids: string[], data: {
  payment_method: string;
  payment_account: string;
  scheduled_date: string;
  remark?: string;
}) => {
  return api.post<ApiResponse<null>>('/payments/batch-process', { ids, ...data })
}

export const cancelPayment = (id: string) => {
  return api.post<ApiResponse<null>>(`/payments/${id}/cancel`)
}

export const exportPaymentVoucher = (id: string) => {
  return api.get(`/payments/${id}/voucher`, { responseType: 'blob' })
}

export const getPaymentHistory = (id: string) => {
  return api.get<ApiResponse<any[]>>(`/payments/${id}/history`)
}

// 文件上传
export const uploadInvoice = (file: File, expenseDetailId: string) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('expense_detail_id', expenseDetailId)
  
  return api.post<ApiResponse<{ url: string }>>('/upload/invoice', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export const uploadAttachment = (file: File, businessType: string, businessId: string) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('business_type', businessType)
  formData.append('business_id', businessId)
  
  return api.post<ApiResponse<{ url: string }>>('/upload/attachment', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
