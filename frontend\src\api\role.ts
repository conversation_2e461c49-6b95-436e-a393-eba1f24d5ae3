import api from './index'
import type { Role, ApiResponse } from '@/types'

// 获取角色列表
export const getRoleList = () => {
  return api.get<ApiResponse<Role[]>>('/roles')
}

// 根据ID获取角色详情
export const getRoleById = (id: string) => {
  return api.get<ApiResponse<Role>>(`/roles/${id}`)
}

// 创建角色
export const createRole = (data: Partial<Role>) => {
  return api.post<ApiResponse<Role>>('/roles', data)
}

// 更新角色
export const updateRole = (id: string, data: Partial<Role>) => {
  return api.put<ApiResponse<Role>>(`/roles/${id}`, data)
}

// 删除角色
export const deleteRole = (id: string) => {
  return api.delete<ApiResponse<null>>(`/roles/${id}`)
}

// 分配角色权限
export const assignRolePermissions = (roleId: string, permissions: string[]) => {
  return api.post<ApiResponse<null>>(`/roles/${roleId}/permissions`, { permissions })
}

// 获取所有权限列表
export const getPermissionList = () => {
  return api.get<ApiResponse<any[]>>('/permissions')
}
