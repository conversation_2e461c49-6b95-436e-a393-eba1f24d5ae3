<template>
  <div class="performance">
    <h1>绩效管理</h1>
    <p>绩效指标管理和评估</p>
    
    <el-card>
      <template #header>
        <span>绩效指标管理</span>
      </template>
      <div class="placeholder">
        绩效管理功能待实现
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('Performance mounted')
})
</script>

<style scoped>
.performance {
  padding: 20px;
}

.placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  border-radius: 4px;
}
</style>
