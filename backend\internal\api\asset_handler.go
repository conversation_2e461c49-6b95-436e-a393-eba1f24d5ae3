package api

import (
	"hospital-management/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AssetHandler struct {
	assetService *service.AssetService
}

func NewAssetHandler(assetService *service.AssetService) *AssetHandler {
	return &AssetHandler{assetService: assetService}
}

// CreateAsset 创建资产
func (h *AssetHandler) CreateAsset(c *gin.Context) {
	var req service.CreateAssetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权访问",
		})
		return
	}

	asset, err := h.assetService.CreateAsset(userID.(string), &req)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "资产创建成功",
		"data":    asset,
	})
}

// GetAssets 获取资产列表
func (h *AssetHandler) GetAssets(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	status := c.Query("status")
	categoryID := c.Query("category_id")
	departmentID := c.Query("department_id")
	responsibleID := c.Query("responsible_id")

	assets, total, err := h.assetService.GetAssets(page, pageSize, status, categoryID, departmentID, responsibleID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取资产列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":      assets,
			"total":      total,
			"page":       page,
			"page_size":  pageSize,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetAssetByID 根据ID获取资产详情
func (h *AssetHandler) GetAssetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "资产ID不能为空",
		})
		return
	}

	asset, err := h.assetService.GetAssetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "资产不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    asset,
	})
}

// CreateAssetMovement 创建资产变动
func (h *AssetHandler) CreateAssetMovement(c *gin.Context) {
	var req service.AssetMovementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权访问",
		})
		return
	}

	if err := h.assetService.CreateAssetMovement(userID.(string), &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "资产变动申请提交成功",
	})
}

// CreateAssetMaintenance 创建资产维保记录
func (h *AssetHandler) CreateAssetMaintenance(c *gin.Context) {
	var req service.AssetMaintenanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权访问",
		})
		return
	}

	if err := h.assetService.CreateAssetMaintenance(userID.(string), &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "维保记录创建成功",
	})
}

// GetAssetStatistics 获取资产统计数据
func (h *AssetHandler) GetAssetStatistics(c *gin.Context) {
	stats, err := h.assetService.GetAssetStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取统计数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": stats,
	})
}

// GetUpcomingMaintenances 获取即将到期的维保
func (h *AssetHandler) GetUpcomingMaintenances(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))

	maintenances, err := h.assetService.GetUpcomingMaintenances(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取即将到期维保失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": maintenances,
	})
}

// GetExpiringWarranties 获取即将到期的保修
func (h *AssetHandler) GetExpiringWarranties(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))

	assets, err := h.assetService.GetExpiringWarranties(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取即将到期保修失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": assets,
	})
}

// GenerateAssetQRCode 生成资产二维码
func (h *AssetHandler) GenerateAssetQRCode(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "资产ID不能为空",
		})
		return
	}

	// TODO: 实现二维码生成
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "二维码生成功能开发中",
	})
}

// PrintAssetLabel 打印资产标签
func (h *AssetHandler) PrintAssetLabel(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "资产ID不能为空",
		})
		return
	}

	// TODO: 实现标签打印
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "标签打印功能开发中",
	})
}

// ExportAssets 导出资产列表
func (h *AssetHandler) ExportAssets(c *gin.Context) {
	// TODO: 实现资产导出
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "导出功能开发中",
	})
}

// ImportAssets 导入资产数据
func (h *AssetHandler) ImportAssets(c *gin.Context) {
	// TODO: 实现资产导入
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "导入功能开发中",
	})
}
