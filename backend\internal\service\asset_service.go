package service

import (
	"fmt"
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
	"time"

	"github.com/google/uuid"
)

type AssetService struct {
	assetRepo *repository.AssetRepository
}

func NewAssetService(assetRepo *repository.AssetRepository) *AssetService {
	return &AssetService{
		assetRepo: assetRepo,
	}
}

// CreateAssetRequest 创建资产请求
type CreateAssetRequest struct {
	AssetName        string  `json:"asset_name" binding:"required"`
	CategoryID       string  `json:"category_id" binding:"required"`
	Specification    string  `json:"specification"`
	Brand            string  `json:"brand"`
	Model            string  `json:"model"`
	SerialNumber     string  `json:"serial_number"`
	PurchaseDate     string  `json:"purchase_date"`
	PurchasePrice    float64 `json:"purchase_price"`
	SupplierID       string  `json:"supplier_id"`
	ContractID       string  `json:"contract_id"`
	ReceiptID        string  `json:"receipt_id"`
	DepartmentID     string  `json:"department_id" binding:"required"`
	LocationID       string  `json:"location_id"`
	ResponsibleID    string  `json:"responsible_id"`
	DepreciationRate float64 `json:"depreciation_rate"`
	UsefulLife       int     `json:"useful_life"`
	WarrantyExpiry   string  `json:"warranty_expiry"`
	Remark           string  `json:"remark"`
}

// CreateAsset 创建资产
func (s *AssetService) CreateAsset(userID string, req *CreateAssetRequest) (*model.Asset, error) {
	// 解析用户ID
	creatorID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("用户ID格式错误")
	}

	// 解析分类ID
	categoryID, err := uuid.Parse(req.CategoryID)
	if err != nil {
		return nil, fmt.Errorf("分类ID格式错误")
	}

	// 解析部门ID
	departmentID, err := uuid.Parse(req.DepartmentID)
	if err != nil {
		return nil, fmt.Errorf("部门ID格式错误")
	}

	// 生成资产编号
	assetCode := s.generateAssetCode()

	// 创建资产
	asset := &model.Asset{
		AssetCode:        assetCode,
		AssetName:        req.AssetName,
		CategoryID:       categoryID,
		Specification:    req.Specification,
		Brand:            req.Brand,
		Model:            req.Model,
		SerialNumber:     req.SerialNumber,
		PurchasePrice:    req.PurchasePrice,
		DepartmentID:     departmentID,
		Status:           model.AssetStatusNormal,
		DepreciationRate: req.DepreciationRate,
		UsefulLife:       req.UsefulLife,
		Remark:           req.Remark,
		CreatedBy:        &creatorID,
	}

	// 处理可选的UUID字段
	if req.SupplierID != "" {
		supplierID, err := uuid.Parse(req.SupplierID)
		if err == nil {
			asset.SupplierID = &supplierID
		}
	}

	if req.ContractID != "" {
		contractID, err := uuid.Parse(req.ContractID)
		if err == nil {
			asset.ContractID = &contractID
		}
	}

	if req.ReceiptID != "" {
		receiptID, err := uuid.Parse(req.ReceiptID)
		if err == nil {
			asset.ReceiptID = &receiptID
		}
	}

	if req.LocationID != "" {
		locationID, err := uuid.Parse(req.LocationID)
		if err == nil {
			asset.LocationID = &locationID
		}
	}

	if req.ResponsibleID != "" {
		responsibleID, err := uuid.Parse(req.ResponsibleID)
		if err == nil {
			asset.ResponsibleID = &responsibleID
		}
	}

	// 处理日期字段
	if req.PurchaseDate != "" {
		purchaseDate, err := time.Parse("2006-01-02", req.PurchaseDate)
		if err == nil {
			asset.PurchaseDate = &purchaseDate
		}
	}

	if req.WarrantyExpiry != "" {
		warrantyExpiry, err := time.Parse("2006-01-02", req.WarrantyExpiry)
		if err == nil {
			asset.WarrantyExpiry = &warrantyExpiry
		}
	}

	// 生成二维码和条形码
	asset.QRCode = s.generateQRCode(assetCode)
	asset.Barcode = s.generateBarcode(assetCode)

	// 保存到数据库
	if err := s.assetRepo.CreateAsset(asset); err != nil {
		return nil, fmt.Errorf("创建资产失败: %v", err)
	}

	return asset, nil
}

// GetAssetByID 根据ID获取资产
func (s *AssetService) GetAssetByID(id string) (*model.Asset, error) {
	return s.assetRepo.GetAssetByID(id)
}

// GetAssets 获取资产列表
func (s *AssetService) GetAssets(page, pageSize int, status, categoryID, departmentID, responsibleID string) ([]model.Asset, int64, error) {
	offset := (page - 1) * pageSize
	return s.assetRepo.GetAssets(offset, pageSize, status, categoryID, departmentID, responsibleID)
}

// AssetMovementRequest 资产变动请求
type AssetMovementRequest struct {
	AssetID        string `json:"asset_id" binding:"required"`
	MovementType   string `json:"movement_type" binding:"required"`
	ToUserID       string `json:"to_user_id"`
	ToDeptID       string `json:"to_dept_id"`
	ToLocationID   string `json:"to_location_id"`
	Reason         string `json:"reason" binding:"required"`
}

// CreateAssetMovement 创建资产变动
func (s *AssetService) CreateAssetMovement(userID string, req *AssetMovementRequest) error {
	// 解析用户ID
	creatorID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("用户ID格式错误")
	}

	// 解析资产ID
	assetID, err := uuid.Parse(req.AssetID)
	if err != nil {
		return fmt.Errorf("资产ID格式错误")
	}

	// 获取当前资产信息
	asset, err := s.assetRepo.GetAssetByID(req.AssetID)
	if err != nil {
		return fmt.Errorf("获取资产信息失败: %v", err)
	}

	// 创建变动记录
	movement := &model.AssetMovement{
		AssetID:      assetID,
		MovementType: req.MovementType,
		FromUserID:   asset.ResponsibleID,
		FromDeptID:   &asset.DepartmentID,
		FromLocationID: asset.LocationID,
		MovementDate: time.Now(),
		Reason:       req.Reason,
		Status:       "PENDING",
		CreatedBy:    &creatorID,
	}

	// 处理目标信息
	if req.ToUserID != "" {
		toUserID, err := uuid.Parse(req.ToUserID)
		if err == nil {
			movement.ToUserID = &toUserID
		}
	}

	if req.ToDeptID != "" {
		toDeptID, err := uuid.Parse(req.ToDeptID)
		if err == nil {
			movement.ToDeptID = &toDeptID
		}
	}

	if req.ToLocationID != "" {
		toLocationID, err := uuid.Parse(req.ToLocationID)
		if err == nil {
			movement.ToLocationID = &toLocationID
		}
	}

	// 保存变动记录
	if err := s.assetRepo.CreateAssetMovement(movement); err != nil {
		return fmt.Errorf("创建资产变动记录失败: %v", err)
	}

	// TODO: 启动审批流程

	return nil
}

// ApproveAssetMovement 审批资产变动
func (s *AssetService) ApproveAssetMovement(movementID, userID string) error {
	// TODO: 实现审批逻辑，更新资产信息
	return nil
}

// AssetMaintenanceRequest 资产维保请求
type AssetMaintenanceRequest struct {
	AssetID         string  `json:"asset_id" binding:"required"`
	MaintenanceType string  `json:"maintenance_type" binding:"required"`
	Description     string  `json:"description" binding:"required"`
	Cost            float64 `json:"cost"`
	SupplierID      string  `json:"supplier_id"`
	ResponsibleID   string  `json:"responsible_id" binding:"required"`
	NextDate        string  `json:"next_date"`
}

// CreateAssetMaintenance 创建资产维保记录
func (s *AssetService) CreateAssetMaintenance(userID string, req *AssetMaintenanceRequest) error {
	// 解析用户ID
	creatorID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("用户ID格式错误")
	}

	// 解析资产ID
	assetID, err := uuid.Parse(req.AssetID)
	if err != nil {
		return fmt.Errorf("资产ID格式错误")
	}

	// 解析负责人ID
	responsibleID, err := uuid.Parse(req.ResponsibleID)
	if err != nil {
		return fmt.Errorf("负责人ID格式错误")
	}

	// 创建维保记录
	maintenance := &model.AssetMaintenance{
		AssetID:         assetID,
		MaintenanceType: req.MaintenanceType,
		MaintenanceDate: time.Now(),
		Description:     req.Description,
		Cost:            req.Cost,
		ResponsibleID:   responsibleID,
		Status:          "COMPLETED",
		CreatedBy:       &creatorID,
	}

	// 处理供应商ID
	if req.SupplierID != "" {
		supplierID, err := uuid.Parse(req.SupplierID)
		if err == nil {
			maintenance.SupplierID = &supplierID
		}
	}

	// 处理下次维保日期
	if req.NextDate != "" {
		nextDate, err := time.Parse("2006-01-02", req.NextDate)
		if err == nil {
			maintenance.NextDate = &nextDate
		}
	}

	// 保存维保记录
	if err := s.assetRepo.CreateAssetMaintenance(maintenance); err != nil {
		return fmt.Errorf("创建维保记录失败: %v", err)
	}

	return nil
}

// GetAssetStatistics 获取资产统计数据
func (s *AssetService) GetAssetStatistics() (map[string]interface{}, error) {
	return s.assetRepo.GetAssetStatistics()
}

// GetUpcomingMaintenances 获取即将到期的维保
func (s *AssetService) GetUpcomingMaintenances(days int) ([]model.AssetMaintenance, error) {
	return s.assetRepo.GetUpcomingMaintenances(days)
}

// GetExpiringWarranties 获取即将到期的保修
func (s *AssetService) GetExpiringWarranties(days int) ([]model.Asset, error) {
	return s.assetRepo.GetExpiringWarranties(days)
}

// generateAssetCode 生成资产编号
func (s *AssetService) generateAssetCode() string {
	now := time.Now()
	return fmt.Sprintf("ZC%s%04d", now.Format("20060102"), now.Unix()%10000)
}

// generateQRCode 生成二维码
func (s *AssetService) generateQRCode(assetCode string) string {
	// TODO: 实现二维码生成逻辑
	return fmt.Sprintf("QR_%s", assetCode)
}

// generateBarcode 生成条形码
func (s *AssetService) generateBarcode(assetCode string) string {
	// TODO: 实现条形码生成逻辑
	return fmt.Sprintf("BC_%s", assetCode)
}
