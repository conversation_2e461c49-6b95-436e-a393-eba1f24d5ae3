package service

import (
	"hospital-management/internal/model"
	"hospital-management/internal/repository"
)

type QualityService struct {
	qualityRepo *repository.QualityIndicatorRepository
}

func NewQualityService(qualityRepo *repository.QualityIndicatorRepository) *QualityService {
	return &QualityService{qualityRepo: qualityRepo}
}

func (s *QualityService) CreateIndicator(indicator *model.QualityIndicator) error {
	return s.qualityRepo.Create(indicator)
}

func (s *QualityService) GetIndicatorByID(id uint) (*model.QualityIndicator, error) {
	return s.qualityRepo.GetByID(id)
}

func (s *QualityService) UpdateIndicator(indicator *model.QualityIndicator) error {
	return s.qualityRepo.Update(indicator)
}

func (s *QualityService) DeleteIndicator(id uint) error {
	return s.qualityRepo.Delete(id)
}

func (s *QualityService) ListIndicators(page, pageSize int) ([]model.QualityIndicator, int64, error) {
	offset := (page - 1) * pageSize
	return s.qualityRepo.List(offset, pageSize)
}

func (s *QualityService) CreateData(data *model.QualityData) error {
	// 计算差异和差异率
	data.Variance = data.ActualValue - data.TargetValue
	if data.TargetValue != 0 {
		data.VarianceRate = (data.Variance / data.TargetValue) * 100
	}

	// 根据差异率确定状态
	if data.VarianceRate >= -5 && data.VarianceRate <= 5 {
		data.Status = "normal"
	} else if data.VarianceRate >= -10 && data.VarianceRate <= 10 {
		data.Status = "warning"
	} else {
		data.Status = "critical"
	}

	return s.qualityRepo.CreateData(data)
}

func (s *QualityService) GetDataByIndicatorID(indicatorID uint, page, pageSize int) ([]model.QualityData, int64, error) {
	offset := (page - 1) * pageSize
	return s.qualityRepo.GetDataByIndicatorID(indicatorID, offset, pageSize)
}
