-- =============================================================================
-- 医院内部控制与运营管理（质控）系统
-- 数据库初始化脚本 for PostgreSQL (v4 - 修复模拟数据UUID)
-- 设计者: AI Software Developer
-- 版本: 1.3
-- 变更: 修复了模拟数据中硬编码无效UUID的问题，全部改用 gen_random_uuid() 函数生成。
-- =============================================================================

-- =============================================================================
-- 第一部分：创建所有表结构 (无外键约束)
-- =============================================================================

DROP TABLE IF EXISTS tbl_asset_categories, tbl_assets, tbl_contract_payment_schedules, tbl_contracts, tbl_purchase_requisitions, tbl_suppliers, tbl_payments, tbl_expense_details, tbl_expense_applications, tbl_pre_applications, tbl_budget_items, tbl_budget_subjects, tbl_budget_schemes, tbl_approval_nodes, tbl_approval_flows, tbl_files, tbl_user_roles, tbl_roles, tbl_users, tbl_departments CASCADE;

CREATE TABLE tbl_departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_id UUID,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE,
    manager_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID,
    updated_by UUID,
    CONSTRAINT uq_department_name UNIQUE (parent_id, name)
);
COMMENT ON TABLE tbl_departments IS '科室/部门表';

CREATE TABLE tbl_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    department_id UUID NOT NULL,
    user_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    email VARCHAR(255) UNIQUE,
    phone_number VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    job_title VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID,
    updated_by UUID
);
COMMENT ON TABLE tbl_users IS '员工/用户表';

CREATE TABLE tbl_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_name VARCHAR(100) NOT NULL UNIQUE,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID,
    updated_by UUID
);
COMMENT ON TABLE tbl_roles IS '角色表';

CREATE TABLE tbl_user_roles (
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    PRIMARY KEY (user_id, role_id)
);
COMMENT ON TABLE tbl_user_roles IS '用户角色关联表 (多对多)';

CREATE TABLE tbl_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    storage_type VARCHAR(20) NOT NULL DEFAULT 'LOCAL',
    business_id UUID,
    business_type VARCHAR(50),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_files IS '文件管理表';

CREATE TABLE tbl_approval_flows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_id UUID NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    status VARCHAR(30) NOT NULL,
    current_step INT DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_approval_flows IS '审批流实例表';

CREATE TABLE tbl_approval_nodes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    flow_id UUID NOT NULL,
    approver_id UUID NOT NULL,
    status VARCHAR(30) NOT NULL,
    comment TEXT,
    step INT,
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
COMMENT ON TABLE tbl_approval_nodes IS '审批节点记录表';

CREATE TABLE tbl_budget_schemes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    year INT NOT NULL,
    control_rule VARCHAR(30) NOT NULL DEFAULT 'FLEXIBLE',
    status VARCHAR(30) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_budget_schemes IS '预算方案表';

CREATE TABLE tbl_budget_subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_id UUID,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL,
    accounting_subject_code VARCHAR(50),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_budget_subjects IS '预算科目体系';

CREATE TABLE tbl_budget_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scheme_id UUID NOT NULL,
    department_id UUID NOT NULL,
    subject_id UUID NOT NULL,
    total_amount DECIMAL(18, 2) NOT NULL DEFAULT 0.00,
    used_amount DECIMAL(18, 2) NOT NULL DEFAULT 0.00,
    frozen_amount DECIMAL(18, 2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID,
    CONSTRAINT uq_budget_item UNIQUE (scheme_id, department_id, subject_id)
);
COMMENT ON TABLE tbl_budget_items IS '预算明细项';

CREATE TABLE tbl_pre_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_code VARCHAR(50) UNIQUE NOT NULL,
    applicant_id UUID NOT NULL,
    department_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    estimated_amount DECIMAL(18, 2) NOT NULL,
    status VARCHAR(30) NOT NULL,
    details JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_pre_applications IS '事前申请单 (差旅、会议等)';

CREATE TABLE tbl_expense_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_code VARCHAR(50) UNIQUE NOT NULL,
    applicant_id UUID NOT NULL,
    department_id UUID NOT NULL,
    pre_application_id UUID,
    title VARCHAR(255) NOT NULL,
    total_amount DECIMAL(18, 2) NOT NULL,
    status VARCHAR(30) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_expense_applications IS '报销申请单主表';

CREATE TABLE tbl_expense_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID NOT NULL,
    budget_item_id UUID NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(18, 2) NOT NULL,
    expense_date DATE NOT NULL,
    invoice_info JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE tbl_expense_details IS '报销明细表';

CREATE TABLE tbl_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_code VARCHAR(50) UNIQUE NOT NULL,
    source_id UUID NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    payee_name VARCHAR(255) NOT NULL,
    payee_account VARCHAR(100) NOT NULL,
    payee_bank VARCHAR(255) NOT NULL,
    amount DECIMAL(18, 2) NOT NULL,
    status VARCHAR(30) NOT NULL,
    paid_at TIMESTAMPTZ,
    transaction_id VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_payments IS '付款单';

CREATE TABLE tbl_suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    credit_code VARCHAR(100) UNIQUE,
    status SMALLINT NOT NULL DEFAULT 1,
    contact_info JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_suppliers IS '供应商信息库';

CREATE TABLE tbl_purchase_requisitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requisition_code VARCHAR(50) UNIQUE NOT NULL,
    applicant_id UUID NOT NULL,
    department_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    details JSONB,
    total_amount DECIMAL(18, 2),
    status VARCHAR(30) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_purchase_requisitions IS '采购需求单';

CREATE TABLE tbl_contracts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_code VARCHAR(50) UNIQUE NOT NULL,
    contract_name VARCHAR(255) NOT NULL,
    contract_type VARCHAR(50) NOT NULL,
    counterparty_id UUID,
    counterparty_name VARCHAR(255) NOT NULL,
    total_amount DECIMAL(18, 2) NOT NULL,
    start_date DATE,
    end_date DATE,
    status VARCHAR(30) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_contracts IS '合同主表';

CREATE TABLE tbl_contract_payment_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID NOT NULL,
    phase_name VARCHAR(100),
    due_date DATE,
    amount DECIMAL(18, 2) NOT NULL,
    status VARCHAR(30) NOT NULL,
    payment_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE tbl_contract_payment_schedules IS '合同分期付款计划';

CREATE TABLE tbl_asset_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_id UUID,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
COMMENT ON TABLE tbl_asset_categories IS '资产分类表';

CREATE TABLE tbl_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    asset_code VARCHAR(50) UNIQUE NOT NULL,
    asset_name VARCHAR(255) NOT NULL,
    category_id UUID NOT NULL,
    source_type VARCHAR(30),
    purchase_contract_id UUID,
    purchase_date DATE,
    purchase_price DECIMAL(18, 2),
    status VARCHAR(30) NOT NULL,
    owner_dept_id UUID NOT NULL,
    custodian_id UUID,
    location VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    created_by UUID NOT NULL,
    updated_by UUID
);
COMMENT ON TABLE tbl_assets IS '资产卡片表';

-- =============================================================================
-- 第二部分：添加所有外键约束和索引
-- =============================================================================
ALTER TABLE tbl_departments ADD CONSTRAINT fk_departments_parent FOREIGN KEY (parent_id) REFERENCES tbl_departments(id);
ALTER TABLE tbl_departments ADD CONSTRAINT fk_departments_manager FOREIGN KEY (manager_id) REFERENCES tbl_users(id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tbl_departments ADD CONSTRAINT fk_departments_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_users ADD CONSTRAINT fk_users_department FOREIGN KEY (department_id) REFERENCES tbl_departments(id);
ALTER TABLE tbl_users ADD CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_user_roles ADD CONSTRAINT fk_user_roles_user FOREIGN KEY (user_id) REFERENCES tbl_users(id) ON DELETE CASCADE;
ALTER TABLE tbl_user_roles ADD CONSTRAINT fk_user_roles_role FOREIGN KEY (role_id) REFERENCES tbl_roles(id) ON DELETE CASCADE;
ALTER TABLE tbl_files ADD CONSTRAINT fk_files_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
CREATE INDEX idx_files_business ON tbl_files (business_id, business_type);
ALTER TABLE tbl_approval_flows ADD CONSTRAINT fk_approval_flows_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
CREATE UNIQUE INDEX idx_flows_business ON tbl_approval_flows (business_id, business_type) WHERE deleted_at IS NULL;
ALTER TABLE tbl_approval_nodes ADD CONSTRAINT fk_approval_nodes_flow FOREIGN KEY (flow_id) REFERENCES tbl_approval_flows(id);
ALTER TABLE tbl_approval_nodes ADD CONSTRAINT fk_approval_nodes_approver FOREIGN KEY (approver_id) REFERENCES tbl_users(id);
CREATE INDEX idx_nodes_approver_status ON tbl_approval_nodes (approver_id, status);
ALTER TABLE tbl_budget_schemes ADD CONSTRAINT fk_budget_schemes_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_budget_subjects ADD CONSTRAINT fk_budget_subjects_parent FOREIGN KEY (parent_id) REFERENCES tbl_budget_subjects(id);
ALTER TABLE tbl_budget_subjects ADD CONSTRAINT fk_budget_subjects_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_budget_items ADD CONSTRAINT fk_budget_items_scheme FOREIGN KEY (scheme_id) REFERENCES tbl_budget_schemes(id);
ALTER TABLE tbl_budget_items ADD CONSTRAINT fk_budget_items_department FOREIGN KEY (department_id) REFERENCES tbl_departments(id);
ALTER TABLE tbl_budget_items ADD CONSTRAINT fk_budget_items_subject FOREIGN KEY (subject_id) REFERENCES tbl_budget_subjects(id);
ALTER TABLE tbl_budget_items ADD CONSTRAINT fk_budget_items_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_pre_applications ADD CONSTRAINT fk_pre_applications_applicant FOREIGN KEY (applicant_id) REFERENCES tbl_users(id);
ALTER TABLE tbl_pre_applications ADD CONSTRAINT fk_pre_applications_department FOREIGN KEY (department_id) REFERENCES tbl_departments(id);
ALTER TABLE tbl_pre_applications ADD CONSTRAINT fk_pre_applications_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_expense_applications ADD CONSTRAINT fk_expense_applications_applicant FOREIGN KEY (applicant_id) REFERENCES tbl_users(id);
ALTER TABLE tbl_expense_applications ADD CONSTRAINT fk_expense_applications_department FOREIGN KEY (department_id) REFERENCES tbl_departments(id);
ALTER TABLE tbl_expense_applications ADD CONSTRAINT fk_expense_applications_pre_app FOREIGN KEY (pre_application_id) REFERENCES tbl_pre_applications(id);
ALTER TABLE tbl_expense_applications ADD CONSTRAINT fk_expense_applications_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
CREATE INDEX idx_expense_applicant ON tbl_expense_applications (applicant_id);
ALTER TABLE tbl_expense_details ADD CONSTRAINT fk_expense_details_application FOREIGN KEY (application_id) REFERENCES tbl_expense_applications(id) ON DELETE CASCADE;
ALTER TABLE tbl_expense_details ADD CONSTRAINT fk_expense_details_budget_item FOREIGN KEY (budget_item_id) REFERENCES tbl_budget_items(id);
CREATE INDEX idx_expense_details_app_id ON tbl_expense_details (application_id);
ALTER TABLE tbl_payments ADD CONSTRAINT fk_payments_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
CREATE INDEX idx_payments_source ON tbl_payments (source_id, source_type);
ALTER TABLE tbl_suppliers ADD CONSTRAINT fk_suppliers_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_purchase_requisitions ADD CONSTRAINT fk_purchase_requisitions_applicant FOREIGN KEY (applicant_id) REFERENCES tbl_users(id);
ALTER TABLE tbl_purchase_requisitions ADD CONSTRAINT fk_purchase_requisitions_department FOREIGN KEY (department_id) REFERENCES tbl_departments(id);
ALTER TABLE tbl_purchase_requisitions ADD CONSTRAINT fk_purchase_requisitions_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_contracts ADD CONSTRAINT fk_contracts_counterparty FOREIGN KEY (counterparty_id) REFERENCES tbl_suppliers(id);
ALTER TABLE tbl_contracts ADD CONSTRAINT fk_contracts_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);
ALTER TABLE tbl_contract_payment_schedules ADD CONSTRAINT fk_schedules_contract FOREIGN KEY (contract_id) REFERENCES tbl_contracts(id);
ALTER TABLE tbl_contract_payment_schedules ADD CONSTRAINT fk_schedules_payment FOREIGN KEY (payment_id) REFERENCES tbl_payments(id);
CREATE INDEX idx_schedules_contract_id ON tbl_contract_payment_schedules (contract_id);
ALTER TABLE tbl_asset_categories ADD CONSTRAINT fk_asset_categories_parent FOREIGN KEY (parent_id) REFERENCES tbl_asset_categories(id);
ALTER TABLE tbl_assets ADD CONSTRAINT fk_assets_category FOREIGN KEY (category_id) REFERENCES tbl_asset_categories(id);
ALTER TABLE tbl_assets ADD CONSTRAINT fk_assets_contract FOREIGN KEY (purchase_contract_id) REFERENCES tbl_contracts(id);
ALTER TABLE tbl_assets ADD CONSTRAINT fk_assets_owner_dept FOREIGN KEY (owner_dept_id) REFERENCES tbl_departments(id);
ALTER TABLE tbl_assets ADD CONSTRAINT fk_assets_custodian FOREIGN KEY (custodian_id) REFERENCES tbl_users(id);
ALTER TABLE tbl_assets ADD CONSTRAINT fk_assets_created_by FOREIGN KEY (created_by) REFERENCES tbl_users(id);

-- =============================================================================
-- 第三部分：插入模拟数据 (已修复UUID生成)
-- =============================================================================

DO $$
DECLARE
    -- ★★★ FIX: 使用内置函数动态生成所有UUID ★★★
    dept_admin_id UUID := gen_random_uuid();
    dept_info_id UUID := gen_random_uuid();
    dept_finance_id UUID := gen_random_uuid();
    dept_cardio_id UUID := gen_random_uuid();
    
    user_sysadmin_id UUID := gen_random_uuid();
    user_zhangsan_id UUID := gen_random_uuid();
    user_lisi_id UUID := gen_random_uuid();
    user_wangwu_id UUID := gen_random_uuid();

    role_admin_id UUID := gen_random_uuid();
    role_employee_id UUID := gen_random_uuid();
    role_finance_approver_id UUID := gen_random_uuid();

    scheme_2025_id UUID := gen_random_uuid();
    subject_travel_id UUID := gen_random_uuid();
    budget_item_info_travel_id UUID := gen_random_uuid();

    expense_app_id UUID := gen_random_uuid();
    
BEGIN
    -- 1. 统一支撑平台数据
    INSERT INTO tbl_departments(id, name, code) VALUES
    (dept_admin_id, '院办', 'D_ADMIN'),
    (dept_info_id, '信息科', 'D_INFO'),
    (dept_finance_id, '财务科', 'D_FINANCE'),
    (dept_cardio_id, '心血管内科', 'D_CARDIO');

    INSERT INTO tbl_users(id, department_id, user_name, employee_id, email, password_hash, job_title) VALUES
    (user_sysadmin_id, dept_admin_id, '系统管理员', 'SYSADMIN', '<EMAIL>', '$2a$10$abcdefghijklmnopqrstuv', '管理员'),
    (user_zhangsan_id, dept_info_id, '张三', 'EMP001', '<EMAIL>', '$2a$10$abcdefghijklmnopqrstuv', '软件工程师'),
    (user_lisi_id, dept_finance_id, '李四', 'EMP002', '<EMAIL>', '$2a$10$abcdefghijklmnopqrstuv', '财务科长'),
    (user_wangwu_id, dept_cardio_id, '王五', 'EMP003', '<EMAIL>', '$2a$10$abcdefghijklmnopqrstuv', '主治医师');
    
    UPDATE tbl_departments SET manager_id = user_lisi_id WHERE id = dept_finance_id;
    UPDATE tbl_departments SET created_by = user_sysadmin_id;
    UPDATE tbl_users SET created_by = user_sysadmin_id;

    INSERT INTO tbl_roles(id, role_name, role_code, description, created_by) VALUES
    (role_admin_id, '系统管理员', 'SYS_ADMIN', '拥有系统所有权限', user_sysadmin_id),
    (role_employee_id, '普通员工', 'EMPLOYEE', '基础报销、申请权限', user_sysadmin_id),
    (role_finance_approver_id, '财务审批人', 'FINANCE_APPROVER', '负责财务相关单据的审批', user_sysadmin_id);

    INSERT INTO tbl_user_roles(user_id, role_id) VALUES
    (user_sysadmin_id, role_admin_id),
    (user_zhangsan_id, role_employee_id),
    (user_lisi_id, role_employee_id),
    (user_lisi_id, role_finance_approver_id),
    (user_wangwu_id, role_employee_id);

    -- 2. 预算管理数据
    INSERT INTO tbl_budget_schemes(id, name, year, status, created_by) VALUES
    (scheme_2025_id, '2025年度预算方案', 2025, 'ACTIVE', user_sysadmin_id);

    INSERT INTO tbl_budget_subjects(id, name, code, type, created_by) VALUES
    (subject_travel_id, '差旅费', '660101', 'EXPENDITURE', user_sysadmin_id);

    INSERT INTO tbl_budget_items(id, scheme_id, department_id, subject_id, total_amount, frozen_amount, created_by) VALUES
    (budget_item_info_travel_id, scheme_2025_id, dept_info_id, subject_travel_id, 50000.00, 205.50, user_lisi_id);

    -- 3. 支出控制数据
    INSERT INTO tbl_expense_applications(id, application_code, applicant_id, department_id, title, total_amount, status, created_by) VALUES
    (expense_app_id, 'BX202507230001', user_zhangsan_id, dept_info_id, '2025年7月市内交通费报销', 205.50, 'PENDING', user_zhangsan_id);

    INSERT INTO tbl_expense_details(application_id, budget_item_id, description, amount, expense_date) VALUES
    (expense_app_id, budget_item_info_travel_id, '从医院到市卫健委往返', 85.50, '2025-07-22'),
    (expense_app_id, budget_item_info_travel_id, '外出培训交通', 120.00, '2025-07-20');

    DECLARE
        flow_id UUID := gen_random_uuid();
    BEGIN
        INSERT INTO tbl_approval_flows(id, business_id, business_type, status, current_step, created_by) VALUES
        (flow_id, expense_app_id, 'EXPENSE_APP', 'PENDING', 1, user_zhangsan_id);

        INSERT INTO tbl_approval_nodes(flow_id, approver_id, status, step) VALUES
        (flow_id, user_lisi_id, 'PENDING', 1);
    END;

    -- 4. 供应商和合同数据
    DECLARE
        supplier_id UUID := gen_random_uuid();
        contract_id UUID := gen_random_uuid();
    BEGIN
        INSERT INTO tbl_suppliers(id, name, credit_code, status, created_by) VALUES
        (supplier_id, '某某医疗器械有限公司', '91310115MA1H88888A', 1, user_sysadmin_id);

        INSERT INTO tbl_contracts(id, contract_code, contract_name, contract_type, counterparty_id, counterparty_name, total_amount, status, created_by) VALUES
        (contract_id, 'HT2025001', '2025年度心电监护仪采购合同', '采购合同', supplier_id, '某某医疗器械有限公司', 150000.00, 'ACTIVE', user_lisi_id);

        INSERT INTO tbl_contract_payment_schedules(contract_id, phase_name, due_date, amount, status) VALUES
        (contract_id, '首付款', '2025-08-01', 75000.00, 'PENDING'),
        (contract_id, '验收款', '2025-09-15', 75000.00, 'PENDING');
    END;

END $$;

-- =============================================================================
-- 脚本结束
-- =============================================================================
