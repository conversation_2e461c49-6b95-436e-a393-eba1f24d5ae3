<template>
  <div class="user-management">
    <a-page-header title="用户管理" sub-title="用户的增、删、改、查、分配角色" />
    
    <a-card>
      <template #extra>
        <a-space>
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索用户名、工号、邮箱"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新增用户
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'roles'">
            <a-tag v-for="role in record.roles" :key="role.id" color="blue">
              {{ role.role_name }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="showEditModal(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="text" size="small" @click="showRoleModal(record)">
                <UserOutlined />
                分配角色
              </a-button>
              <a-popconfirm
                title="确定要删除这个用户吗？"
                @confirm="deleteUser(record.id)"
              >
                <a-button type="text" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑用户模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户姓名" name="user_name">
              <a-input v-model:value="formData.user_name" placeholder="请输入用户姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工号" name="employee_id">
              <a-input v-model:value="formData.employee_id" placeholder="请输入工号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所属部门" name="department_id">
              <a-tree-select
                v-model:value="formData.department_id"
                :tree-data="departmentOptions"
                :field-names="{ children: 'children', label: 'name', value: 'id' }"
                placeholder="请选择所属部门"
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="formData.status">
                <a-radio :value="1">启用</a-radio>
                <a-radio :value="0">禁用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item v-if="!isEdit" label="初始密码" name="password">
          <a-input-password v-model:value="formData.password" placeholder="请输入初始密码" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 分配角色模态框 -->
    <a-modal
      v-model:open="roleModalVisible"
      title="分配角色"
      @ok="handleRoleSubmit"
      @cancel="handleRoleCancel"
    >
      <a-form layout="vertical">
        <a-form-item label="用户信息">
          <div class="user-info">
            <a-avatar>{{ currentUser?.user_name?.charAt(0) }}</a-avatar>
            <div class="user-details">
              <div class="user-name">{{ currentUser?.user_name }}</div>
              <div class="user-meta">{{ currentUser?.employee_id }} | {{ currentUser?.department?.name }}</div>
            </div>
          </div>
        </a-form-item>
        
        <a-form-item label="选择角色">
          <a-checkbox-group v-model:value="selectedRoles">
            <a-row>
              <a-col v-for="role in roleOptions" :key="role.id" :span="24">
                <a-checkbox :value="role.id" style="margin-bottom: 8px">
                  <div class="role-item">
                    <div class="role-name">{{ role.role_name }}</div>
                    <div class="role-desc">{{ role.description }}</div>
                  </div>
                </a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import type { User, Department, Role } from '@/types'

const loading = ref(false)
const searchKeyword = ref('')
const tableData = ref<User[]>([])
const departmentOptions = ref<Department[]>([])
const roleOptions = ref<Role[]>([])
const modalVisible = ref(false)
const roleModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentUser = ref<User | null>(null)
const selectedRoles = ref<string[]>([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  { title: '用户姓名', dataIndex: 'user_name', key: 'user_name' },
  { title: '工号', dataIndex: 'employee_id', key: 'employee_id' },
  { title: '邮箱', dataIndex: 'email', key: 'email' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' },
  { title: '部门', dataIndex: ['department', 'name'], key: 'department' },
  { title: '角色', key: 'roles' },
  { title: '状态', key: 'status' },
  { title: '最后登录', dataIndex: 'last_login_at', key: 'last_login_at' },
  { title: '操作', key: 'action', width: 200 }
]

const formData = reactive({
  id: '',
  user_name: '',
  employee_id: '',
  email: '',
  phone: '',
  department_id: '',
  status: 1,
  password: ''
})

const formRules = {
  user_name: [{ required: true, message: '请输入用户姓名', trigger: 'blur' }],
  employee_id: [{ required: true, message: '请输入工号', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  department_id: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
  password: [{ required: true, message: '请输入初始密码', trigger: 'blur' }]
}

const loadUsers = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取用户列表
    // const response = await getUserList({
    //   page: pagination.current,
    //   pageSize: pagination.pageSize,
    //   keyword: searchKeyword.value
    // })
    // tableData.value = response.data.list
    // pagination.total = response.data.total
    
    // 临时数据
    tableData.value = [
      {
        id: '1',
        user_name: '张三',
        employee_id: 'EMP001',
        email: '<EMAIL>',
        phone: '13800138001',
        department_id: '2',
        status: 1,
        last_login_at: '2024-07-23 09:30:00',
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        department: { id: '2', name: '内科', code: 'NK' },
        roles: [
          { id: '1', role_name: '医生', role_code: 'DOCTOR', description: '医生角色' }
        ]
      },
      {
        id: '2',
        user_name: '李四',
        employee_id: 'EMP002',
        email: '<EMAIL>',
        phone: '13800138002',
        department_id: '3',
        status: 1,
        last_login_at: '2024-07-23 08:45:00',
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        department: { id: '3', name: '心内科', code: 'XNK' },
        roles: [
          { id: '1', role_name: '医生', role_code: 'DOCTOR', description: '医生角色' },
          { id: '2', role_name: '科室主任', role_code: 'DEPT_HEAD', description: '科室主任角色' }
        ]
      }
    ]
    pagination.total = 2
  } catch (error) {
    message.error('加载用户数据失败')
  } finally {
    loading.value = false
  }
}

const loadDepartments = async () => {
  try {
    // TODO: 调用API获取部门树形数据
    departmentOptions.value = [
      {
        id: '1',
        name: '医院总部',
        code: 'HQ',
        children: [
          { id: '2', name: '内科', code: 'NK' },
          { id: '3', name: '心内科', code: 'XNK' },
          { id: '4', name: '外科', code: 'WK' }
        ]
      }
    ]
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

const loadRoles = async () => {
  try {
    // TODO: 调用API获取角色列表
    roleOptions.value = [
      { id: '1', role_name: '医生', role_code: 'DOCTOR', description: '医生角色', status: 1 },
      { id: '2', role_name: '科室主任', role_code: 'DEPT_HEAD', description: '科室主任角色', status: 1 },
      { id: '3', role_name: '财务人员', role_code: 'FINANCE', description: '财务人员角色', status: 1 },
      { id: '4', role_name: '系统管理员', role_code: 'ADMIN', description: '系统管理员角色', status: 1 }
    ]
  } catch (error) {
    message.error('加载角色数据失败')
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadUsers()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadUsers()
}

const showAddModal = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formData, {
    id: '',
    user_name: '',
    employee_id: '',
    email: '',
    phone: '',
    department_id: '',
    status: 1,
    password: ''
  })
}

const showEditModal = (user: User) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, {
    id: user.id,
    user_name: user.user_name,
    employee_id: user.employee_id,
    email: user.email,
    phone: user.phone,
    department_id: user.department_id,
    status: user.status,
    password: ''
  })
}

const showRoleModal = (user: User) => {
  currentUser.value = user
  selectedRoles.value = user.roles?.map(role => role.id) || []
  roleModalVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      // TODO: 调用更新API
      message.success('用户更新成功')
    } else {
      // TODO: 调用创建API
      message.success('用户创建成功')
    }
    
    modalVisible.value = false
    loadUsers()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const handleRoleSubmit = async () => {
  try {
    // TODO: 调用分配角色API
    message.success('角色分配成功')
    roleModalVisible.value = false
    loadUsers()
  } catch (error) {
    message.error('角色分配失败')
  }
}

const handleRoleCancel = () => {
  roleModalVisible.value = false
  selectedRoles.value = []
}

const deleteUser = async (id: string) => {
  try {
    // TODO: 调用删除API
    message.success('用户删除成功')
    loadUsers()
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  loadUsers()
  loadDepartments()
  loadRoles()
})
</script>

<style scoped>
.user-management {
  padding: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.user-meta {
  font-size: 12px;
  color: #666;
}

.role-item {
  margin-left: 8px;
}

.role-name {
  font-weight: 500;
}

.role-desc {
  font-size: 12px;
  color: #666;
}
</style>
