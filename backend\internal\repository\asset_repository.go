package repository

import (
	"hospital-management/internal/model"

	"gorm.io/gorm"
)

type AssetRepository struct {
	db *gorm.DB
}

func NewAssetRepository(db *gorm.DB) *AssetRepository {
	return &AssetRepository{db: db}
}

// CreateAsset 创建资产
func (r *AssetRepository) CreateAsset(asset *model.Asset) error {
	return r.db.Create(asset).Error
}

// GetAssetByID 根据ID获取资产
func (r *AssetRepository) GetAssetByID(id string) (*model.Asset, error) {
	var asset model.Asset
	err := r.db.Preload("Category").Preload("Supplier").Preload("Contract").
		Preload("Receipt").Preload("Department").Preload("Location").
		Preload("Responsible").Where("id = ?", id).First(&asset).Error
	if err != nil {
		return nil, err
	}
	return &asset, nil
}

// GetAssetByCode 根据资产编号获取资产
func (r *AssetRepository) GetAssetByCode(code string) (*model.Asset, error) {
	var asset model.Asset
	err := r.db.Preload("Category").Preload("Supplier").Preload("Department").
		Preload("Location").Preload("Responsible").
		Where("asset_code = ?", code).First(&asset).Error
	if err != nil {
		return nil, err
	}
	return &asset, nil
}

// UpdateAsset 更新资产
func (r *AssetRepository) UpdateAsset(asset *model.Asset) error {
	return r.db.Save(asset).Error
}

// DeleteAsset 删除资产
func (r *AssetRepository) DeleteAsset(id string) error {
	return r.db.Where("id = ?", id).Delete(&model.Asset{}).Error
}

// GetAssets 获取资产列表
func (r *AssetRepository) GetAssets(offset, limit int, status, categoryID, departmentID, responsibleID string) ([]model.Asset, int64, error) {
	var assets []model.Asset
	var total int64

	query := r.db.Model(&model.Asset{}).
		Preload("Category").Preload("Department").Preload("Location").Preload("Responsible")

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 分类筛选
	if categoryID != "" {
		query = query.Where("category_id = ?", categoryID)
	}

	// 部门筛选
	if departmentID != "" {
		query = query.Where("department_id = ?", departmentID)
	}

	// 责任人筛选
	if responsibleID != "" {
		query = query.Where("responsible_id = ?", responsibleID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&assets).Error
	return assets, total, err
}

// GetAssetsByDepartment 根据部门获取资产列表
func (r *AssetRepository) GetAssetsByDepartment(departmentID string) ([]model.Asset, error) {
	var assets []model.Asset
	err := r.db.Preload("Category").Where("department_id = ?", departmentID).Find(&assets).Error
	return assets, err
}

// GetAssetsByCategory 根据分类获取资产列表
func (r *AssetRepository) GetAssetsByCategory(categoryID string) ([]model.Asset, error) {
	var assets []model.Asset
	err := r.db.Preload("Department").Where("category_id = ?", categoryID).Find(&assets).Error
	return assets, err
}

// UpdateAssetStatus 更新资产状态
func (r *AssetRepository) UpdateAssetStatus(id, status string) error {
	return r.db.Model(&model.Asset{}).Where("id = ?", id).Update("status", status).Error
}

// GetAssetStatistics 获取资产统计数据
func (r *AssetRepository) GetAssetStatistics() (map[string]interface{}, error) {
	var result map[string]interface{} = make(map[string]interface{})

	// 总资产数
	var totalCount int64
	r.db.Model(&model.Asset{}).Count(&totalCount)
	result["total_count"] = totalCount

	// 各状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	r.db.Model(&model.Asset{}).Select("status, COUNT(*) as count").Group("status").Find(&statusStats)
	result["status_stats"] = statusStats

	// 各分类统计
	var categoryStats []struct {
		CategoryName string `json:"category_name"`
		Count        int64  `json:"count"`
	}
	r.db.Model(&model.Asset{}).
		Select("ac.category_name, COUNT(*) as count").
		Joins("LEFT JOIN tbl_asset_categories ac ON tbl_assets.category_id = ac.id").
		Group("ac.category_name").Find(&categoryStats)
	result["category_stats"] = categoryStats

	// 总资产价值
	var totalValue float64
	r.db.Model(&model.Asset{}).Select("COALESCE(SUM(purchase_price), 0)").Scan(&totalValue)
	result["total_value"] = totalValue

	return result, nil
}

// CreateAssetCategory 创建资产分类
func (r *AssetRepository) CreateAssetCategory(category *model.AssetCategory) error {
	return r.db.Create(category).Error
}

// GetAssetCategories 获取资产分类列表
func (r *AssetRepository) GetAssetCategories() ([]model.AssetCategory, error) {
	var categories []model.AssetCategory
	err := r.db.Preload("Parent").Preload("Children").Order("level ASC, sort_order ASC").Find(&categories).Error
	return categories, err
}

// CreateAssetLocation 创建资产位置
func (r *AssetRepository) CreateAssetLocation(location *model.AssetLocation) error {
	return r.db.Create(location).Error
}

// GetAssetLocations 获取资产位置列表
func (r *AssetRepository) GetAssetLocations() ([]model.AssetLocation, error) {
	var locations []model.AssetLocation
	err := r.db.Preload("Parent").Preload("Children").Find(&locations).Error
	return locations, err
}

// CreateAssetMovement 创建资产变动记录
func (r *AssetRepository) CreateAssetMovement(movement *model.AssetMovement) error {
	return r.db.Create(movement).Error
}

// GetAssetMovements 获取资产变动记录
func (r *AssetRepository) GetAssetMovements(assetID string, offset, limit int) ([]model.AssetMovement, int64, error) {
	var movements []model.AssetMovement
	var total int64

	query := r.db.Model(&model.AssetMovement{}).
		Preload("FromUser").Preload("ToUser").Preload("FromDept").Preload("ToDept").
		Preload("FromLocation").Preload("ToLocation")

	if assetID != "" {
		query = query.Where("asset_id = ?", assetID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("movement_date DESC").Find(&movements).Error
	return movements, total, err
}

// CreateAssetMaintenance 创建资产维保记录
func (r *AssetRepository) CreateAssetMaintenance(maintenance *model.AssetMaintenance) error {
	return r.db.Create(maintenance).Error
}

// GetAssetMaintenances 获取资产维保记录
func (r *AssetRepository) GetAssetMaintenances(assetID string, offset, limit int) ([]model.AssetMaintenance, int64, error) {
	var maintenances []model.AssetMaintenance
	var total int64

	query := r.db.Model(&model.AssetMaintenance{}).
		Preload("Supplier").Preload("Responsible")

	if assetID != "" {
		query = query.Where("asset_id = ?", assetID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("maintenance_date DESC").Find(&maintenances).Error
	return maintenances, total, err
}

// GetUpcomingMaintenances 获取即将到期的维保
func (r *AssetRepository) GetUpcomingMaintenances(days int) ([]model.AssetMaintenance, error) {
	var maintenances []model.AssetMaintenance
	err := r.db.Preload("Asset").Preload("Responsible").
		Where("next_date IS NOT NULL AND next_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL ? DAY", days).
		Order("next_date ASC").Find(&maintenances).Error
	return maintenances, err
}

// CreateAssetInventory 创建资产盘点
func (r *AssetRepository) CreateAssetInventory(inventory *model.AssetInventory) error {
	return r.db.Create(inventory).Error
}

// GetAssetInventories 获取资产盘点列表
func (r *AssetRepository) GetAssetInventories(offset, limit int, status string) ([]model.AssetInventory, int64, error) {
	var inventories []model.AssetInventory
	var total int64

	query := r.db.Model(&model.AssetInventory{}).
		Preload("Department").Preload("Category")

	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&inventories).Error
	return inventories, total, err
}

// CreateAssetInventoryItem 创建资产盘点明细
func (r *AssetRepository) CreateAssetInventoryItem(item *model.AssetInventoryItem) error {
	return r.db.Create(item).Error
}

// GetAssetInventoryItems 获取资产盘点明细
func (r *AssetRepository) GetAssetInventoryItems(inventoryID string) ([]model.AssetInventoryItem, error) {
	var items []model.AssetInventoryItem
	err := r.db.Preload("Asset").Preload("Checker").
		Where("inventory_id = ?", inventoryID).Find(&items).Error
	return items, err
}

// BatchCreateAssetInventoryItems 批量创建盘点明细
func (r *AssetRepository) BatchCreateAssetInventoryItems(items []model.AssetInventoryItem) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		for _, item := range items {
			if err := tx.Create(&item).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// UpdateAssetInventoryItem 更新盘点明细
func (r *AssetRepository) UpdateAssetInventoryItem(item *model.AssetInventoryItem) error {
	return r.db.Save(item).Error
}

// GetExpiringWarranties 获取即将到期的保修
func (r *AssetRepository) GetExpiringWarranties(days int) ([]model.Asset, error) {
	var assets []model.Asset
	err := r.db.Preload("Category").Preload("Department").Preload("Responsible").
		Where("warranty_expiry IS NOT NULL AND warranty_expiry BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL ? DAY", days).
		Order("warranty_expiry ASC").Find(&assets).Error
	return assets, err
}
