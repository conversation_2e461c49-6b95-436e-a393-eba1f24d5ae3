package api

import (
	"hospital-management/internal/model"
	"hospital-management/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type BudgetHandler struct {
	budgetService *service.BudgetService
}

func NewBudgetHandler(budgetService *service.BudgetService) *BudgetHandler {
	return &BudgetHandler{budgetService: budgetService}
}

// List 获取预算列表
func (h *BudgetHandler) List(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("pageSize", "10"))
	year, _ := strconv.Atoi(c.Query("year"))
	departmentID := c.Query("departmentId")

	budgets, total, err := h.budgetService.GetBudgets(page, pageSize, year, departmentID)
	if err != nil {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "查询失败",
			"data":    nil,
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "查询成功",
		"data": gin.H{
			"total": total,
			"list":  budgets,
		},
	})
}

// Create 创建预算
func (h *BudgetHandler) Create(c *gin.Context) {
	var budget model.Budget
	if err := c.ShouldBindJSON(&budget); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	userID := c.GetString("user_id")
	createdBy, _ := uuid.Parse(userID)
	budget.CreatedBy = &createdBy

	if err := h.budgetService.CreateBudget(&budget); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "创建预算失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "创建成功",
		"data":    budget,
	})
}

// GetByID 根据ID获取预算
func (h *BudgetHandler) GetByID(c *gin.Context) {
	id := c.Param("id")
	if _, err := uuid.Parse(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	budget, err := h.budgetService.GetBudgetByID(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40004,
			"message": "预算不存在",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "获取成功",
		"data":    budget,
	})
}

// Update 更新预算
func (h *BudgetHandler) Update(c *gin.Context) {
	id := c.Param("id")
	if _, err := uuid.Parse(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	var budget model.Budget
	if err := c.ShouldBindJSON(&budget); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	budgetID, _ := uuid.Parse(id)
	budget.ID = budgetID

	userID := c.GetString("user_id")
	updatedBy, _ := uuid.Parse(userID)
	budget.UpdatedBy = &updatedBy

	if err := h.budgetService.UpdateBudget(&budget); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "更新预算失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "更新成功",
		"data":    budget,
	})
}

// Delete 删除预算
func (h *BudgetHandler) Delete(c *gin.Context) {
	id := c.Param("id")
	if _, err := uuid.Parse(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "ID格式错误",
			"data":    nil,
		})
		return
	}

	if err := h.budgetService.DeleteBudget(id); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "删除预算失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "删除成功",
		"data":    nil,
	})
}

// GetItems 获取预算项目列表
func (h *BudgetHandler) GetItems(c *gin.Context) {
	budgetID := c.Param("id")
	if _, err := uuid.Parse(budgetID); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "预算ID格式错误",
			"data":    nil,
		})
		return
	}

	items, err := h.budgetService.GetBudgetItems(budgetID)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "查询失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "查询成功",
		"data":    items,
	})
}

// CreateItem 创建预算项目
func (h *BudgetHandler) CreateItem(c *gin.Context) {
	budgetID := c.Param("id")
	if _, err := uuid.Parse(budgetID); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "预算ID格式错误",
			"data":    nil,
		})
		return
	}

	var item model.BudgetItem
	if err := c.ShouldBindJSON(&item); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    40000,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	item.BudgetID, _ = uuid.Parse(budgetID)
	userID := c.GetString("user_id")
	createdBy, _ := uuid.Parse(userID)
	item.CreatedBy = &createdBy

	if err := h.budgetService.CreateBudgetItem(&item); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"code":    50000,
			"message": "创建预算项目失败",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    20000,
		"message": "创建成功",
		"data":    item,
	})
}
