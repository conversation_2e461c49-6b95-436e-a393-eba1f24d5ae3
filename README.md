# 医院内部控制与运营管理系统

## 项目简介

医院内部控制与运营管理（质控）系统是一个专为医院设计的综合性管理平台，旨在提升医院的运营效率、质量控制和风险管理能力。

## 技术栈

### 前端
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **图表库**: ECharts

### 后端
- **语言**: Go 1.21+
- **框架**: Gin
- **数据库**: MySQL 8.0+
- **ORM**: GORM
- **认证**: JWT
- **配置管理**: Viper

## 项目结构

```
zhikong/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── api/             # API接口
│   │   ├── components/      # 公共组件
│   │   ├── views/           # 页面组件
│   │   ├── router/          # 路由配置
│   │   ├── stores/          # 状态管理
│   │   ├── types/           # TypeScript类型定义
│   │   └── utils/           # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── backend/                  # 后端项目
│   ├── cmd/                 # 应用入口
│   ├── internal/            # 内部包
│   │   ├── api/             # API处理器
│   │   ├── service/         # 业务逻辑层
│   │   ├── repository/      # 数据访问层
│   │   ├── model/           # 数据模型
│   │   ├── middleware/      # 中间件
│   │   ├── config/          # 配置管理
│   │   └── utils/           # 工具函数
│   ├── configs/             # 配置文件
│   ├── docs/                # 文档
│   ├── scripts/             # 脚本文件
│   ├── go.mod
│   └── .env
└── docs/                    # 项目文档
```

## 功能模块

### 1. 用户管理
- 用户注册、登录、权限管理
- 部门管理、角色管理
- 用户信息维护

### 2. 质量控制
- 质量指标管理
- 质量数据采集与分析
- 质量报告生成

### 3. 绩效管理
- 绩效指标设定
- 绩效数据统计
- 绩效评估与分析

### 4. 风险管理
- 风险识别与评估
- 风险事件管理
- 风险控制措施

### 5. 合规管理
- 合规检查管理
- 合规报告生成
- 合规监督

### 6. 报告中心
- 各类报告模板
- 报告生成与导出
- 报告分发管理

## 快速开始

### 环境要求
- Node.js 18+
- Go 1.21+
- MySQL 8.0+

### 前端启动

```bash
cd frontend
npm install
npm run dev
```

### 后端启动

```bash
cd backend
# 复制环境配置文件
cp .env.example .env
# 修改数据库配置
# 安装依赖
go mod tidy
# 启动服务
go run cmd/main.go
```

### 数据库初始化

1. 创建数据库：
```sql
CREATE DATABASE hospital_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 系统启动时会自动创建表结构

## 开发指南

### 前端开发
- 使用Vue 3 Composition API
- 遵循TypeScript最佳实践
- 使用Element Plus组件库
- API调用统一使用axios实例

### 后端开发
- 遵循Clean Architecture架构
- 使用依赖注入模式
- 统一错误处理和响应格式
- 使用GORM进行数据库操作

## 部署说明

### 前端部署
```bash
npm run build
# 将dist目录部署到Web服务器
```

### 后端部署
```bash
go build -o hospital-management cmd/main.go
# 配置环境变量
# 启动服务
./hospital-management
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。
