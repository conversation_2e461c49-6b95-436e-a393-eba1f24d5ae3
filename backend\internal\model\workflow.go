package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ApprovalFlow 审批流实例表 (tbl_approval_flows)
type ApprovalFlow struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	BusinessID   uuid.UUID `json:"business_id" gorm:"type:uuid;not null;comment:关联的业务单据ID"`
	BusinessType string    `json:"business_type" gorm:"size:50;not null;comment:业务类型 EXPENSE/CONTRACT/PURCHASE"`
	Status       string    `json:"status" gorm:"size:30;not null;comment:审批流状态 PENDING/APPROVED/REJECTED"`
	CurrentStep  int       `json:"current_step" gorm:"comment:当前走到的步骤序号"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy    *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy    *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Nodes []ApprovalNode `json:"nodes" gorm:"foreignKey:FlowID"`
}

// ApprovalNode 审批节点记录表 (tbl_approval_nodes)
type ApprovalNode struct {
	ID          uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	FlowID      uuid.UUID  `json:"flow_id" gorm:"type:uuid;not null;comment:所属审批流实例ID"`
	ApproverID  uuid.UUID  `json:"approver_id" gorm:"type:uuid;not null;comment:审批人ID"`
	Status      string     `json:"status" gorm:"size:30;not null;comment:节点状态 PENDING/APPROVED/REJECTED/TRANSFERRED"`
	Comment     string     `json:"comment" gorm:"type:text;comment:审批意见"`
	Step        int        `json:"step" gorm:"comment:节点所在步骤序号"`
	ProcessedAt *time.Time `json:"processed_at" gorm:"comment:处理时间"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid;comment:创建人"`
	UpdatedBy   *uuid.UUID `json:"updated_by" gorm:"type:uuid;comment:更新人"`

	// 关联
	Flow     ApprovalFlow `json:"flow" gorm:"foreignKey:FlowID"`
	Approver User         `json:"approver" gorm:"foreignKey:ApproverID"`
}
